import { apiBaseUrl } from '../utils/constant.js';

// API相关操作的store
export const createApiStore = (set, get) => ({
  // API相关状态
  apis: [],
  defaultApiModel: ['', ''],
  apisInitialized: false, // 新增状态：标记API列表是否已初始化

  // API相关 setters
  setApis: (apis) => set({ apis }),
  setDefaultApiModel: (provider, model) => set({ defaultApiModel: [provider, model] }),
  setApisInitialized: (initialized) => set({ apisInitialized: initialized }), // 新增 action

  // API 相关操作
  fetchApis: async (signal) => {
    // console.log('[APIStore DEBUG] fetchApis called. Current apisInitialized:', get().apisInitialized);
    if (get().apisInitialized) {
      // console.log('[APIStore DEBUG] APIs already initialized, returning existing data. Count:', get().apis.length);
      return get().apis;
    }

    let success = false;
    // console.log('[APIStore DEBUG] Proceeding to fetch APIs from server.');
    try {
      const response = await fetch(`${apiBaseUrl}api/apis`, {
        method: 'GET',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        signal
      });

      // console.log('[APIStore DEBUG] Fetch response status:', response.status);
      if (response.ok) {
        const data = await response.json();
        // console.log('[APIStore DEBUG] Received raw data from /api/apis:', JSON.stringify(data, null, 2));
        let formattedApis = data
          .map(api => ({
            key: api.uuid,
            provider: api.provider || '',
            apiUrl: api.apiUrl || '',
            apiKey: api.apiKey || '',
            models: api.models || [],
            order: api.order || 0
          }))
          .sort((a, b) => a.order - b.order);
        // console.log('[APIStore DEBUG] Formatted APIs:', JSON.stringify(formattedApis, null, 2));
        // console.log('[APIStore DEBUG] Current apis in store BEFORE set:', JSON.stringify(get().apis, null, 2));
        set({ apis: formattedApis });
        // console.log('[APIStore DEBUG] Current apis in store AFTER set:', JSON.stringify(get().apis, null, 2));
        success = true;
        return formattedApis;
      } else {
        // console.error('[APIStore DEBUG] Fetch /api/apis failed. Status:', response.status, 'StatusText:', response.statusText);
        // const errorBody = await response.text();
        // console.error('[APIStore DEBUG] Fetch /api/apis error body:', errorBody);
        console.error('Fetch /api/apis failed. Status:', response.status, 'StatusText:', response.statusText); // Keep a minimal error log
      }
      return [];
    } catch (error) {
      if (error.name === 'AbortError') {
        // console.log('[APIStore DEBUG] Fetch Apis aborted');
        console.log('Fetch Apis aborted'); // Keep a minimal log for abort
      } else {
        // console.error('[APIStore DEBUG] Error fetching Apis:', error);
        console.error('Error fetching Apis:', error); // Keep a minimal error log
      }
      return [];
    } finally {
      if (signal && !signal.aborted) {
        // console.log('[APIStore DEBUG] fetchApis finished (not aborted). Setting apisInitialized to true. Success was:', success);
        set({ apisInitialized: true });
      } else if (!signal) {
        // console.log('[APIStore DEBUG] fetchApis finished (no signal). Setting apisInitialized to true. Success was:', success);
        set( { apisInitialized: true });
      } else {
        // console.log('[APIStore DEBUG] fetchApis was aborted. apisInitialized remains:', get().apisInitialized);
      }
      // console.log('[APIStore DEBUG] Final apisInitialized state:', get().apisInitialized);
      // console.log('[APIStore DEBUG] Final apis count in store:', get().apis.length);
    }
  },

  saveApi: async (apiData) => {
    const { apis } = get();

    const backendFormat = {
      provider: apiData.provider || '',
      apiUrl: apiData.apiUrl || '',
      apiKey: apiData.apiKey || '',
      models: Array.isArray(apiData.models) ? apiData.models : []
    };

    try {
      const response = await fetch(`${apiBaseUrl}api/apis`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(backendFormat)
      });

      if (response.ok) {
        const savedApi = await response.json();
        const newApi = {
          key: savedApi.uuid,
          provider: apiData.provider,
          apiUrl: apiData.apiUrl,
          apiKey: apiData.apiKey,
          models: apiData.models || []
        };

        set({ apis: [...apis, newApi] });
        return newApi;
      }
      return null;
    } catch (error) {
      return null;
    }
  },

  updateApi: async (apiUuid, apiData) => {
    const { apis } = get();

    const backendFormat = {
      provider: apiData.provider || '',
      apiUrl: apiData.apiUrl || '',
      apiKey: apiData.apiKey || '',
      models: apiData.models || []
    };

    try {
      const response = await fetch(`${apiBaseUrl}api/apis/${apiUuid}`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(backendFormat)
      });

      if (response.ok) {
        const updatedApis = apis.map(api =>
          api.key === apiUuid ? { ...api, ...apiData } : api
        );
        set({ apis: updatedApis });
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  },

  deleteApi: async (apiUuid) => {
    const { apis } = get();

    try {
      const response = await fetch(`${apiBaseUrl}api/apis/${apiUuid}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const responseData = await response.json().catch(() => ({}));

      if (response.ok && responseData.success) {
        const updatedApis = apis.filter(api => api.key !== apiUuid);
        set({ apis: updatedApis });
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  },

  getSelectedApiConfig: (currentApis, currentDefaultApiModel) => {
    // Helper function to determine selected API config and model name
    // This function can be called with specific apis and defaultApiModel,
    // or it can use get() if integrated deeper into the store action context.
    // For now, it accepts them as arguments, matching the call signature in translation.js
    const apis = currentApis || get().apis; // Fallback to store's apis if not provided
    const defaultApiModel = currentDefaultApiModel || get().defaultApiModel; // Fallback

    let provider, model, selectedApiConfig;

    if (defaultApiModel && defaultApiModel[0] && defaultApiModel[1]) {
      [provider, model] = defaultApiModel;
      if (provider === '系统默认') {
        selectedApiConfig = {
          key: 'system-default',
          provider: '系统默认',
          apiUrl: '/api/v1/chat/completions', // This should ideally be configurable or a constant
          apiKey: '',
          models: ['GLM-4-9B-0414'], // Example, ensure this matches actual available system default models
        };
        // Ensure the passed model is valid for system default, or fallback
        if (!selectedApiConfig.models.includes(model)) {
          console.warn(`[APIStore getSelectedApiConfig] 系统默认模型 "${model}" 无效, 回退到 "${selectedApiConfig.models[0]}"`);
          model = selectedApiConfig.models[0];
        }
      } else {
        selectedApiConfig = apis.find(api => api.provider === provider);
      }
    } else {
      // Fallback if defaultApiModel is not set properly or at all
      console.warn('[APIStore getSelectedApiConfig] defaultApiModel 未正确设置或未设置，回退到系统默认。');
      provider = '系统默认';
      model = 'GLM-4-9B-0414'; // Example fallback model
      selectedApiConfig = {
        key: 'system-default',
        provider: '系统默认',
        apiUrl: '/api/v1/chat/completions',
        apiKey: '',
        models: ['GLM-4-9B-0414'],
      };
      // Attempt to set the default in the store if it was missing
      // This might be too aggressive here, consider if this side-effect is desired.
      // get().setDefaultApiModel(provider, model);
    }

    if (!selectedApiConfig) {
      console.error(`[APIStore getSelectedApiConfig] 未找到提供商 "${provider}" 的有效 API 配置。`);
      // Attempt to fallback to the first available API if any, or system default as a last resort
      if (apis && apis.length > 0) {
        console.warn(`[APIStore getSelectedApiConfig] 回退到第一个可用的API配置: ${apis[0].provider}`);
        selectedApiConfig = apis[0];
        model = selectedApiConfig.models[0] || null; // Use its first model
      } else {
         console.error(`[APIStore getSelectedApiConfig] 没有可用的API配置，也无法回退。`);
        return { selectedApiConfig: null, modelName: model }; // Critical failure
      }
    }
    
    // Validate the model against the selected API config's models
    if (!selectedApiConfig.models || !selectedApiConfig.models.includes(model)) {
      console.warn(`[APIStore getSelectedApiConfig] 提供商 "${selectedApiConfig.provider}" (原请求模型: "${defaultApiModel ? defaultApiModel[1] : 'N/A'}") 不支持模型 "${model}" 或模型列表未定义。将尝试使用该提供商的第一个可用模型。`);
      if (selectedApiConfig.models && selectedApiConfig.models.length > 0) {
        model = selectedApiConfig.models[0];
        console.warn(`[APIStore getSelectedApiConfig] 已回退到模型: "${model}"`);
      } else {
        console.error(`[APIStore getSelectedApiConfig] 提供商 "${selectedApiConfig.provider}" 没有可用的模型。`);
        return { selectedApiConfig: selectedApiConfig, modelName: null }; // Return the API but no valid model
      }
    }
    return { selectedApiConfig, modelName: model };
  }
});
