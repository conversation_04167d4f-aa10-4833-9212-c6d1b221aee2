import { apiBaseUrl } from '../utils/constant.js';
import {
  defaultTranslationPromptText,
  defaultTitleGenerationPromptText,
  defaultSummaryGenerationPromptText,
  defaultParseTextPromptText,
  defaultReferenceParsePromptText
} from '../utils/constant.js';

// 重新导出默认提示词文本供其他文件使用
export {
  defaultTranslationPromptText,
  defaultTitleGenerationPromptText,
  defaultSummaryGenerationPromptText,
  defaultParseTextPromptText,
  defaultReferenceParsePromptText
};

// 提示词相关操作的store
export const createPromptStore = (set, get) => ({
  // 提示词相关状态
  translationPrompt: '',
  titleGenerationPrompt: '',
  summaryGenerationPrompt: '', // 新增AI总结提示词状态
  parseTextPrompt: '', // 新增解析文本提示词状态
  referenceParsePrompt: '', // 新增参考文献解析提示词状态
  glossary: '', // 新增术语库状态

  // 提示词相关操作
  setTranslationPrompt: (prompt) => set({ translationPrompt: prompt }),
  setTitleGenerationPrompt: (prompt) => set({ titleGenerationPrompt: prompt }),
  setSummaryGenerationPrompt: (prompt) => set({ summaryGenerationPrompt: prompt }), // 新增AI总结提示词 setter
  setParseTextPrompt: (prompt) => set({ parseTextPrompt: prompt }), // 新增解析文本提示词 setter
  setReferenceParsePrompt: (prompt) => set({ referenceParsePrompt: prompt }), // 新增参考文献解析提示词 setter
  setGlossary: (glossary) => set({ glossary }), // 新增术语库 setter

  // 辅助函数：将术语库附加到提示词后面
  appendGlossaryToPrompt: (basePrompt, promptType = 'translation') => {
    const { glossary } = get();
    if (!glossary || glossary.trim() === '') {
      return basePrompt;
    }

    // 根据提示词类型决定是否附加术语库
    const shouldAppendGlossary = ['translation', 'titleGeneration', 'summaryGeneration'].includes(promptType);

    if (shouldAppendGlossary) {
      return `${basePrompt}\n\n术语库：\n${glossary}`;
    }

    return basePrompt;
  },

  // 保存所有提示词到服务器
  savePrompts: async (translationPromptParam, titleGenerationPromptParam, summaryGenerationPromptParam, parseTextPromptParam, referenceParsePromptParam, glossaryParam) => {
    // 如果传入了参数，使用参数值；否则从 store 状态中获取
    const translationPrompt = translationPromptParam !== undefined ? translationPromptParam : get().translationPrompt;
    const titleGenerationPrompt = titleGenerationPromptParam !== undefined ? titleGenerationPromptParam : get().titleGenerationPrompt;
    const summaryGenerationPrompt = summaryGenerationPromptParam !== undefined ? summaryGenerationPromptParam : get().summaryGenerationPrompt;
    const parseTextPrompt = parseTextPromptParam !== undefined ? parseTextPromptParam : get().parseTextPrompt;
    const referenceParsePrompt = referenceParsePromptParam !== undefined ? referenceParsePromptParam : get().referenceParsePrompt;
    const glossary = glossaryParam !== undefined ? glossaryParam : get().glossary;

    try {
      const response = await fetch(`${apiBaseUrl}api/user/prompts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          translationPrompt,
          titleGenerationPrompt,
          summaryGenerationPrompt, // 包含AI总结提示词
          parseTextPrompt, // 包含解析文本提示词
          referenceParsePrompt, // 包含参考文献解析提示词
          glossary // 包含术语库
        }),
      });

      if (!response.ok) {
        throw new Error('保存提示词失败');
      }

      // 保存成功后，更新 store 中的状态
      set({
        translationPrompt,
        titleGenerationPrompt,
        summaryGenerationPrompt,
        parseTextPrompt,
        referenceParsePrompt,
        glossary
      });

      return { success: true };
    } catch (error) {
      console.error('保存提示词时出错:', error);
      return { success: false, error: error.message };
    }
  },
});
