import { create } from 'zustand';

// 从各个功能模块导入
// import { createBaseStore } from './base.js'; // Removed base store import
import { createApiStore } from './api.js';
import { createContentStore } from './content.js';
import { createTranslationStore } from './translation.js';
import { createAuthStore } from './user.js'; // Changed path from auth.js to user.js
import { createPromptStore } from './prompt.js';
import {
  defaultTranslationPromptText,
  defaultTitleGenerationPromptText,
  defaultSummaryGenerationPromptText,
  defaultParseTextPromptText,
  defaultReferenceParsePromptText
} from '../utils/constant.js';

// 导出默认提示词文本供外部组件使用
export {
  defaultTranslationPromptText,
  defaultTitleGenerationPromptText,
  defaultSummaryGenerationPromptText,
  defaultParseTextPromptText,
  defaultReferenceParsePromptText
};

// 创建并导出统一的store
export const useStore = create((set, get) => ({
  // 从各个模块合并功能
  // ...createBaseStore(set, get), // Removed base store spread
  ...createApiStore(set, get),
  ...createContentStore(set, get), // Contains doi, uuid, content, translated, etc.
  ...createTranslationStore(set, get), // Contains translation logic
  ...createAuthStore(set, get), // Contains user state, logout, saveChunkSize, etc. (Renamed from createAuthStore internally but export name kept for now)
  ...createPromptStore(set, get), // Contains prompt state

  // PDF 导入状态
  isImportingPdf: false,
  setIsImportingPdf: (isLoading) => set({ isImportingPdf: isLoading }),
  importProgressMessage: '',
  setImportProgressMessage: (message) => set({ importProgressMessage: message }),

  // 跳过AI解析状态
  shouldSkipAiParsing: false,
  setShouldSkipAiParsing: (shouldSkip) => set({ shouldSkipAiParsing: shouldSkip }),

  // PDF Import Retry Logic
  canRetryFinalizePdf: false,
  setCanRetryFinalizePdf: (canRetry) => set({ canRetryFinalizePdf: canRetry }),
  pdfFinalizeErrorDetails: null,
  setPdfFinalizeErrorDetails: (details) => set({ pdfFinalizeErrorDetails: details }),
  _actualRetryFn: null, // Holds the reference to the retry function from usePdfImport.js

  triggerPdfImportRetry: () => {
    const { _actualRetryFn, canRetryFinalizePdf } = get();
    if (canRetryFinalizePdf && typeof _actualRetryFn === 'function') {
      console.log("Store action: Triggering PDF import retry via registered function.");
      _actualRetryFn();
    } else {
      console.warn("Store action: PDF import retry cannot be triggered. No retry function registered or not in a retryable state.", { canRetryFinalizePdf, hasRetryFn: typeof _actualRetryFn === 'function' });
    }
  },

  currentImportController: null,
  setCurrentImportController: (controller) => set({ currentImportController: controller }),
  clearCurrentImportController: () => set({
    currentImportController: null,
    importProgressMessage: '',
    canRetryFinalizePdf: false, // Reset retry state
    pdfFinalizeErrorDetails: null, // Reset error details
    shouldSkipAiParsing: false // Reset skip AI parsing state
  }),

  // Action to cancel/abort import
  // cancelPdfImport is already used in App.jsx, let's ensure it works as expected
  // and add abortPdfImportProcess if it needs to be distinct or called from usePdfImport
  cancelPdfImport: () => { // This is used by the existing UI
    const controller = get().currentImportController;
    if (controller) {
      controller.abort();
      set({ importProgressMessage: '导入已取消。', isImportingPdf: false, canRetryFinalizePdf: false });
    }
  },

  abortPdfImportProcess: () => { // New action as per instructions
    const controller = get().currentImportController;
    if (controller) {
      controller.abort();
      // set({ importProgressMessage: '导入进程已中止。', isImportingPdf: false, canRetryFinalizePdf: false });
      // Decided to let the abort signal handler in usePdfImport handle message updates for consistency
    }
  },
}));
