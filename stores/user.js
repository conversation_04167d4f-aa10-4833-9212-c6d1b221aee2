import { apiBaseUrl } from '../utils/constant.js'; // Import apiBaseUrl
import _ from 'lodash'; // Import lodash for deep comparison

// 用户状态和认证相关操作的store
export const createAuthStore = (set, get) => ({
  // 用户状态
  user: undefined,
  userArticles: [], // 新增：用户文章列表
  isLoadingUserArticles: false, // 新增：加载用户文章列表的状态
  userArticlesFetched: false, // 新增：标记是否已经获取过用户文章列表

  // 用户 setters - with deep comparison to prevent unnecessary updates
  setUser: (newUser) => {
    const currentUser = get().user;
    // Only call set if the new user data is actually different
    if (!_.isEqual(currentUser, newUser)) {
      set({ user: newUser });

      // 当用户信息更新时，同步更新 promptStore 中的提示词状态
      if (newUser) {
        // 从 newUser 对象中获取提示词。
        // /api/auth/me 返回的 user 对象 (经过 model.js 的 toJSON()) 包含了展开的 config 属性，
        // 所以 translationPrompt 和 titleGenerationPrompt 应该在 newUser 的顶层。
        if (newUser.translationPrompt !== undefined) {
          set({ translationPrompt: newUser.translationPrompt });
        }
        if (newUser.titleGenerationPrompt !== undefined) {
          set({ titleGenerationPrompt: newUser.titleGenerationPrompt });
        }
        if (newUser.summaryGenerationPrompt !== undefined) {
          set({ summaryGenerationPrompt: newUser.summaryGenerationPrompt });
        }
        // 如果 parseTextPrompt 也由用户配置管理，并由 /api/auth/me 返回，则也应在此处更新
        if (newUser.parseTextPrompt !== undefined) {
          set({ parseTextPrompt: newUser.parseTextPrompt });
        }
        // 如果 referenceParsePrompt 也由用户配置管理，并由 /api/auth/me 返回，则也应在此处更新
        if (newUser.referenceParsePrompt !== undefined) {
          set({ referenceParsePrompt: newUser.referenceParsePrompt });
        }
        // 如果 glossary 也由用户配置管理，并由 /api/auth/me 返回，则也应在此处更新
        if (newUser.glossary !== undefined) {
          set({ glossary: newUser.glossary });
        }
      } else {
        // 如果 newUser 为 null (例如用户登出)，则可能需要将提示词重置为默认值。
        // 引入 default prompts
        // const { defaultTranslationPromptText, defaultTitleGenerationPromptText, defaultParseTextPromptText } = get(); // 假设这些在顶层store
        // 或者直接从 './stores/prompt.js' 导入，但从 get() 获取更符合 zustand 模式
        // 注意：如果这些默认值没有通过 get() 暴露，需要调整。
        // 一个更简单的方法是，如果 promptStore 有一个 reset action，则调用它。
        // 为简单起见，如果它们在 get() 中不可用，我们将依赖 promptStore 自己的初始默认值，
        // 并且假设应用在登出后会进行某种形式的完全状态重置或重新加载。
        // 但为了明确，如果需要重置，应该这样做：
        // set({
        //   translationPrompt: defaultTranslationPromptText,
        //   titleGenerationPrompt: defaultTitleGenerationPromptText,
        //   parseTextPrompt: defaultParseTextPromptText
        // });
        // 鉴于 createPromptStore 已经设置了初始默认值，当 user 为 null 时，
        // 依赖组件从 promptStore 读取这些默认值即可，此处可能无需显式重置，
        // 除非有特定逻辑要求在 setUser(null) 时强制重置。
        // 目前保持不在此处重置，依赖 promptStore 的初始状态。
      }
    }
  },

  // 认证相关操作
  logout: () => {
    window.location.href = '/api/auth/logout';
  },

  // 保存用户分块大小设置
  saveChunkSize: async (newSize) => {
    const { user } = get();
    if (!user) {
      throw new Error("无法保存分块大小：用户未登录");
    }

    try {
      const response = await fetch(`${apiBaseUrl}api/user/chunkSize`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ chunkSize: newSize })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`保存分块大小失败: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        // 更新本地 user 状态
        set({ user: { ...user, chunkSize: newSize } });
        return newSize;
      } else {
        throw new Error(result.error || '保存分块大小设置时发生未知错误');
      }
    } catch (error) {
      throw error;
    }
  },

  // 合并的启动数据获取 - 一次请求获取用户信息、文章列表、API配置和可选的文章内容
  fetchBootstrapData: async (articleUuid = null, signal = null) => {
    const { user, isLoadingUserArticles, userArticlesFetched } = get();

    if (signal && signal.aborted) {
      set({ isLoadingUserArticles: false });
      return null;
    }

    // 防止重复请求：如果正在加载或已经获取过，则跳过
    if (isLoadingUserArticles || userArticlesFetched) {
      console.log('[UserStore] fetchBootstrapData: Skipping duplicate request - already loading or fetched');
      return null;
    }

    set({ isLoadingUserArticles: true });

    try {
      const url = new URL(`${apiBaseUrl}api/bootstrap`);
      if (articleUuid) {
        url.searchParams.set('articleUuid', articleUuid);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        credentials: 'include',
        signal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: '获取启动数据失败' }));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // 更新所有相关状态
      const newState = {
        user: data.user,
        userArticles: data.articles || [],
        isLoadingUserArticles: false,
        userArticlesFetched: true
      };

      // 如果有文章数据，也更新内容状态
      if (data.article) {
        newState.content = data.article.content || [];
        newState.translated = data.article.translated || {};
        newState.uuid = data.article.uuid;
        newState.article = data.article;
      }

      set(newState);

      // 如果有API数据，更新API store状态
      if (data.apis) {
        const formattedApis = data.apis.map(api => ({
          key: api.uuid,
          provider: api.provider || '',
          apiUrl: api.apiUrl || '',
          apiKey: api.apiKey || '',
          models: api.models || [],
          order: api.order || 0
        })).sort((a, b) => a.order - b.order);

        // 获取API store的方法来更新状态
        const { setApis, setApisInitialized, setDefaultApiModel, setSystemPrompt, setUserPrompt } = get();
        setApis(formattedApis);
        setApisInitialized(true);

        // 如果有用户数据，设置默认API模型和提示词
        if (data.user) {
          if (data.user.defaultApiModel && Array.isArray(data.user.defaultApiModel) && data.user.defaultApiModel.length === 2) {
            setDefaultApiModel(data.user.defaultApiModel[0], data.user.defaultApiModel[1]);
          }
          if (data.user.systemPrompt !== undefined) setSystemPrompt(data.user.systemPrompt || '');
          if (data.user.userPrompt !== undefined) setUserPrompt(data.user.userPrompt || '');
        }
      } else if (data.user) {
        // 如果没有API数据但有用户数据，仍然设置用户相关的状态
        const { setDefaultApiModel, setSystemPrompt, setUserPrompt } = get();
        if (data.user.defaultApiModel && Array.isArray(data.user.defaultApiModel) && data.user.defaultApiModel.length === 2) {
          setDefaultApiModel(data.user.defaultApiModel[0], data.user.defaultApiModel[1]);
        }
        if (data.user.systemPrompt !== undefined) setSystemPrompt(data.user.systemPrompt || '');
        if (data.user.userPrompt !== undefined) setUserPrompt(data.user.userPrompt || '');
      }

      return data;
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('fetchBootstrapData: Fetch aborted.');
      } else {
        console.error("fetchBootstrapData: Error during fetch or processing:", error.message);
      }
      set({
        user: null,
        userArticles: [],
        isLoadingUserArticles: false,
        userArticlesFetched: false
      });
      throw error;
    }
  },

  // 获取用户文章列表 (保留原有方法以兼容)
  fetchUserArticles: async (signal, forceRefresh = false) => { // Accept AbortSignal and forceRefresh flag
    // console.log('[UserStore] fetchUserArticles action started.');
    const { user, isLoadingUserArticles, userArticlesFetched } = get(); // Get current user state

    if (signal && signal.aborted) {
      // console.log('[UserStore] fetchUserArticles: Aborted before starting.');
      set({ isLoadingUserArticles: false });
      return;
    }

    // 防止重复请求：如果正在加载，则跳过；如果已经获取过且不是强制刷新，则跳过
    if (isLoadingUserArticles || (userArticlesFetched && !forceRefresh)) {
      console.log('[UserStore] fetchUserArticles: Skipping duplicate request - already loading or fetched (forceRefresh:', forceRefresh, ')');
      return;
    }

    // console.log('[UserStore] fetchUserArticles: Setting isLoadingUserArticles to true.');
    set({ isLoadingUserArticles: true });

    const apiUrl = `${apiBaseUrl}api/articles/my`;
    // console.log('[UserStore] fetchUserArticles: Fetching from URL:', apiUrl);

    try {
      const response = await fetch(apiUrl, {
        method: 'GET',
        credentials: 'include',
        signal // Pass signal to fetch
      });
      // console.log('[UserStore] fetchUserArticles: API response status:', response.status);
      const responseDataText = await response.text();

      if (!response.ok) {
        // console.error(`[UserStore] fetchUserArticles: API request failed. Status: ${response.status}, Response Text: ${responseDataText}`);
        let errorDetail = `获取文章列表失败: ${response.statusText}`;
        try {
            const errorDataJson = JSON.parse(responseDataText);
            errorDetail = errorDataJson.message || errorDetail;
        } catch (e) {
            errorDetail = responseDataText || errorDetail;
        }
        console.error(`fetchUserArticles: API request failed. Status: ${response.status}, Detail: ${errorDetail}`); // Keep minimal error
        throw new Error(errorDetail);
      }

      let data;
      try {
        data = JSON.parse(responseDataText);
        // console.log('[UserStore] fetchUserArticles: Parsed API response data:', data);
      } catch (e) {
        console.error('fetchUserArticles: Failed to parse API response JSON. Error:', e); // Keep minimal error
        throw new Error('获取文章列表失败：响应数据格式错误');
      }

      const articles = data.articles || [];
      const newState = { userArticles: articles, isLoadingUserArticles: false, userArticlesFetched: true };
      // console.log('[UserStore] fetchUserArticles: Preparing to set new state:', newState);
      set(newState);
      // console.log('[UserStore] fetchUserArticles: Successfully set userArticles and isLoadingUserArticles. Action ended.');
    } catch (error) {
      if (error.name === 'AbortError') {
        // console.log('[UserStore] fetchUserArticles: Fetch aborted.');
        console.log('fetchUserArticles: Fetch aborted.'); // Keep minimal log for abort
      } else {
        // console.error("[UserStore] fetchUserArticles: Error during fetch or processing:", error.message, error);
        console.error("fetchUserArticles: Error during fetch or processing:", error.message); // Keep minimal error
      }
      const errorState = { userArticles: [], isLoadingUserArticles: false };
      // console.log('[UserStore] fetchUserArticles: Setting state due to error/abort:', errorState);
      set(errorState);
      // console.log('[UserStore] fetchUserArticles: Action ended due to error/abort.');
    }
  },

  // 删除用户文章
  deleteUserArticle: async (articleId) => {
    if (!articleId) {
      throw new Error("删除文章失败：缺少文章 ID");
    }
    try {
      const response = await fetch(`${apiBaseUrl}api/articles/${articleId}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: '删除文章操作失败' }));
        throw new Error(errorData.message || `删除文章失败: ${response.statusText}`);
      }
      // 从本地 store 中移除文章
      set(state => ({
        userArticles: state.userArticles.filter(article => article.uuid !== articleId)
      }));
      return { success: true, articleId };
    } catch (error) {
      console.error("Error deleting article:", error);
      throw error; // Re-throw to be caught by the component
    }
  },

  // 新建空白文章
  createNewBlankArticle: async () => {
    try {
      const response = await fetch(`${apiBaseUrl}api/articles/new`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        // body: JSON.stringify({}), // 不需要 body
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: '新建空白文章操作失败' }));
        throw new Error(errorData.message || `新建空白文章失败: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success && result.newArticleUuid) {
        // 创建成功后，直接在本地状态中添加新文章，而不是重新获取整个列表
        const newArticle = {
          uuid: result.newArticleUuid,
          title: result.title,
          createdAt: new Date().toISOString(), // 添加创建时间以便正确排序
          updatedAt: new Date().toISOString()
        };

        // 将新文章添加到列表开头（最新的文章在前面）
        set(state => ({
          userArticles: [newArticle, ...state.userArticles]
        }));

        return {
          newArticleUuid: result.newArticleUuid,
          title: result.title,
          contentUuid: result.contentUuid
        };
      } else {
        throw new Error(result.message || '新建空白文章后返回数据格式不正确');
      }
    } catch (error) {
      console.error("Error creating new blank article:", error);
      throw error; // Re-throw to be caught by the component
    }
  },
});
