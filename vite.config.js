import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import AutoImport from 'unplugin-auto-import/vite';
import monkey, { cdn, util } from 'vite-plugin-monkey';
import path from 'path';
import fs from 'fs';
import url from 'url';
import { tsImport } from 'tsx/esm/api';
const {reactPort,apiPort,monkeyPort}=await tsImport('./utils/constant.js', import.meta.url)


// 获取目录模块的函数
export async function getDirModules(dir) {
    let modules = [];

    const moduleDir = path.join(import.meta.dirname, dir);
    const filenames = fs.readdirSync(moduleDir)
        .filter(filename => filename.endsWith('.js') || filename.endsWith('.jsx')); // 只读取 .js 或 .jsx 文件
    modules = await Promise.all(filenames.map(filename => {
        const modulePath = url.pathToFileURL(path.join(moduleDir, filename)).href;
        console.log(modulePath);
        return tsImport(modulePath, import.meta.url);
    }));

    return modules;
}

// 根据命令行参数或环境变量确定模式
export default defineConfig(async ({ mode }) => {
    // 判断是否为油猴脚本模式
    const isMonkey = mode === 'monkey';

    // 如果是油猴模式，加载解析器和入口
    let parsers = [];
    let entries = [];

    if (isMonkey) {
        parsers = await getDirModules('./parsers');
        entries = await getDirModules('./entries');
    }

    // 基础配置
    const baseConfig = {
        esbuild: {
            jsxFactory: 'React.createElement',
            jsxFragment: 'React.Fragment',
            // loader: 'jsx',
        },
        assetsInclude: ['**/*.html','**/*.md']
    };

    // React 模式配置
    if (!isMonkey) {
        return {
            ...baseConfig,

            plugins: [
                react({
                    include: /\.(jsx|js)$/
                }),
            ],
            server: {
                port: reactPort,
                strictPort: true,
                host: true,
                hmr:true,
                timeout:120000,
                proxy: {
                    '/api': {
                        target: `http://localhost:${apiPort}`,
                        changeOrigin: true,
                    },
                },
            },
            optimizeDeps:{
                include: ['unpdf'],
                force:true,
                esbuildOptions: {
                    target: 'esnext'
                }
            },
            resolve: {
                alias: {
                    'react': path.resolve('./node_modules/react'),
                    'react-dom': path.resolve('./node_modules/react-dom')
                }
            },
            // 构建配置
            build: {
                target: 'esnext'
            }
        };
    }

    // 油猴模式配置
    return {
        ...baseConfig,
        server: {
            port: monkeyPort,
            open: false,
        },
        plugins: [
            AutoImport({
                imports: [util.unimportPreset],
            }),
            react(),
            monkey({
                entry: 'userscript.jsx',
                userscript: {
                    name: 'TSG Userscript',
                    namespace: 'https://github.com/yourusername',
                    version: '0.1.0',
                    description: 'TSG',
                    match: [
                        'https://tsgv3.vercel.app/*',
                        'http://localhost:3000/*',
                        'https://mp.weixin.qq.com/*',
                        ...parsers.map(module => module.match || module.matchUrls || []).flat(),
                        ...entries.map(module => module.match || module.matchUrls || []).flat(),
                    ],
                    include: [
                        ...parsers.map(module => module.include || []).flat(),
                        ...entries.map(module => module.include || []).flat(),
                    ],
                    connect: [
                        ...[...entries, ...parsers].map(module =>
                            module.connect || module.connectUrls || []
                        ).flat(),
                    ],
                    grant: 'auto',
                    icon: 'https://vitejs.dev/logo.svg',
                },
                build: {
                    fileName: 'tsg-userscript.user.js',
                    externalGlobals: {
                        react: cdn => `https://unpkg.com/react@18.2.0/umd/react.production.min.js`,
                        'react-dom': cdn => `https://unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js`,
                        jquery: cdn.jsdelivr("jQuery"),
                    },
                },
            }),
        ],
    };
});