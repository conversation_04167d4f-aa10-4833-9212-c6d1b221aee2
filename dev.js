#!/usr/bin/env node

import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { tsImport } from 'tsx/esm/api';

// 导入端口配置
const { reactPort, apiPort, monkeyPort } = await tsImport('./utils/constant.js', import.meta.url);

const execAsync = promisify(exec);

// 颜色输出工具
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

function colorLog(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 杀死占用指定端口的进程
async function killPortProcess(port) {
    try {
        const { stdout } = await execAsync(`lsof -ti:${port}`);
        const pids = stdout.trim().split('\n').filter(pid => pid);
        
        if (pids.length > 0) {
            colorLog('yellow', `🔍 发现端口 ${port} 被进程占用: ${pids.join(', ')}`);
            
            for (const pid of pids) {
                try {
                    await execAsync(`kill -9 ${pid}`);
                    colorLog('green', `✅ 已杀死进程 ${pid} (端口 ${port})`);
                } catch (killError) {
                    colorLog('red', `⚠️ 无法杀死进程 ${pid}: ${killError.message}`);
                }
            }
        }
    } catch (error) {
        // 端口未被占用是正常情况，不需要输出错误
        if (error.code !== 1) {
            colorLog('blue', `ℹ️ 端口 ${port} 检查完成`);
        }
    }
}

// 清理 Vite 缓存
function cleanViteCache() {
    const viteCacheDir = path.join(process.cwd(), 'node_modules', '.vite');
    if (fs.existsSync(viteCacheDir)) {
        try {
            fs.rmSync(viteCacheDir, { recursive: true, force: true });
            colorLog('green', '✅ 已清理 Vite 缓存目录: node_modules/.vite');
        } catch (error) {
            colorLog('red', `⚠️ 清理 Vite 缓存失败: ${error.message}`);
        }
    } else {
        colorLog('blue', 'ℹ️ Vite 缓存目录不存在，跳过清理');
    }
}

// 根据模式清理对应端口和缓存
async function cleanPortsByMode(mode) {
    colorLog('cyan', '🧹 开始清理端口...');

    switch (mode) {
        case 'react':
            colorLog('blue', `🧹 清理 React 端口 ${reactPort}...`);
            await killPortProcess(reactPort);
            colorLog('blue', '🧹 清理 Vite 缓存 (React 模式)...');
            cleanViteCache();
            break;
        case 'server':
            colorLog('blue', `🧹 清理 API 服务器端口 ${apiPort}...`);
            await killPortProcess(apiPort);
            // Server 模式不需要清理 Vite 缓存
            break;
        case 'userscript':
            colorLog('blue', `🧹 清理油猴脚本端口 ${monkeyPort}...`);
            await killPortProcess(monkeyPort);
            colorLog('blue', '🧹 清理 Vite 缓存 (Userscript 模式)...');
            cleanViteCache();
            break;
        default: // 'all' 模式
            colorLog('blue', `🧹 清理所有端口 ${reactPort}, ${apiPort}, ${monkeyPort}...`);
            await Promise.all([
                killPortProcess(reactPort),
                killPortProcess(apiPort),
                killPortProcess(monkeyPort)
            ]);
            colorLog('blue', '🧹 清理 Vite 缓存 (All 模式)...');
            cleanViteCache();
    }

    colorLog('cyan', '🧹 端口和缓存清理完成');
}

// 启动开发服务器
function startDevServers(mode = 'all') {
    let command, args;
    
    switch (mode) {
        case 'react':
            colorLog('magenta', '🚀 启动 React 开发服务器...');
            command = 'pnpm';
            args = ['exec', 'vite', '--mode', 'react'];
            break;
        case 'server':
            colorLog('magenta', '🚀 启动 API 服务器...');
            command = 'node';
            args = ['--watch', 'server.js'];
            break;
        case 'userscript':
            colorLog('magenta', '🚀 启动油猴脚本服务器...');
            command = 'pnpm';
            args = ['exec', 'vite', '--mode', 'monkey'];
            break;
        default:
            colorLog('magenta', '🚀 启动所有开发服务器...');
            command = 'pnpm';
            args = ['exec', 'concurrently', 
                '"pnpm exec vite --mode react"',
                '"node --watch server.js"',
                '"pnpm exec vite --mode monkey"'
            ];
    }
    
    // 使用 spawn 启动服务，这样可以保持颜色输出
    const child = spawn(command, args, {
        stdio: 'inherit',
        shell: true
    });

    // 处理进程退出
    child.on('close', (code) => {
        if (code !== 0) {
            colorLog('red', `❌ 开发服务器退出，退出码: ${code}`);
            process.exit(code);
        }
    });

    // 处理 Ctrl+C 等信号
    process.on('SIGINT', () => {
        colorLog('yellow', '\n🛑 收到中断信号，正在关闭服务器...');
        child.kill('SIGINT');
    });

    process.on('SIGTERM', () => {
        colorLog('yellow', '\n🛑 收到终止信号，正在关闭服务器...');
        child.kill('SIGTERM');
    });
}

// 显示帮助信息
function showHelp() {
    console.log(`
🎯 TSG 开发环境调度器

用法:
  node dev.js [--mode <模式>]

选项:
  --help, -h          显示帮助信息
  --mode <模式>       指定启动模式

启动模式:
  all                 启动所有服务器 (默认)
  react               仅启动 React 开发服务器 (端口 ${reactPort})
  server              仅启动 API 服务器 (端口 ${apiPort})
  userscript          仅启动油猴脚本服务器 (端口 ${monkeyPort})

示例:
  node dev.js                    # 启动所有服务器
  node dev.js --mode react       # 仅启动 React 服务器
  node dev.js --mode server      # 仅启动 API 服务器
  node dev.js --mode userscript  # 仅启动油猴脚本服务器
`);
}

// 解析命令行参数
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {
        mode: 'all',
        help: false
    };

    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        switch (arg) {
            case '--help':
            case '-h':
                options.help = true;
                break;
            case '--mode':
                if (i + 1 < args.length) {
                    options.mode = args[++i];
                }
                break;
        }
    }

    return options;
}

// 主函数
async function main() {
    const options = parseArgs();

    if (options.help) {
        showHelp();
        return;
    }

    try {
        colorLog('blue', '🎯 TSG 开发环境调度器启动');
        colorLog('blue', `📍 端口配置: React(${reactPort}), API(${apiPort}), Monkey(${monkeyPort})`);

        // 1. 根据模式清理对应端口和缓存
        await cleanPortsByMode(options.mode);

        // 2. 启动开发服务器
        startDevServers(options.mode);

    } catch (error) {
        colorLog('red', `❌ 启动失败: ${error.message}`);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行主函数
if (process.argv[1] === new URL(import.meta.url).pathname) {
    main();
}

export { cleanPortsByMode, cleanViteCache, killPortProcess };
