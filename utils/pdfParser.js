// 从远程 CDN 动态导入 pdfjs-dist
const PDFJS_CDN_URL = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@5.2.133/build/pdf.mjs';
const PDFJS_WORKER_URL = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@5.2.133/build/pdf.worker.mjs';

let pdfjsLib = null;

// 动态加载 pdfjs-dist 从 CDN
async function loadPDFJS() {
  if (pdfjsLib) {
    return pdfjsLib; // 已经加载过了
  }

  try {
    console.log('[pdfParser] Loading PDF.js from CDN:', PDFJS_CDN_URL);
    pdfjsLib = await import(/* @vite-ignore */ PDFJS_CDN_URL);

    // 配置 worker
    if (typeof window !== 'undefined') {
      pdfjsLib.GlobalWorkerOptions.workerSrc = PDFJS_WORKER_URL;
      console.log('[pdfParser] PDF.js worker configured:', PDFJS_WORKER_URL);
    } else {
      console.warn('[pdfParser] Non-browser environment detected, worker configuration may need adjustment.');
    }

    console.log('[pdfParser] PDF.js loaded successfully from CDN');
    return pdfjsLib;
  } catch (error) {
    console.error('[pdfParser] Failed to load PDF.js from CDN:', error);
    throw new Error(`Failed to load PDF.js from CDN: ${error.message}`);
  }
}

// 创建自己的 getDocumentProxy 函数，使用远程 PDF.js
async function getDocumentProxy(pdfData) {
  try {
    const pdfjs = await loadPDFJS();
    const loadingTask = pdfjs.getDocument({ data: pdfData });
    const pdf = await loadingTask.promise;
    return pdf;
  } catch (error) {
    console.error('[pdfParser] Error creating document proxy:', error);
    throw error;
  }
}

// 创建自己的 extractText 函数
async function extractText(pdf, options = {}) {
  const { mergePages = false } = options;
  const pageTexts = [];

  for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
    try {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();
      const pageText = textContent.items
        .filter(item => item.str && item.str.trim())
        .map(item => item.str)
        .join(' ');
      pageTexts.push(pageText);
    } catch (error) {
      console.error(`[pdfParser] Error extracting text from page ${pageNum}:`, error);
      pageTexts.push(''); // 添加空字符串以保持页面顺序
    }
  }

  return { text: pageTexts };
}

/**
 * Converts a Uint8Array to a Base64 Data URI.
 * @param {Uint8Array} uint8Array The Uint8Array to convert.
 * @param {string} mimeType The MIME type of the data.
 * @returns {string} The Base64 Data URI.
 */
export function uint8ArrayToBase64DataURI(uint8Array, mimeType) {
  let binary = '';
  const len = uint8Array.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(uint8Array[i]);
  }
  const base64 = typeof window !== 'undefined' ? window.btoa(binary) : Buffer.from(binary).toString('base64');
  return `data:${mimeType};base64,${base64}`;
}

/**
 * Extracts text content from a PDF.
 * @param {ArrayBuffer|Uint8Array} pdfDataInput - The PDF data as an ArrayBuffer or Uint8Array.
 * @returns {Promise<string>} A promise that resolves with the extracted text.
 */
export async function getPdfText(pdfDataInput) {
  let pdfDataUint8Array;
  if (pdfDataInput instanceof ArrayBuffer) {
    pdfDataUint8Array = new Uint8Array(pdfDataInput);
  } else if (pdfDataInput instanceof Uint8Array) {
    pdfDataUint8Array = pdfDataInput;
  } else {
    const errorMsg = `[pdfParser-getText] Invalid input type. Expected ArrayBuffer or Uint8Array. Received: ${typeof pdfDataInput}`;
    console.error(errorMsg);
    throw new Error('Invalid input type for getPdfText. Expected ArrayBuffer or Uint8Array.');
  }

  try {
    const pdf = await getDocumentProxy(pdfDataUint8Array);
    const { text: pageTextsArray } = await extractText(pdf, { mergePages: false });
    let rawTextFromPdf = pageTextsArray.join("\n\n");
    // Normalize newlines and trim
    rawTextFromPdf = rawTextFromPdf.replace(/[ \t]+\n/g, '\n').replace(/\n[ \t]+/g, '\n').replace(/\n{3,}/g, '\n\n').trim();
    return rawTextFromPdf;
  } catch (error) {
    console.error('[pdfParser-getText] Error extracting text from PDF:', error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

/**
 * Extracts text content from a PDF with page-level information.
 * @param {ArrayBuffer|Uint8Array} pdfDataInput - The PDF data as an ArrayBuffer or Uint8Array.
 * @returns {Promise<Array<{pageNum: number, text: string}>>} A promise that resolves with an array of page text objects.
 */
export async function getPdfTextByPages(pdfDataInput) {
  let pdfDataUint8Array;
  if (pdfDataInput instanceof ArrayBuffer) {
    pdfDataUint8Array = new Uint8Array(pdfDataInput);
  } else if (pdfDataInput instanceof Uint8Array) {
    pdfDataUint8Array = pdfDataInput;
  } else {
    const errorMsg = `[pdfParser-getTextByPages] Invalid input type. Expected ArrayBuffer or Uint8Array. Received: ${typeof pdfDataInput}`;
    console.error(errorMsg);
    throw new Error('Invalid input type for getPdfTextByPages. Expected ArrayBuffer or Uint8Array.');
  }

  try {
    const pdf = await getDocumentProxy(pdfDataUint8Array);
    const { text: pageTextsArray } = await extractText(pdf, { mergePages: false });

    return pageTextsArray.map((pageText, index) => ({
      pageNum: index + 1,
      text: pageText.replace(/[ \t]+\n/g, '\n').replace(/\n[ \t]+/g, '\n').replace(/\n{3,}/g, '\n\n').trim()
    }));
  } catch (error) {
    console.error('[pdfParser-getTextByPages] Error extracting text from PDF:', error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

/**
 * Extracts images from a PDF and converts them to Data URIs.
 * @param {ArrayBuffer|Uint8Array} pdfDataInput - The PDF data as an ArrayBuffer or Uint8Array.
 * @returns {Promise<Array<{src: string, alt: string, width?: number, height?: number}>>} A promise that resolves with an array of image objects.
 */
export async function extractPdfImages(pdfDataInput) {
  let pdfDataUint8Array;
  if (pdfDataInput instanceof ArrayBuffer) {
    pdfDataUint8Array = new Uint8Array(pdfDataInput);
  } else if (pdfDataInput instanceof Uint8Array) {
    pdfDataUint8Array = pdfDataInput;
  } else {
    const errorMsg = `[pdfParser-extractImages] Invalid input type. Expected ArrayBuffer or Uint8Array. Received: ${typeof pdfDataInput}`;
    console.error(errorMsg);
    throw new Error('Invalid input type for extractPdfImages. Expected ArrayBuffer or Uint8Array.');
  }

  const embeddedImageObjects = [];
  console.log('[pdfParser-extractImages] Entered extractPdfImages.');
  try {
    // 加载远程 PDF.js
    const pdfjs = await loadPDFJS();

    // Ensure we are working with a copy for getDocumentProxy
    const pdf = await getDocumentProxy(new Uint8Array(pdfDataUint8Array)); // pdf is the PDFDocumentProxy
    console.log('[pdfParser-extractImages] PDF document proxy loaded for image extraction. Total pages:', pdf.numPages);

    // Helper for logging large data arrays (can be kept or removed if not essential for util)
    // const logReplacer = (key, value) => {
    //   if (key === 'data' && value && typeof value.length === 'number' && value.length > 100) {
    //     return `Uint8Array(length=${value.length}, firstBytes=${value.slice(0,10)})`;
    //   }
    //   if (value && typeof value === 'object' && !Array.isArray(value)) {
    //     const newObj = {};
    //     for (const prop of Object.getOwnPropertyNames(value)) {
    //         newObj[prop] = value[prop];
    //     }
    //     return newObj;
    //   }
    //   return value;
    // };

    let imageCandidates = [];

    // 直接使用 pdfjs-dist 的 operator list 方法提取图片
    console.log('[pdfParser-extractImages] Using pdfjs-dist operator list method to extract images.');
    const fallbackImages = [];
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        console.log(`[pdfParser-extractImages-fallback] Processing page ${pageNum} for images.`);
        const page = await pdf.getPage(pageNum);
        const operatorList = await page.getOperatorList({ intent: 'display' });
        console.log(`[pdfParser-extractImages-fallback] Page ${pageNum}: Operator list obtained. fnArray length: ${operatorList.fnArray.length}`);
        
        let paintImageOpsCount = 0;
        const imagesOnPage = [];

        for (let i = 0; i < operatorList.fnArray.length; i++) {
          const fn = operatorList.fnArray[i];
          const args = operatorList.argsArray[i];

          if (fn === pdfjs.OPS.paintImageXObject) {
            paintImageOpsCount++;
            const imgKey = args[0];
            console.log(`[pdfParser-extractImages-fallback] Page ${pageNum}: Found OPS.paintImageXObject. imgKey: ${imgKey}.`);
            
            let imgData = null;
            const MAX_RETRIES = 2;
            const RETRY_DELAY_MS = 250;
            let retrievalMethod = '';

            console.log(`[pdfParser-extractImages-fallback] Page ${pageNum}, Key ${imgKey}: Attempting with page.commonObjs.get()`);
            for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
                try {
                    if (attempt > 0) {
                        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
                    }
                    imgData = await page.commonObjs.get(imgKey);
                    if (imgData && imgData.data && imgData.data.length > 0) {
                        retrievalMethod = 'commonObjs.get';
                        break;
                    } else if (imgData) { // imgData exists but data is invalid
                        imgData = null; 
                    }
                } catch (objError) {
                    if (attempt === MAX_RETRIES || !objError.message?.includes("Requesting object that isn't resolved yet")) {
                        imgData = null; break;
                    }
                    console.warn(`[pdfParser-extractImages-fallback] Page ${pageNum}, Key ${imgKey}, commonObjs.get() attempt ${attempt} error: ${objError.message}. Retrying...`);
                }
            }

            if (!imgData) {
              console.log(`[pdfParser-extractImages-fallback] Page ${pageNum}, Key ${imgKey}: commonObjs.get() failed or returned invalid data. Attempting with page.objs.get()`);
              if (page.objs && typeof page.objs.get === 'function') {
                for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
                    try {
                        if (attempt > 0) {
                            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
                        }
                        imgData = await page.objs.get(imgKey);
                        if (imgData && imgData.data && imgData.data.length > 0) {
                            retrievalMethod = 'objs.get';
                            break;
                        } else if (imgData) {
                            if (!imgData.data && imgData.ref) {
                                console.log(`[pdfParser-debug] Page ${pageNum}, Key ${imgKey}: imgData.data is null. Ref: '${imgData.ref}'. Attempting alternatives.`);
                                let fetchedData = null;
                                let currentAltMethod = 'none';

                                if (typeof imgData.getBytes === 'function') {
                                    currentAltMethod = 'imgData.getBytes()';
                                    try {
                                        const potentialBytes = await imgData.getBytes(imgData.dataLen > 0 ? imgData.dataLen : undefined);
                                        if (potentialBytes instanceof Uint8Array && potentialBytes.length > 0) fetchedData = potentialBytes;
                                    } catch (e) { console.error(`[pdfParser-debug] Error ASYNC ${currentAltMethod}:`, e.message); }
                                }
                                if (!fetchedData && imgData.stream && typeof imgData.stream.getBytes === 'function') {
                                    currentAltMethod = 'imgData.stream.getBytes()';
                                    try {
                                        const potentialBytes = await imgData.stream.getBytes(imgData.dataLen > 0 ? imgData.dataLen : undefined);
                                        if (potentialBytes instanceof Uint8Array && potentialBytes.length > 0) fetchedData = potentialBytes;
                                    } catch (e) { console.error(`[pdfParser-debug] Error ASYNC ${currentAltMethod}:`, e.message); }
                                }
                                if (!fetchedData && typeof imgData.readBlock === 'function') {
                                    currentAltMethod = 'imgData.readBlock()';
                                    try {
                                        const bufferList = []; let totalLength = 0;
                                        if (typeof imgData.reset === 'function') imgData.reset();
                                        while (true) {
                                            const block = imgData.readBlock(); if (!block || block.length === 0) break;
                                            bufferList.push(block); totalLength += block.length;
                                            if ((imgData.dataLen && totalLength >= imgData.dataLen) || totalLength > 20 * 1024 * 1024) break;
                                        }
                                        if (totalLength > 0) {
                                            const finalBuffer = new Uint8Array(totalLength); let offset = 0;
                                            for (const buffer of bufferList) { finalBuffer.set(buffer, offset); offset += buffer.length; }
                                            fetchedData = finalBuffer;
                                        }
                                    } catch (e) { console.error(`[pdfParser-debug] Error ${currentAltMethod} loop:`, e.message); }
                                }
                                if (!fetchedData && imgData.bitmap && typeof ImageBitmap !== 'undefined' && imgData.bitmap instanceof ImageBitmap && typeof document !== 'undefined') {
                                    currentAltMethod = 'imgData.bitmap via Canvas';
                                    try {
                                        const canvas = document.createElement('canvas'); canvas.width = imgData.bitmap.width; canvas.height = imgData.bitmap.height;
                                        const ctx = canvas.getContext('2d');
                                        if (!ctx) throw new Error('No 2D context for ImageBitmap.');
                                        ctx.drawImage(imgData.bitmap, 0, 0);
                                        const blob = await new Promise((res, rej) => canvas.toBlob(b => b ? res(b) : rej(new Error('Canvas toBlob failed.')), 'image/png'));
                                        if (blob && blob.size > 0) {
                                            fetchedData = new Uint8Array(await blob.arrayBuffer());
                                        }
                                    } catch (e) { console.error(`[pdfParser-debug] Error ${currentAltMethod}:`, e.message); }
                                }

                                if (fetchedData) {
                                    imgData.data = fetchedData; retrievalMethod = currentAltMethod;
                                    console.log(`[pdfParser-debug] Populated imgData.data via '${retrievalMethod}'. Length: ${imgData.data.length}.`);
                                } else {
                                    imgData = null;
                                }
                            } else {
                               imgData = null;
                            }
                        } else {
                           imgData = null;
                        }
                    } catch (objError) {
                        if (attempt === MAX_RETRIES || !objError.message?.includes("Requesting object that isn't resolved yet")) {
                           imgData = null; break;
                        }
                        console.warn(`[pdfParser-extractImages-fallback] Page ${pageNum}, Key ${imgKey}, objs.get() attempt ${attempt} error: ${objError.message}. Retrying...`);
                    }
                }
              } else {
                console.log(`[pdfParser-extractImages-fallback] Page ${pageNum}, Key ${imgKey}: page.objs.get is not available.`);
              }
            }

            if (imgData && imgData.data && imgData.data.length > 0) {
                let mimeType = 'image/unknown';
                let dataToProcess = imgData.data;

                console.log(`[pdfParser-extractImages-fallback] Page ${pageNum}, Key ${imgKey}: Data obtained. Method: ${retrievalMethod}, Kind: ${imgData.kind}, W: ${imgData.width}, H: ${imgData.height}.`);

                if (imgData.kind === pdfjs.ImageKind.JPEG) {
                    mimeType = 'image/jpeg';
                } else if (imgData.kind === pdfjs.ImageKind.RGB_24BPP || imgData.kind === pdfjs.ImageKind.RGBA_32BPP) {
                    mimeType = 'image/png';
                    if (typeof document !== 'undefined') {
                        try {
                            const canvas = document.createElement('canvas'); canvas.width = imgData.width; canvas.height = imgData.height;
                            const ctx = canvas.getContext('2d');
                            if (!ctx) throw new Error('No 2D context.');
                            const imageData = ctx.createImageData(imgData.width, imgData.height);
                            const pixelData = (imgData.data instanceof Uint8ClampedArray) ? imgData.data : new Uint8ClampedArray(imgData.data.buffer, imgData.data.byteOffset, imgData.data.byteLength);
                            
                            const expectedLen = imgData.width * imgData.height * (imgData.kind === pdfjs.ImageKind.RGBA_32BPP ? 4 : 3);
                            let dataForCanvasSet;

                            if (imgData.kind === pdfjs.ImageKind.RGB_24BPP) {
                                const rgbaData = new Uint8ClampedArray(imgData.width * imgData.height * 4);
                                for (let j = 0, k = 0; j < Math.min(pixelData.length, expectedLen); j += 3, k += 4) {
                                    if (k + 3 >= rgbaData.length) break;
                                    rgbaData[k]   = pixelData[j];
                                    rgbaData[k+1] = pixelData[j+1];
                                    rgbaData[k+2] = pixelData[j+2];
                                    rgbaData[k+3] = 255;
                                }
                                dataForCanvasSet = rgbaData;
                            } else {
                                dataForCanvasSet = pixelData;
                            }
                            imageData.data.set(dataForCanvasSet.slice(0, imageData.data.length));
                            ctx.putImageData(imageData, 0, 0);
                            const blob = await new Promise((res, rej) => canvas.toBlob(b => b ? res(b) : rej(new Error('Canvas toBlob failed.')), 'image/png'));
                            dataToProcess = new Uint8Array(await blob.arrayBuffer());
                        } catch (canvasError) {
                            console.error(`[pdfParser-extractImages-fallback-canvas] Error converting raw pixels (Kind: ${imgData.kind}):`, canvasError.message);
                            mimeType = 'application/octet-stream';
                            dataToProcess = imgData.data;
                        }
                    } else {
                        console.warn(`[pdfParser-extractImages-fallback] Canvas conversion for RGB/RGBA skipped (not in browser). Kind: ${imgData.kind}. Using raw data.`);
                        mimeType = 'application/octet-stream';
                        dataToProcess = imgData.data;
                    }
                } else if (dataToProcess.length > 3 && dataToProcess[0] === 0x89 && dataToProcess[1] === 0x50 && dataToProcess[2] === 0x4E && dataToProcess[3] === 0x47) {
                    mimeType = 'image/png';
                } else if (dataToProcess.length > 1 && dataToProcess[0] === 0xFF && dataToProcess[1] === 0xD8) {
                    mimeType = 'image/jpeg';
                } else if (imgData.kind === pdfjs.ImageKind.JPX_IMAGE) {
                     mimeType = 'image/jp2';
                } else {
                    mimeType = 'application/octet-stream';
                }
                
                imagesOnPage.push({
                  data: dataToProcess, mimeType, originalKind: imgData.kind,
                  width: imgData.width, height: imgData.height, pageNum,
                  id: `fallback_page${pageNum}_obj${imgKey}_${retrievalMethod || 'unknown'}`,
                  source: `fallback-${retrievalMethod || 'unknown'}`
                });
            } else {
                 console.warn(`[pdfParser-extractImages-fallback] Page ${pageNum}: No data or invalid data for key ${imgKey}.`);
            }
          }
        }
      fallbackImages.push(...imagesOnPage);
      console.log(`[pdfParser-extractImages-fallback] Page ${pageNum}: Found ${imagesOnPage.length} images via OPS.paintImageXObject (Total paint ops: ${paintImageOpsCount}).`);
    }
    imageCandidates = fallbackImages;
    console.log(`[pdfParser-extractImages] Processing complete. Total candidates: ${imageCandidates.length}`);

    console.log(`[pdfParser-extractImages] Total image candidates after all methods: ${imageCandidates.length}. Converting to Data URI.`);

    for (const [index, imgCandidate] of imageCandidates.entries()) {
      const imageIdentifier = imgCandidate.id || `Page ${imgCandidate.pageNum}, Candidate ${index + 1}`;
      if (!imgCandidate.data || imgCandidate.data.length === 0) {
          console.warn(`[pdfParser-extractImages] ${imageIdentifier}: Data is null/empty, skipping conversion.`);
          continue;
      }
      try {
        console.log(`[pdfParser-extractImages-debug] Candidate ${imageIdentifier} - data size before base64: ${imgCandidate.data?.length}, mimeType: ${imgCandidate.mimeType}`);
        const dataUri = uint8ArrayToBase64DataURI(imgCandidate.data, imgCandidate.mimeType);
        embeddedImageObjects.push({
          src: dataUri,
          alt: `Image from PDF ${imageIdentifier} (w:${imgCandidate.width} h:${imgCandidate.height})`,
          width: imgCandidate.width,
          height: imgCandidate.height,
          pageNum: imgCandidate.pageNum // 保留页面信息用于位置匹配
        });
      } catch (conversionError) {
        console.error(`[pdfParser-extractImages] Failed to convert to Data URI for ${imageIdentifier}:`, conversionError.message);
      }
    }
    
    console.log('[pdfParser-extractImages] Finished image conversion. Total Data URIs created:', embeddedImageObjects.length);
    return embeddedImageObjects;
  } catch (error) {
    console.error('[pdfParser-extractImages] Error in extractPdfImages:', error.message);
    if (error.name === 'TypeError' && error.message.includes('detached ArrayBuffer')) {
        console.error('[pdfParser-extractImages] CRITICAL: Detached ArrayBuffer issue detected.');
    }
    return embeddedImageObjects; // Return whatever was processed, or an empty array on catastrophic failure
  }
}