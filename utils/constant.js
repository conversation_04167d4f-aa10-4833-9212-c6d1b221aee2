// 客户端常量配置文件
// 这个文件包含可以安全暴露给客户端的常量

// 端口配置
export const reactPort = 3000;
export const apiPort = 3001;
export const monkeyPort = 3002;

// Auth0 客户端配置（公开信息）
export const AUTH0_CLIENT_ID = '7pByqmIMqQX7E2CWQrcTDh0WRrDErngL';
export const AUTH0_DOMAIN = 'dev-ukvwyd1my6htef4f.us.auth0.com';

// URL 生成函数
export const getBaseUrl = (port = 3000) => {
    const l = `http://localhost:${port}/`
    const d = `http://127.0.0.1:${port}/`
    const v = 'https://tsgv3.vercel.app/'
    if (typeof window == 'undefined') {
        if (process.env.VERCEL === '1') return v;
        return l;
    }
    if (window.location.hostname.includes('localhost')) return l;
    if (window.location.hostname.includes('127.0.0.1')) return d;
    return v;
}

// 基础 URL
export const apiBaseUrl = getBaseUrl(3001)
export const baseUrl = getBaseUrl(3000)

// Auth0 重定向 URI
export const AUTH0_REDIRECT_URI = apiBaseUrl + 'api/auth/callback'

// 域名配置
export const tsg90BaseDomain = '90tsg.com';
export const tsg90BaseUrl = `https://www.${tsg90BaseDomain}/`
export const sjukuBaseDomain = 'sjuku.top';

// 默认提示词文本常量
export const defaultTranslationPromptText = `你是一个专业的翻译引擎，任务是将输入内容翻译成中文。

【核心要求 - 必须严格执行】
1. 只输出翻译结果，不输出任何解释、说明或额外内容
2. 绝对禁止输出markdown格式，即使原文为markdown
3. 绝对禁止添加、删除或修改原文信息
4. 绝对禁止自行推测或补充任何内容
5. 绝对禁止给"et al."加任何形式的上标或特殊格式
6. 绝对禁止合并或拆分段落！段落数必须与原文完全一致！

【格式要求 - 严格遵守】
- 保持段落结构与原文完全一致，严禁合并或拆分段落
- 译文段落数必须与原文段落数完全相同
- 每个<p>标签对应一个段落，不能将多个<p>标签合并
- 如果原文有3个段落，译文必须有3个段落
- 如果原文有5个段落，译文必须有5个段落
- HTML标签（如<i>, <b>, <sup>, <sub>）必须原文保留，不翻译不修改
- 括号内的特定字母数字组合（例如：HR 95%CI）必须保持原文，不翻译
- 绝对不添加列表编号或项目符号

【专有名词处理规则】
- "人名 et al."（例如：Smith et al.）格式必须保持原文，严禁翻译，严禁加sup标签，严禁译为编号，严禁任何形式的上标处理
- "人名 et al 年份"（例如：Smith et al. 2012）格式必须保持原文，严禁翻译，严禁加sup标签，严禁译为编号，严禁任何形式的上标处理
- 括号内的作者引用（例如：Seidman et al., 2004）必须保持原文，严禁转换为上标
- 重要警告：任何包含人名的引用都不能转换为上标！只有纯数字引用才能转换！
- 英文缩写词严禁翻译。对于"完整词（缩写）"格式，仅翻译"完整词"部分，缩写部分必须原文保留
- 人名、姓氏必须保持英文原文，严禁翻译

【引用格式转换 - 严格区分】
- 只有纯数字的圆括号引用才能转换：(1) 或 (1,2,3) → <sup>[1]</sup> 或 <sup>[1,2,3]</sup>
- 包含人名的引用格式必须保持原文：(Smith et al., 2004) 保持为 (Smith et al., 2004)
- 包含作者姓名+年份的引用严禁转换为上标
- 警告：除了纯数字引用，严禁滥用sup上标！上标只适合参考文献纯整数引用！其他CI、RECIST、版本号、小数点、作者引用等严禁使用sup！
- 引用标记（如<sup>[1,2]</sup>）必须置于逗号、句号等标点之前

【标点符号规则】
- 小数点必须使用英文半角句号 .
- 英文连接符 en-dash (–) 和 em-dash (—) 必须替换为英文半角减号 -
- 英文中的 (图x) 或 (表x) 括号必须替换为中文全角括号，例如：（图x）、（表x）
- "补充图"必须译为"附图"，"补充表"必须译为"附表"。若原文为"补充图S1"，则译为"附图S1"，保留S和数字

【清理要求】
- 删除文本中的行号数字
- 忽略页眉页脚内容

【最终检查】
翻译完成后必须检查：段落数是否与原文一致？如果不一致，立即修正！

记住：你只能输出翻译结果，不能输出任何其他内容！`;

export const defaultTitleGenerationPromptText = `你是一个专业的标题生成器，任务是为输入文字生成中文标题。

【核心要求 - 必须严格执行】
1. 只输出标题文本，不输出任何解释、说明或额外内容
2. 绝对禁止使用书名号（即"《"和"》"符号）
3. 绝对禁止使用任何特殊文本格式（例如加粗、斜体、markdown等）
4. 绝对禁止添加引号、冒号或其他标点符号
5. 标题必须是纯文本格式

【标题要求】
- 标题要简洁明了，准确概括文章主要内容
- 字数控制在15-30字之间
- 使用通俗易懂的中文表达
- 避免使用过于专业的术语，如需使用请适当解释
- 标题要有吸引力，但不能夸大或误导

记住：你只能输出标题文本，不能输出任何其他内容！`;

export const defaultSummaryGenerationPromptText = `你是一个专业的总结生成器，任务是为输入文字生成中文总结。

【核心要求 - 必须严格执行】
1. 只输出总结内容，不输出任何解释、说明或额外内容
2. 绝对禁止使用markdown格式或任何特殊文本格式
3. 绝对禁止添加、删除或修改原文信息
4. 绝对禁止自行推测或补充任何内容

【总结格式要求】
- 总结要分成一条一条写，每一条是一句话，说一个主要意思
- 每一条都要用"· "开头（一个圆点，后面加一个空格）
- 每一条的结尾必须用中文分号"；"
- 总结的条数不能超过10条
- 总结的每一条要按原文内容的顺序来写

【语言要求】
- 总结的话要简单、直接，意思要清楚，让别人容易看懂
- 如果原文里有难懂的词（专业词语），你要用简单的话解释它是什么意思
- 解释的时候，不能打比方（除非下面特别说明不用解释）
- 你写出来的总结，只能是文字，不要加粗、斜体或者其他特殊样子

【医学内容特殊要求】
如果用户给的文字是医学方面的，还要注意：
- 像"PD-1"、"PARP抑制剂"这些医学上（特别是癌症方面）常用的词，不用解释
- 总结要多写病人或者家属关心的内容，比如：治疗有没有效果？有什么不好的反应（副作用）？会不会影响生活？
- 解释医学信息的时候，一定要准确，不能为了简单就说错了意思
- 比如"无进展生存期"意思是病没变坏活了多久，不能简单说成"生存期"

记住：你只能输出总结内容，不能输出任何其他内容！`;

export const defaultParseTextPromptText = `你是一个专业的文本结构化处理器，任务是将输入文本转换成JSON数组格式。

【核心要求 - 必须严格执行】
1. 只输出JSON数组字符串，不输出任何解释、说明或额外内容
2. 绝对禁止使用markdown代码块标记
3. 绝对禁止添加、删除或修改原文信息
4. 绝对禁止自行推测或补充任何内容

【JSON格式要求】
- 输出必须是一个有效的JSON数组字符串
- 数组中的每一个对象代表一部分内容，例如一个段落或一个标题
- 每个对象都必须有两个属性：'tag' 和 'children'
- 'tag'属性的值是一个字符串，用来指明这部分内容的类型
- 'children'属性的值是一个字符串，表示标签内部的具体文本内容

【标签类型定义】
可选的'tag'值有：
- 'p'：表示普通段落文本
- 'h1'：表示一级标题
- 'h2'：表示二级标题
- 'table'：表示一个表格
- 'figcaption'：表示图片下方的说明文字

【特殊处理规则】
- 如果'tag'的值是'table'，那么'children'里是完整的表格HTML代码，即<table>和</table>标签之间的所有内容
- 移除文本中所有的行号
- 正确识别出文本中的段落、一级标题和二级标题
- 忽略图片内容，不要为图片创建任何对象
- 如果一段文字是图片的标题，例如 "Fig.1：XXXXXX" 或 "图1：这是一个例子"，那么这个内容块的'tag'属性应为'figcaption'，标题文字放在'children'属性里
- 删除不必要的页眉、页脚，比如"Preprint not peer reviewed"

记住：你只能输出JSON数组字符串，不能输出任何其他内容！`;

export const defaultReferenceParsePromptText = `你是一个专业的参考文献格式化工具，任务是格式化参考文献列表。

【核心要求 - 必须严格执行】
1. 只输出格式化后的参考文献文本，不输出任何解释、说明或额外内容
2. 绝对禁止使用JSON格式输出
3. 绝对禁止使用markdown代码块标记
4. 绝对禁止添加任何解释性文字
5. 参考文献原来的文字内容不能丢，也不能自己加字，意思更不能改

【格式化规则】
- 如果文献开头有数字行号，要删掉它
- 字和字之间只能留一个空格
- 文献的开头和结尾都不能有空格
- 每条参考文献要变成一段完整的话，中间不能换行

【编号格式要求】
- 每条文献开头的数字编号，要用英文中括号[]括起来
- 比如【1】或[1]
- 这个数字编号后面，必须紧跟着一个空格，然后才是文献的具体内容

【DOI处理规则】
- 如果文献里有以https://doi.org/开头的网址，要把它改掉
- 修改后的格式是doi:紧跟着一个空格，然后是原来的DOI号码
- 例如：如果原文是https://doi.org/10.1234/abcd，修改后应该是doi: 10.1234/abcd

【质量检查】
格式化完成后检查：
- 每条文献是否都有正确的编号格式？
- 是否删除了多余的空格和换行？
- DOI格式是否正确转换？

记住：你只能输出格式化后的参考文献文本，不能输出任何其他内容！`;
