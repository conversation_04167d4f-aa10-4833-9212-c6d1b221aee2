import aesjs from 'aes-js';

var utf8 = aesjs.utils.utf8;
var hex = aesjs.utils.hex
var AesCfb = aesjs.ModeOfOperation.cfb
var textRightAppend = function (text, mode) {
    var segmentByteSize = mode === 'utf8' ? 16 : 32

    if (text.length % segmentByteSize === 0) {
        return text
    }

    var appendLength = segmentByteSize - text.length % segmentByteSize
    var i = 0
    while (i++ < appendLength) {
        text += '0'
    }
    return text
}

var encrypt = function (text, key, iv) {
    var textLength = text.length
    text = textRightAppend(text, 'utf8')
    var keyBytes = utf8.toBytes(key)
    var ivBytes = utf8.toBytes(iv)
    var textBytes = utf8.toBytes(text)
    var aesCfb = new AesCfb(keyBytes, ivBytes, 16)
    var encryptBytes = aesCfb.encrypt(textBytes)
    return hex.fromBytes(ivBytes) + hex.fromBytes(encryptBytes).slice(0, textLength * 2)
}

var decrypt = function (text, key) {
    var textLength = (text.length - 32) / 2
    text = textRightAppend(text, 'hex')
    var keyBytes = utf8.toBytes(key)
    var ivBytes = hex.toBytes(text.slice(0, 32))
    var textBytes = hex.toBytes(text.slice(32))
    var aesCfb = new AesCfb(keyBytes, ivBytes, 16)
    var decryptBytes = aesCfb.decrypt(textBytes)
    return utf8.fromBytes(decryptBytes).slice(0, textLength)
}
var encrypUrl = function (protocol, url,wrdvpnKey,wrdvpnIV) {
    var port = "";
    var segments = "";

    if (url.substring(0, 7) == "http://") {
        url = url.substr(7);
    } else if (url.substring(0, 8) == "https://") {
        url = url.substr(8);
    }


    var v6 = "";
    var match = /\[[0-9a-fA-F:]+?\]/.exec(url);
    if (match) {
        v6 = match[0];
        url = url.slice(match[0].length);
    }
    segments = url.split("?")[0].split(":");
    if (segments.length > 1) {
        port = segments[1].split("/")[0]
        url = url.substr(0, segments[0].length) + url.substr(segments[0].length + port.length + 1);
    }

    if (protocol != "connection") {
        var i = url.indexOf('/');
        if (i == -1) {
            if (v6 != "") {
                url = v6;
            }
            url = encrypt(url, wrdvpnKey, wrdvpnIV)
        } else {
            var host = url.slice(0, i);
            var path = url.slice(i);
            if (v6 != "") {
                host = v6;
            }
            url = encrypt(host, wrdvpnKey, wrdvpnIV) + path;
        }
    }
    if (port != "") {
        url = protocol + "-" + port + "/" + url;
    } else {
        url = protocol + "/" + url;
    }
    return url;
}
var parseHost = function (url) {
    if (/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])/.test(url)) {
        return url.match(/^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))/)[0]
    } else if (/^(\[\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*\])/.test(url)) {
        return url.match(/^(\[\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*\])/)[0]
    } else if (/^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+?/.test(url)) {
        return url.match(/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/)[0]
    }
    return ""
}

var parsePort = function (url) {
    if (/^\//.test(url)) {
        return ""
    } else if (/^(:\d+\/)/.test(url)) {
        return url.match(/^(:\d+\/)/)[0].replace(":", "").replace("/", "")
    }
    return ""
}
var parseProtocol = function (url) {
    if (/^((http|https|ssh|telnet|rdp|vnc):\/\/)/.test(url)) {
        return url.match(/^((http|https|ssh|telnet|rdp|vnc):\/\/)/)[0].replace("://", "")
    }
    return ""
}

export function url2VParam(url,wrdvpnKey='wrdvpnisthebest!',wrdvpnIV='wrdvpnisthebest!') {
    var protocol = parseProtocol(url);
    if (protocol == "") {
        protocol='http';
    } else {
        url = url.replace(protocol + "://", "")
    }

    var host = parseHost(url);
    url = encrypUrl(protocol, url,wrdvpnKey,wrdvpnIV)

    return url;
}

