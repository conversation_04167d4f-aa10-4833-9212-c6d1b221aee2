// c:/www/tsgv3/hooks/useTranslationControls.js
import { useCallback, useState } from 'react';
import { useStore } from '../stores/index.js';
import { App } from 'antd'; // Import App for App.useApp()

export const useTranslationControls = (chunked, stableUpdateTranslationDOM) => {
  const { message: messageApi } = App.useApp(); // Ensure App.useApp() is available

  // Store selectors
  const translateAllStore = useStore(state => state.translateAll);
  const cancelAllTranslationsStore = useStore(state => state.cancelAllTranslations);
  const saveArticleStore = useStore(state => state.saveArticle);
  const setGlobalCancellationFlagStore = useStore(state => state.setGlobalCancellationFlag);
  const setIsTranslatingAllActiveStore = useStore(state => state.setIsTranslatingAllActive);
  const clearIndicesToTranslateStore = useStore(state => state.clearIndicesToTranslate);
  const clearAllTranslationsActionStore = useStore(state => state.clearAllTranslations);
  const translateChunkStore = useStore(state => state.translateChunk);
  const currentTranslationControllersStore = useStore(state => state.currentTranslationControllers);
  const isTranslatingAllActiveStore = useStore(state => state.isTranslatingAllActive);
  const indicesToTranslateStore = useStore(state => state.indicesToTranslate);
  const removeTranslationControllerStore = useStore(state => state.removeTranslationController);
  const clearStreamingChunkTextStore = useStore(state => state.clearStreamingChunkText);
  const setIndicesToTranslateStore = useStore(state => state.setIndicesToTranslate);
  const cancelSingleTranslationStore = useStore(state => state.cancelSingleTranslation);
  const clearTranslatedChunkActionStore = useStore(state => state.clearTranslatedChunk);

  const [stoppingChunks, setStoppingChunks] = useState(new Set());

  const handleTranslateAll = useCallback(async () => {
    if (chunked && chunked.length > 0) {
      try {
        messageApi.info("开始翻译全文...");
        await translateAllStore(chunked);
        if (!useStore.getState().isTranslationGloballyCancelled) {
          messageApi.success("全文翻译完成");
        } else {
          messageApi.info("翻译已取消");
        }
      } catch (error) {
        messageApi.error(`翻译过程中发生错误: ${error.message || '未知错误'}`);
      }
    } else {
      messageApi.error("无法获取文章内容以进行翻译");
    }
  }, [chunked, translateAllStore, messageApi]);

  const handleCancelTranslateAll = useCallback(() => {
    return new Promise((resolve, reject) => {
      const {
        isTranslationGloballyCancelled: currentlyCancelling,
      } = useStore.getState();

      if (currentlyCancelling) {
        messageApi.info("正在取消翻译，请稍候...");
        resolve({ status: 'already_cancelling' });
        return;
      }
      messageApi.loading({ content: "正在停止翻译...", key: "cancel-all-translation", duration: 0 });

      setTimeout(async () => {
        try {
          setGlobalCancellationFlagStore(true);
          const latestControllers = useStore.getState().currentTranslationControllers;
          for (const [, controller] of latestControllers.entries()) {
            if (controller && controller.abort) controller.abort();
          }
          await cancelAllTranslationsStore();
          const latestTranslated = useStore.getState().translated;
          try {
            await saveArticleStore({
              translated: latestTranslated
            });
            messageApi.success({ content: "已停止翻译并保存当前进度", key: "cancel-all-translation", duration: 2 });
          } catch (saveError) {
            messageApi.error({ content: `保存翻译进度失败: ${saveError.message || '未知错误'}`, key: "cancel-all-translation", duration: 3 });
          }
          setIsTranslatingAllActiveStore(false);
          clearIndicesToTranslateStore();
          resolve({ status: 'cancelled_successfully' });
        } catch (error) {
          messageApi.error({ content: `取消翻译时发生错误: ${error.message || '未知错误'}`, key: "cancel-all-translation", duration: 3 });
          reject(error);
        } finally {
          setGlobalCancellationFlagStore(false);
        }
      }, 100);
    });
  }, [messageApi, setGlobalCancellationFlagStore, cancelAllTranslationsStore, saveArticleStore, setIsTranslatingAllActiveStore, clearIndicesToTranslateStore]);

  const handleClearAll = useCallback(async () => {
    const isTranslatingAllActiveCurrently = useStore.getState().isTranslatingAllActive;
    if (isTranslatingAllActiveCurrently) {
      messageApi.info("翻译全文正在进行，将先停止翻译...");
      try {
        await handleCancelTranslateAll();
      } catch (error) {
        messageApi.error(`停止翻译过程中发生错误: ${error.message || '未知错误'}。清空操作未执行。`);
        return;
      }
    }
    try {
      await clearAllTranslationsActionStore();
      messageApi.success('译文已清空');
    } catch (error) {
      messageApi.error(`清空译文失败: ${error.message}`);
    }
  }, [handleCancelTranslateAll, clearAllTranslationsActionStore, messageApi]);

  const handleTranslateChunk = useCallback(async (idx, originalItemsInChunk, shouldDisableTranslateButton) => {
    if (shouldDisableTranslateButton(idx)) return;
    try {
      console.log(`[useTranslationControls] handleTranslateChunk 开始: 区块=${idx}, 原始项数=${originalItemsInChunk?.length || 0}`);

      // 记录DOM容器状态
      if (typeof window !== 'undefined' && window._translationContainers && window._translationContainers[idx]) {
        console.log(`[useTranslationControls] handleTranslateChunk 容器状态: 区块=${idx}, 内容长度=${window._translationContainers[idx].innerHTML.length}, 内容样本=${window._translationContainers[idx].innerHTML.substring(0, 50)}...`);
      }

      // 记录当前store状态
      const storeStateBefore = useStore.getState();
      console.log(`[useTranslationControls] handleTranslateChunk store状态: 区块=${idx}, 翻译项数=${Object.keys(storeStateBefore.translated || {}).length}`);

      stableUpdateTranslationDOM(idx, []);
      const result = await translateChunkStore(originalItemsInChunk, idx);
      console.log(`[useTranslationControls] handleTranslateChunk 翻译结果: 区块=${idx}, 状态=${result.status}`);

      if (result.status === 'completed' || result.status === 'partial_completed') {
        try {
          const { translated: currentTranslatedAfterChunk, article: currentArticleDetails, content: currentContentAfterChunk } = useStore.getState();

          // 记录保存前的状态
          console.log(`[useTranslationControls] handleTranslateChunk 保存前状态: 区块=${idx}, 翻译项数=${Object.keys(currentTranslatedAfterChunk || {}).length}`);

          // 检查区块中的项目是否在translated中
          const chunkItemIds = originalItemsInChunk.map(item => item.id).filter(Boolean);
          const missingIds = chunkItemIds.filter(id => !currentTranslatedAfterChunk[id]);
          if (missingIds.length > 0) {
            console.warn(`[useTranslationControls] handleTranslateChunk 警告: 区块=${idx}, 有${missingIds.length}个ID在translated中缺失: ${missingIds.join(', ')}`);
          } else {
            console.log(`[useTranslationControls] handleTranslateChunk: 区块=${idx}, 所有ID在translated中都存在`);
          }

          // 记录DOM容器状态
          if (typeof window !== 'undefined' && window._translationContainers && window._translationContainers[idx]) {
            console.log(`[useTranslationControls] handleTranslateChunk 保存前容器状态: 区块=${idx}, 内容长度=${window._translationContainers[idx].innerHTML.length}, 内容样本=${window._translationContainers[idx].innerHTML.substring(0, 50)}...`);
          }

          await saveArticleStore({
            translated: currentTranslatedAfterChunk
          });

          console.log(`[useTranslationControls] handleTranslateChunk 保存成功: 区块=${idx}`);
        } catch (saveError) {
          console.error(`[useTranslationControls] handleTranslateChunk 保存失败: 区块=${idx}, 错误=${saveError.message}`, saveError);
          messageApi.error(`保存译文区块 ${idx} 失败: ${saveError.message}`);
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        messageApi.error(`翻译失败: ${error.message}`);
      }
    }
  }, [stableUpdateTranslationDOM, translateChunkStore, saveArticleStore, messageApi]);


  const handleStopChunkTranslation = useCallback(async (idx) => {
    try {
      setStoppingChunks(prev => new Set([...prev, idx]));
      const messageKey = `stopping-chunk-${idx}`;
      messageApi.loading({ content: "正在停止翻译...", key: messageKey, duration: 0 });

      const isActuallyTranslating = currentTranslationControllersStore.has(idx);
      const isInTranslationQueue = isTranslatingAllActiveStore && indicesToTranslateStore?.includes(idx);

      if (!isActuallyTranslating && !isInTranslationQueue) {
        setStoppingChunks(prev => { const newSet = new Set([...prev]); newSet.delete(idx); return newSet; });
        messageApi.info({ content: '该区块未在翻译中', key: messageKey, duration: 2 });
        return;
      }
      try {
        if (isActuallyTranslating) {
          const controller = currentTranslationControllersStore.get(idx);
          if (controller && controller.abort) {
            controller.abort();
            removeTranslationControllerStore(idx);
            clearStreamingChunkTextStore(idx);
          }
        }
        if (isInTranslationQueue) {
          const currentIndices = indicesToTranslateStore || [];
          const updatedIndices = currentIndices.filter(itemIdx => itemIdx !== idx);
          setIndicesToTranslateStore(updatedIndices);
        }
        if (isActuallyTranslating) {
          await cancelSingleTranslationStore(idx);
          const { translated: currentTranslatedAfterStop } = useStore.getState();
          await saveArticleStore({
            translated: currentTranslatedAfterStop
          });
          messageApi.success({ content: '已停止翻译并保存当前进度', key: messageKey, duration: 2 });
        } else if (isInTranslationQueue) {
          messageApi.success({ content: '已从翻译队列中移除', key: messageKey, duration: 2 });
        }
      } finally {
        setStoppingChunks(prev => { const newSet = new Set([...prev]); newSet.delete(idx); return newSet; });
      }
    } catch (error) {
      messageApi.error(`停止翻译失败: ${error.message || '未知错误'}`);
      setStoppingChunks(prev => { const newSet = new Set([...prev]); newSet.delete(idx); return newSet; });
    }
  }, [
    messageApi, currentTranslationControllersStore, isTranslatingAllActiveStore, indicesToTranslateStore,
    removeTranslationControllerStore, clearStreamingChunkTextStore, setIndicesToTranslateStore,
    cancelSingleTranslationStore, saveArticleStore
  ]);

  const handleClearTranslatedChunk = useCallback(async (idx, itemsInChunk) => {
    const messageKey = `clearing-chunk-${idx}`;
    try {
      messageApi.loading({ content: '清空中...', key: messageKey, duration: 0 });
      await clearTranslatedChunkActionStore(itemsInChunk);
      messageApi.success({ content: `区块 ${idx} 译文已清空`, key: messageKey, duration: 2 });
    } catch (error) {
      messageApi.error({ content: `清空失败: ${error.message}`, key: messageKey, duration: 5 });
    }
  }, [clearTranslatedChunkActionStore, messageApi]);

  return {
    stoppingChunks, // Expose for UI indication if needed
    handleTranslateAll,
    handleCancelTranslateAll,
    handleClearAll,
    handleTranslateChunk,
    handleStopChunkTranslation,
    handleClearTranslatedChunk,
  };
};