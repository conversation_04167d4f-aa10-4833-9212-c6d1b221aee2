import { useState, useCallback, useMemo } from 'react';
import { useStore } from '../stores/index.js';
import { apiBaseUrl } from '../utils/constant.js';
import { App } from 'antd';

export const useAISettings = () => {
  const { message: messageApi } = App.useApp();
  
  // 从store获取状态
  const storeApis = useStore(state => state.apis);
  const storeDefaultApiModel = useStore(state => state.defaultApiModel);
  const user = useStore(state => state.user);
  const setUser = useStore(state => state.setUser);
  const setDefaultApiModel = useStore(state => state.setDefaultApiModel);
  const isTranslatingAllActive = useStore(state => state.isTranslatingAllActive);
  const isTranslationGloballyCancelled = useStore(state => state.isTranslationGloballyCancelled);
  
  // 本地状态
  const [concurrencyLevel, setConcurrencyLevel] = useState(user?.concurrencyLevel ?? 3);

  // 构建TreeSelect的数据
  const treeData = useMemo(() => {
    const systemDefaultApiOption = {
      title: '系统模型',
      value: '系统默认|GLM-4-9B-0414',
      key: '系统默认|GLM-4-9B-0414',
    };
    let userApisData = [];
    if (Array.isArray(storeApis)) {
      userApisData = storeApis.map(api => {
        if (!api || api.key === 'system-default') return null;
        return {
          title: api.provider || '未命名Provider',
          value: api.key,
          key: api.key,
          selectable: false,
          children: Array.isArray(api.models) ? api.models.map(model => {
            const nodeValue = `${api.provider}|${model}`;
            return { title: model, value: nodeValue, key: nodeValue };
          }) : []
        };
      }).filter(Boolean);
    }
    return [systemDefaultApiOption, ...userApisData];
  }, [storeApis]);

  // 保存并发数设置
  const triggerSaveConcurrencyLevel = useCallback(async (value) => {
    const newLevel = value || 1;
    try {
      const response = await fetch(`${apiBaseUrl}api/user/concurrencyLevel`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ concurrencyLevel: newLevel })
      });
      if (!response.ok) throw new Error(`保存并发设置失败: ${response.statusText}`);
      const result = await response.json();
      if (result.success) {
        setUser({ ...user, concurrencyLevel: newLevel });
      } else {
        throw new Error(result.error || '保存并发设置时发生未知错误');
      }
    } catch (error) {
      messageApi.error(`保存并发设置失败: ${error.message}`);
    }
  }, [user, setUser, messageApi]);

  // 处理并发数变化
  const handleConcurrencyChangeImmediate = useCallback((value) => {
    let newLevel;
    if (typeof value !== 'number') {
      newLevel = concurrencyLevel;
    } else {
      newLevel = (typeof value === 'number' && !isNaN(value)) ? Math.max(1, Math.min(10, value)) : 1;
      setConcurrencyLevel(newLevel);
    }
    triggerSaveConcurrencyLevel(newLevel);
  }, [triggerSaveConcurrencyLevel, concurrencyLevel]);

  // 处理模型变化
  const handleModelChange = useCallback((value) => {
    console.log(`[handleModelChange] 开始处理模型更改, value:`, value);
    if (value) {
      const parts = value.split('|');
      let provider, modelName;
      if (parts.length === 2) {
        [provider, modelName] = parts;
        console.log(`[handleModelChange] 解析结果: provider=${provider}, modelName=${modelName}`);
      } else {
        console.warn("API模型选择的value格式不符合预期:", value);
        return;
      }

      // 保存到store
      setDefaultApiModel(provider, modelName);
      
      // 保存到服务器
      const saveToServer = async () => {
        try {
          const response = await fetch(`${apiBaseUrl}api/user/defaultApiModel`, {
            method: 'POST',
            credentials: 'include',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ provider, model: modelName })
          });
          if (!response.ok) throw new Error(`保存模型设置失败: ${response.statusText}`);
          const result = await response.json();
          if (!result.success) {
            throw new Error(result.error || '保存模型设置时发生未知错误');
          }
        } catch (error) {
          messageApi.error(`保存模型设置失败: ${error.message}`);
        }
      };
      saveToServer();
    }
  }, [setDefaultApiModel, messageApi]);

  return {
    // 状态
    concurrencyLevel,
    treeData,
    storeDefaultApiModel,
    isTranslatingAllActive,
    isTranslationGloballyCancelled,
    
    // 处理函数
    handleConcurrencyChangeImmediate,
    handleModelChange,
  };
};
