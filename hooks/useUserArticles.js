import { useEffect, useState, useRef } from 'react';
import { useStore } from '../stores/index.js';
import { shallow } from 'zustand/shallow';
import { useNavigate, useParams } from 'react-router-dom';
import { App }
from 'antd';

export const useUserArticles = () => {
  const navigate = useNavigate();
  const { uuid: currentArticleUuidFromParams } = useParams();
  const { message: messageApi } = App.useApp();

  const user = useStore(state => state.user, shallow);
  const userArticles = useStore(state => state.userArticles, shallow);
  const isLoadingUserArticles = useStore(state => state.isLoadingUserArticles);
  const fetchUserArticlesStore = useStore(state => state.fetchUserArticles);
  const deleteUserArticleStore = useStore(state => state.deleteUserArticle);

  const [deletingId, setDeletingId] = useState(null);
  const prevUserIdRef = useRef();

  useEffect(() => {
    const currentUserId = user?.uuid;
    const prevUserId = prevUserIdRef.current;

    if (currentUserId) {
      if (currentUserId !== prevUserId) {
        fetchUserArticlesStore();
      }
    }
    prevUserIdRef.current = currentUserId;
  }, [user?.uuid, fetchUserArticlesStore]);

  const handleDeleteArticle = async (articleId) => {
    if (!articleId || deletingId === articleId) return;
    setDeletingId(articleId);
    try {
      await deleteUserArticleStore(articleId);
      messageApi.success('文章删除成功');
      const currentPath = window.location.pathname;
      if (currentPath.includes(`/article/${articleId}/edit`)) {
        messageApi.info('当前文章已被删除，将返回首页。', 2, () => navigate('/'));
      }
      // fetchUserArticlesStore(); // deleteUserArticleStore 内部应该会刷新列表
    } catch (error) {
      messageApi.error(error.message || '删除文章失败');
    } finally {
      setDeletingId(null);
    }
  };

  const handleMenuClick = (e) => {
    navigate(`/article/${e.key}/edit`);
  };

  return {
    userArticles,
    isLoadingUserArticles,
    currentArticleUuidFromParams,
    deletingId,
    handleDeleteArticle,
    handleMenuClick,
    fetchUserArticles: fetchUserArticlesStore, // 暴露一个刷新函数以备不时之需
  };
};