// c:/www/tsgv3/hooks/useChunkManagement.js
import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useStore } from '../stores';
import _ from 'lodash';
import debounce from 'lodash.debounce';
import { sliceContentByIds } from '../utils/index.js';

export const useChunkManagement = (initialContentProp, initialUserChunkSize) => {
  const contentFromStore = useStore(state => state.content);
  const translated = useStore(state => state.translated || {});
  const user = useStore(state => state.user);
  const saveChunkSize = useStore(state => state.saveChunkSize);
  const { message: messageApi } = useStore(state => state.getAppBridge?.()) || {}; // 从 store 获取 App 实例

  const [chunkInputValue, setChunkInputValue] = useState(initialUserChunkSize ?? 5);
  const [displayedChunkSize, setDisplayedChunkSize] = useState(initialUserChunkSize ?? 5);
  const initialChunkSizeAppliedRef = useRef(false);
  const prevAllChunkTranslatedItemsRef = useRef([]);

  useEffect(() => {
    if (!initialChunkSizeAppliedRef.current && typeof user?.chunkSize === 'number') {
      const storeChunkSize = user.chunkSize;
      setChunkInputValue(storeChunkSize);
      setDisplayedChunkSize(storeChunkSize);
      initialChunkSizeAppliedRef.current = true;
    }
  }, [user?.chunkSize]);

  const chunked = useMemo(() => {
    const currentContentToChunk = contentFromStore && contentFromStore.length > 0 ? contentFromStore : initialContentProp;
    if (!Array.isArray(currentContentToChunk)) return [];

    const contentWithIds = currentContentToChunk
      .map((item, index) => (item === null ? null : { ...item, id: index }))
      .filter(item => item !== null);

    if (contentWithIds.length === 0) return [[]];

    const chunkIds = contentWithIds
      .filter(item => ['img', 'table'].includes(item.tag))
      .map(item => item.id);

    const slicedBySpecialTags = sliceContentByIds(contentWithIds, chunkIds);
    const finalChunks = slicedBySpecialTags.map(items => _.chunk(items, chunkInputValue)).flat();
    const resultChunks = finalChunks.filter(chunk => chunk.length > 0);

    return resultChunks.length === 0 ? [[]] : resultChunks;
  }, [initialContentProp, contentFromStore, chunkInputValue]);

  const allChunkTranslatedItems = useMemo(() => {
    const newAllChunkTranslatedItems = chunked.map((originalItemsInChunk, chunkIndex) => {
      if (!Array.isArray(originalItemsInChunk)) return [];

      const prevChunkItems = prevAllChunkTranslatedItemsRef.current[chunkIndex] || [];
      const prevChunkItemsById = _.keyBy(prevChunkItems.filter(item => item && typeof item.id !== 'undefined'), 'id');

      return originalItemsInChunk.map(originalItem => {
        if (!originalItem || typeof originalItem.id === 'undefined') {
          return undefined;
        }
        const translatedItem = translated[originalItem.id];
        const prevTranslatedItem = prevChunkItemsById[originalItem.id];

        if (_.isEqual(translatedItem, prevTranslatedItem)) {
          return prevTranslatedItem;
        }
        return translatedItem;
      });
    });

    // Compare the overall new structure with the previous one.
    // This is a shallow comparison for the outer array, deeper comparison is handled above.
    // If the new array of arrays of items is structurally similar and items are _.isEqual,
    // we can return the previous ref to stabilize downstream consumers.
    // However, _.isEqual for complex nested arrays can be expensive.
    // The item-wise _.isEqual above already helps a lot.
    // For now, we'll update the ref and return the new structure,
    // relying on the item-wise memoization.
    if (!_.isEqual(newAllChunkTranslatedItems, prevAllChunkTranslatedItemsRef.current)) {
       prevAllChunkTranslatedItemsRef.current = newAllChunkTranslatedItems;
    }
    return prevAllChunkTranslatedItemsRef.current; // Return the (potentially old) ref if structure is same

  }, [chunked, translated]);

  const triggerSaveChunkSize = useCallback(async (value) => {
    const newSize = value || 1;
    try {
      await saveChunkSize(newSize);
    } catch (error) {
      if (messageApi && messageApi.error) {
        messageApi.error(`保存分块设置失败: ${error.message}`);
      } else {
        console.error(`保存分块设置失败: ${error.message}`);
      }
    }
  }, [saveChunkSize, messageApi]);

  const debouncedProcessChunkSizeChange = useMemo(
    () => debounce((newSizeToProcess) => {
      setChunkInputValue(newSizeToProcess);
      triggerSaveChunkSize(newSizeToProcess);
    }, 700),
    [triggerSaveChunkSize] // setChunkInputValue is stable
  );

  const handleDisplayChunkSizeChange = useCallback((valueFromInput) => {
    let newDisplaySize = (typeof valueFromInput === 'number' && !isNaN(valueFromInput)) ? valueFromInput : 1;
    newDisplaySize = Math.max(1, newDisplaySize);
    setDisplayedChunkSize(newDisplaySize);
    debouncedProcessChunkSizeChange(newDisplaySize);
  }, [debouncedProcessChunkSizeChange]);

  const handleChunkSizeChangeImmediate = useCallback(() => {
    debouncedProcessChunkSizeChange.cancel();
    setChunkInputValue(displayedChunkSize);
    triggerSaveChunkSize(displayedChunkSize);
  }, [displayedChunkSize, debouncedProcessChunkSizeChange, triggerSaveChunkSize]); // setChunkInputValue is stable

  return {
    chunked,
    allChunkTranslatedItems,
    chunkInputValue, // Might not be needed by the page directly if only display/handlers are exposed
    displayedChunkSize,
    handleDisplayChunkSizeChange,
    handleChunkSizeChangeImmediate,
  };
};