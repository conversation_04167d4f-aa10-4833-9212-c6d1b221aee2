import { App } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useStore } from '../stores';
import { shallow } from 'zustand/shallow';

export const useAuth = () => {
  const navigate = useNavigate();
  const { message: messageApi } = App.useApp(); // Renamed to avoid conflict if other messageApis are in scope

  const user = useStore(state => state.user, shallow);
  const logoutStore = useStore(state => state.logout); // Renamed for clarity

  const handleLogout = async () => {
    try {
      await logoutStore();
      navigate('/login');
      messageApi.success('已成功退出登录');
    } catch (error) {
      messageApi.error('退出登录失败');
    }
  };

  const handleLoginNavigation = () => {
    navigate('/login');
  };

  return {
    user,
    handleLogout,
    handleLoginNavigation,
  };
};