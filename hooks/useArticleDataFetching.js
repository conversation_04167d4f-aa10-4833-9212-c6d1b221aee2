// c:/www/tsgv3/hooks/useArticleDataFetching.js
import { useState, useEffect, useRef } from 'react';
import { useStore } from '../stores/index.js'; // 假设 stores/index.js 在上一级目录

export const useArticleDataFetching = (uuid, locationPathname) => {
  const [isLoading, setIsLoading] = useState(false);
  const requestSentRef = useRef(false);
  const prevIdentifierRef = useRef(null);
  const abortControllerRef = useRef(null);

  // 从 store 中选择需要的状态和 action
  const articleFromStore = useStore(state => state.article);
  const contentFromStore = useStore(state => state.content);
  const fetchArticleForEditing = useStore(state => state.fetchArticleForEditing);
  const setContent = useStore(state => state.setContent);

  useEffect(() => {
    let identifierForFetch = uuid;

    // 当 identifier 变化时，重置请求发送标记并清理 store 状态
    if (identifierForFetch && identifierForFetch !== prevIdentifierRef.current) {
      requestSentRef.current = false;

      // 清理 store 中的旧数据
      if (articleFromStore?.uuid && articleFromStore.uuid !== identifierForFetch) {
        // 使用 setContent 来清理，这样更安全
        setContent([]);
        // 注意：我们不能直接设置 article 为 null，因为没有 setArticle 函数
        // fetchArticleForEditing 会在获取新数据时更新 article
      }
    }

    const fetchData = async (currentIdToFetch) => {
      // 如果此实例已为此 ID 发送请求，则跳过
      if (requestSentRef.current && prevIdentifierRef.current === currentIdToFetch) {
        setIsLoading(false);
        return;
      }

      // 如果 store 中已有此 ID 的内容，则跳过
      if (articleFromStore?.uuid === currentIdToFetch && contentFromStore && contentFromStore.length > 0) {
        setIsLoading(false);
        requestSentRef.current = true; // 标记此 ID 已处理
        prevIdentifierRef.current = currentIdToFetch; // 标记此 ID 已被此实例处理
        return;
      }

      setIsLoading(true);
      requestSentRef.current = true; // 标记正在为此 ID 发起请求

      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // 创建新的 AbortController
      abortControllerRef.current = new AbortController();

      try {
        // fetchArticleForEditing action 会在内部更新 store
        await fetchArticleForEditing(currentIdToFetch, abortControllerRef.current.signal);
        prevIdentifierRef.current = currentIdToFetch; // 只有成功时才标记为已处理
      } catch (error) {
        // 如果是取消请求，重置请求标记以便重试
        if (error.name === 'AbortError') {
          requestSentRef.current = false; // 重置标记，允许重试
          prevIdentifierRef.current = null; // 重置标记，允许重试
        } else {
          console.error(`[useArticleDataFetching] Error calling fetchArticleForEditing for ID ${currentIdToFetch}:`, error);
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (identifierForFetch) {
      // 如果尚未发送请求或 identifier 已更改，则获取数据
      if (!requestSentRef.current || prevIdentifierRef.current !== identifierForFetch) {
        fetchData(identifierForFetch);
      }
    } else {
      // 如果没有 identifier (例如，导航到空白状态)，则清除内容
      // 修复：当没有UUID时，调用fetchArticleForEditing(null)来完全清理store状态
      if (!requestSentRef.current || prevIdentifierRef.current !== null) {
        fetchArticleForEditing(null); // 这会清理article、content、translated等所有状态
        requestSentRef.current = true; // 标记为“已处理”无 identifier 的情况
        prevIdentifierRef.current = null; // 标记当前处理的是null状态
      }
      setIsLoading(false);
    }

    // 清理函数：取消正在进行的请求
    return () => {
      if (abortControllerRef.current) {
        // Erroneous/duplicate log lines that referenced currentIdToFetch removed.
        abortControllerRef.current.abort();
      }
    };

  }, [uuid, locationPathname, fetchArticleForEditing, setContent]);

  return { isLoading }; // 返回加载状态，Page 组件可能会用到
};