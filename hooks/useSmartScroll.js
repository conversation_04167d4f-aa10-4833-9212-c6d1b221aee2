import { useCallback } from 'react';

/**
 * 智能滚动定位Hook
 * 提供统一的滚动定位逻辑，支持原文和译文的滚动定位
 */
export const useSmartScroll = (
  stickyHeaderRef,
  isHeaderSticky,
  isStickyDisabled
) => {

  /**
   * 计算目标chunk位置的通用逻辑
   * @param {Object} options - 计算选项
   * @param {string} options.operation - 操作类型：'save' 或 'cancel'
   * @param {boolean} options.isTranslated - 是否为译文
   * @param {Object} options.fullPageEditorRef - CodeMirror编辑器引用
   * @param {function} options.getChunkIndexFromLineNumber - 根据行号获取chunk索引的函数
   * @param {number|null} options.originalEditingChunkIndex - 原始编辑chunk索引
   * @returns {Object} 包含targetChunkIndex, capturedLineNumber, usedCurrentPosition的对象
   */
  const calculateTargetPosition = useCallback((options) => {
    const {
      operation,
      isTranslated,
      fullPageEditorRef,
      getChunkIndexFromLineNumber,
      originalEditingChunkIndex
    } = options;

    let targetChunkIndex = 0;
    let capturedLineNumber = null;
    let usedCurrentPosition = false;

    const operationType = isTranslated ? 'translated' : 'original';
    console.log(`[Debug] calculateTargetPosition: ${operation} ${operationType}, originalEditingChunkIndex:`, originalEditingChunkIndex);

    if (operation === 'save') {
      // 保存操作：优先使用CodeMirror的当前光标位置（用户最后focus的位置）
      const codeMirrorView = fullPageEditorRef.current?.view;
      if (codeMirrorView && codeMirrorView.state) {
        const doc = codeMirrorView.state.doc;
        const selection = codeMirrorView.state.selection;

        // 优先使用光标位置，如果没有选择则使用可见范围
        let targetPos;
        if (selection && selection.main) {
          targetPos = selection.main.head; // 光标位置
          console.log(`[Debug] calculateTargetPosition: Using cursor position: ${targetPos}`);
        } else if (codeMirrorView.visibleRanges.length > 0) {
          targetPos = codeMirrorView.visibleRanges[0].from; // 可见范围开始位置
          console.log(`[Debug] calculateTargetPosition: Using visible range position: ${targetPos}`);
        }

        if (targetPos !== undefined) {
          capturedLineNumber = doc.lineAt(targetPos).number;
          targetChunkIndex = getChunkIndexFromLineNumber(capturedLineNumber, isTranslated);
          usedCurrentPosition = true;
          console.log(`[Debug] calculateTargetPosition: Using current CM position - CM line: ${capturedLineNumber}, Target chunk: ${targetChunkIndex}`);
        }
      } else if (originalEditingChunkIndex !== null && originalEditingChunkIndex >= 0) {
        // 如果无法获取CodeMirror位置，才使用保存的原始chunk索引作为fallback
        targetChunkIndex = originalEditingChunkIndex;
        console.log(`[Debug] calculateTargetPosition: Fallback to saved original chunk index: ${targetChunkIndex}`);
      } else {
        console.warn('[Debug] calculateTargetPosition: No CodeMirror view and no saved chunk index');
        targetChunkIndex = 0; // 默认回到第一个chunk
      }
    } else if (operation === 'cancel') {
      // 取消操作：优先使用保存的原始chunk索引（回到进入编辑时的位置）
      if (originalEditingChunkIndex !== null && originalEditingChunkIndex >= 0) {
        targetChunkIndex = originalEditingChunkIndex;
        console.log(`[Debug] calculateTargetPosition: Using saved original chunk index: ${targetChunkIndex}`);
      } else {
        // 如果没有保存的索引，才使用CodeMirror的行号计算
        const codeMirrorView = fullPageEditorRef.current?.view;
        if (codeMirrorView && codeMirrorView.visibleRanges.length > 0) {
          const visibleRanges = codeMirrorView.visibleRanges;
          const firstVisiblePos = visibleRanges[0].from;
          const doc = codeMirrorView.state.doc;
          capturedLineNumber = doc.lineAt(firstVisiblePos).number;
          targetChunkIndex = getChunkIndexFromLineNumber(capturedLineNumber, isTranslated);
          console.log(`[Debug] calculateTargetPosition: Fallback to CM calculation - CM line: ${capturedLineNumber}, Target chunk: ${targetChunkIndex}`);
        } else {
          console.warn('[Debug] calculateTargetPosition: No saved chunk index and no CodeMirror view');
          targetChunkIndex = 0; // 默认回到第一个chunk
        }
      }
    }

    return {
      targetChunkIndex,
      capturedLineNumber,
      usedCurrentPosition
    };
  }, []);
  /**
   * 执行智能滚动定位
   * @param {Object} options - 滚动选项
   * @param {number} options.targetChunkIndex - 目标chunk索引
   * @param {number} [options.capturedLineNumber] - 捕获的行号（用于调试）
   * @param {boolean} [options.useOriginalIndex] - 是否使用原始索引（用于调试）
   * @param {string} [options.type] - 滚动类型，'original' 或 'translated'
   */
  const executeSmartScroll = useCallback((options) => {
    const {
      targetChunkIndex,
      capturedLineNumber = null,
      useOriginalIndex = false,
      type = 'original'
    } = options;

    console.log(`[Debug] executeSmartScroll triggered for ${type}:`, {
      targetChunkIndex,
      capturedLineNumber,
      useOriginalIndex
    });

    // 使用 requestAnimationFrame 确保DOM渲染完成
    requestAnimationFrame(() => {
      const targetElementId = `chunk-row-${targetChunkIndex}`;
      const targetElement = document.getElementById(targetElementId);

      if (targetElement) {
        const toolbarElem = document.querySelector('.article-controls');
        const toolbarHeight = toolbarElem ? toolbarElem.getBoundingClientRect().height : 80;

        console.log(`[Debug] Found ${type} target element ${targetElementId}, scrolling into view`);
        console.log(`  - Target chunk index: ${targetChunkIndex}`);
        console.log(`  - Used original index: ${useOriginalIndex ? 'YES' : 'NO'}`);
        console.log(`  - Original CM line: ${capturedLineNumber || 'N/A'}`);
        console.log(`  - Toolbar height: ${toolbarHeight}`);

        // 使用 scroll-margin-top 方法进行滚动
        setTimeout(() => {
          console.log(`[Debug Scroll ${type} V2] setTimeout triggered.`);
          const currentToolbarElem = document.querySelector('.article-controls');
          const currentToolbarHeight = currentToolbarElem ? currentToolbarElem.getBoundingClientRect().height : 80;
          console.log(`[Debug Scroll ${type} V2] currentToolbarHeight: ${currentToolbarHeight}`);

          let stickyHeaderOffset = 0;
          const stickyHeaderElement = stickyHeaderRef.current;
          console.log(`[Debug Scroll ${type} V2] stickyHeaderElement exists: ${!!stickyHeaderElement}`);

          if (stickyHeaderElement) {
            console.log(`[Debug Scroll ${type} V2] isStickyDisabled (state): ${isStickyDisabled}`);
            console.log(`[Debug Scroll ${type} V2] isHeaderSticky (state): ${isHeaderSticky}`);
            if (!isStickyDisabled && isHeaderSticky) {
              stickyHeaderOffset = stickyHeaderElement.offsetHeight;
              console.log(`[Debug Scroll ${type} V2] stickyHeaderElement.offsetHeight: ${stickyHeaderOffset}`);
            } else {
              console.log(`[Debug Scroll ${type} V2] Sticky header offset not applied (isStickyDisabled: ${isStickyDisabled}, isHeaderSticky: ${isHeaderSticky})`);
            }
          }
          
          const paddingFromHeader = 10; // 目标元素顶部与最后一个固定头之间的间隙
          console.log(`[Debug Scroll ${type} V2] paddingFromHeader: ${paddingFromHeader}`);

          const scrollMarginValue = currentToolbarHeight + stickyHeaderOffset + paddingFromHeader;
          console.log(`[Debug Scroll ${type} V2] Calculated scrollMarginValue: ${scrollMarginValue}px (Toolbar: ${currentToolbarHeight}, StickyOffset: ${stickyHeaderOffset}, Padding: ${paddingFromHeader})`);

          try {
            targetElement.style.scrollMarginTop = `${scrollMarginValue}px`;
            console.log(`[Debug Scroll ${type} V2] Applied scrollMarginTop: ${targetElement.style.scrollMarginTop} to element ${targetElementId}`);
            
            targetElement.scrollIntoView({ behavior: 'auto', block: 'start' });
            console.log(`[Debug Scroll ${type} V2] Called scrollIntoView on ${targetElementId}`);
            
            // 日志记录 scrollIntoView 后的状态
            const rectAfterScroll = targetElement.getBoundingClientRect();
            console.log(`[Debug Scroll ${type} V2] After scrollIntoView, targetElement.getBoundingClientRect().top: ${rectAfterScroll.top}`);
            console.log(`[Debug Scroll ${type} V2] After scrollIntoView, window.scrollY: ${window.scrollY}`);

          } catch (e) {
            console.error(`[Debug Scroll ${type} V2] Error during scroll operation:`, e);
          } finally {
            // 确保在操作后清除样式
            requestAnimationFrame(() => {
              setTimeout(() => {
                if (targetElement && targetElement.style.scrollMarginTop) {
                  targetElement.style.scrollMarginTop = '';
                  console.log(`[Debug Scroll ${type} V2] Cleared scrollMarginTop from element ${targetElementId}`);
                }
              }, 0);
            });
          }
        }, 50); // 延迟以确保状态已更新

      } else {
        console.warn(`[Debug Scroll ${type} V2] Target element ${targetElementId} not found after DOM update`);
      }
    });
  }, [stickyHeaderRef, isHeaderSticky, isStickyDisabled]);

  /**
   * 创建滚动目标对象
   * @param {number} targetChunkIndex - 目标chunk索引
   * @param {number} [capturedLineNumber] - 捕获的行号
   * @param {boolean} [useOriginalIndex] - 是否使用原始索引
   * @returns {Object} 滚动目标对象
   */
  const createScrollTarget = useCallback((targetChunkIndex, capturedLineNumber = null, useOriginalIndex = false) => {
    return {
      targetChunkIndex,
      capturedLineNumber,
      useOriginalIndex
    };
  }, []);

  return {
    executeSmartScroll,
    createScrollTarget,
    calculateTargetPosition
  };
};
