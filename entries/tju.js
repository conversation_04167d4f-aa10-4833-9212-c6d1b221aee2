import {Req} from '../utils/index.js'
import {tsg90BaseUrl,tsg90BaseDomain,sjukuBaseDomain} from '../utils/constant.js'

const req=new Req()

export const label='天津大学';
export const name='tju'
const portalBaseUrl=`https://p.lib.tju.edu.cn/`;
const sBaseUrl=`https://a10.${sjukuBaseDomain}/`;

export const connectUrls=[
	portalBaseUrl,sBaseUrl,tsg90BaseUrl,
]


export async function checkLogin(html=''){

	const checkHtml=html=>!html?false:html.toString().includes(`<span class="am-icon-caret-down"></span>`);
	if(html){
		return checkHtml(html)
	}

	return checkHtml((await req(portalBaseUrl))['data'])
}


export async function login(){

	if(await checkLogin()){
		return 0;
	}

    const rsa=await req(`${sBaseUrl}ermsClient/browse.do`,{
        method:'POST',
        headers:{
            referer:tsg90BaseUrl,
            origin:`www.${tsg90BaseDomain}`,
        },
        body:`siteid=www.${tsg90BaseDomain}&uname=158273300&uid=9301`,
    })

    console.log(rsa['data'])

    if(await this.checkLogin(rsa['data'])){
    	return {
            code:200,

        };
    }

    return -1;
}