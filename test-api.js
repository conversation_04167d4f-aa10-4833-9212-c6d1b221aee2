#!/usr/bin/env node

/**
 * 测试API接口的脚本
 * 用于验证单独提交各个字段是否正常工作
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001';

// 测试数据
const testData = {
  title: '测试标题 - ' + Date.now(),
  summary: '这是一个测试总结',
  content: [
    { tag: 'h1', children: '测试标题', id: 0 },
    { tag: 'p', children: '这是测试内容', id: 1 }
  ],
  translated: [
    { tag: 'h1', children: 'Test Title', id: 0 },
    { tag: 'p', children: 'This is test content', id: 1 }
  ],
  refs: '这是参考文献内容',
  citation: '这是来源内容'
};

// 模拟登录获取cookie
async function login() {
  console.log('🔐 模拟登录...');
  // 由于我们使用的是JWT认证，这里我们需要一个有效的token
  // 在实际测试中，你需要先通过浏览器登录获取token
  // 这里我们跳过登录，直接使用现有的认证状态
  return '';
}

// 创建测试文章
async function createTestArticle(cookies) {
  console.log('📝 创建测试文章...');
  
  const response = await fetch(`${API_BASE}/api/articles/new`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies
    },
    credentials: 'include'
  });

  if (!response.ok) {
    throw new Error(`创建文章失败: ${response.status} ${response.statusText}`);
  }

  const result = await response.json();
  console.log('✅ 文章创建成功:', result.newArticleUuid);
  return result.newArticleUuid;
}

// 测试单独提交字段
async function testSingleFieldUpdate(articleUuid, fieldName, fieldValue, cookies) {
  console.log(`🧪 测试单独提交 ${fieldName}...`);
  
  const payload = { [fieldName]: fieldValue };
  
  const response = await fetch(`${API_BASE}/api/articles/${articleUuid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies
    },
    credentials: 'include',
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`❌ ${fieldName} 提交失败:`, response.status, errorText);
    return false;
  }

  const result = await response.json();
  console.log(`✅ ${fieldName} 提交成功`);
  return true;
}

// 主测试函数
async function runTests() {
  try {
    console.log('🚀 开始API测试...\n');

    // 1. 登录
    const cookies = await login();

    // 2. 创建测试文章
    const articleUuid = await createTestArticle(cookies);

    // 3. 测试各个字段的单独提交
    const tests = [
      ['title', testData.title],
      ['summary', testData.summary],
      ['content', testData.content],
      ['translated', testData.translated],
      ['refs', testData.refs],
      ['citation', testData.citation]
    ];

    let passedTests = 0;
    let totalTests = tests.length;

    for (const [fieldName, fieldValue] of tests) {
      const success = await testSingleFieldUpdate(articleUuid, fieldName, fieldValue, cookies);
      if (success) {
        passedTests++;
      }
      console.log(''); // 空行分隔
    }

    // 4. 输出测试结果
    console.log('📊 测试结果:');
    console.log(`✅ 通过: ${passedTests}/${totalTests}`);
    console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);

    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！');
    } else {
      console.log('⚠️ 部分测试失败，请检查服务端日志');
    }

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
  }
}

// 运行测试
runTests();
