import $ from 'jquery'
import {Req, contentParser} from '../utils/index.js'

const req = new Req()

// URL匹配模式 - 基于HTML中的URL结构

const realBaseUrl='https://ascopubs.org/';
export const match = [
    realBaseUrl+'doi/*',
    realBaseUrl+'doi/10.*',  // 更精确匹配DOI格式
    realBaseUrl+'doi/full/*', // 匹配full文章页面
    'http://ascopubs-org-s.zju.sjuku.top/doi/*',
]

// 允许连接的域名
export const connect = [
    'ascopubs.org',
    'cms.ascopubs.org',
    'sjuku.top',
]

// 截断词配置 - 定义在这些标题前截断内容
export const cut = [
    'Data Sharing Statement',
'Additional Resources',
]

export const getContent = async () => {
    console.log('=== ASCO解析器开始执行 ===')
    console.log('当前URL:', location.href)
    console.log('匹配的URL模式:', match)

    // 等待页面内容加载完成
    await new Promise(resolve => {
        if (document.readyState === 'complete') {
            resolve()
        } else {
            window.addEventListener('load', resolve)
        }
    })

    // 额外等待动态内容加载
    await new Promise(resolve => setTimeout(resolve, 3000))

    console.log('页面加载完成，开始解析内容')
    console.log('页面标题:', document.title)
    console.log('页面URL:', location.href)
    console.log('页面是否包含文章标题元素:', $('h1[property="name"]').length > 0)
    console.log('页面是否包含摘要元素:', $('#abstract').length > 0)
    console.log('页面是否包含主体内容元素:', $('#bodymatter').length > 0)
    console.log('主体内容区域的section数量:', $('#bodymatter section').length)
    console.log('主体内容区域的h2数量:', $('#bodymatter section h2').length)
    console.log('主体内容区域的h3数量:', $('#bodymatter section h3').length)

    const content = await contentParser([
        // 文章标题 - 基于HTML第1338行的h1[property="name"]，排除界面区域
        ['h1[property="name"]:not(.meta-panel *, .info-panel *, .core-nav-wrapper *, .collateral-pill *)', 'p'],

        // 摘要标题 - 基于HTML第1370行的#abstract h2
        ['#abstract h2[property="name"]', 'h1'],

        // 摘要子标题 - 基于HTML第1370行的#abstract section h3
        ['#abstract section h3', 'h2'],

        // 摘要段落 - 基于HTML第1370行的#abstract div[role="paragraph"]
        ['#abstract div[role="paragraph"]', 'p'],

        // 章节标题 - 只选择文章主体内容区域的标题，排除界面区域
        ['#bodymatter section h2:not(.meta-panel *, .info-panel *, .core-nav-wrapper *, .collateral-pill *)', 'h1'],

        // 子章节标题 - 只选择文章主体内容区域的子标题，排除界面区域
        ['#bodymatter section h3:not(.meta-panel *, .info-panel *, .core-nav-wrapper *, .collateral-pill *)', 'h2'],

        // 三级标题 - 只选择文章主体内容区域的三级标题，排除界面区域
        ['#bodymatter section h4:not(.meta-panel *, .info-panel *, .core-nav-wrapper *, .collateral-pill *)', 'h3'],

        // 正文段落 - 只选择文章主体内容区域的段落，排除界面区域和表格注释
        ['#bodymatter section div[role="paragraph"]:not(figure.table .notes div[role="paragraph"]):not(.meta-panel *, .info-panel *, .core-nav-wrapper *, .collateral-pill *)', 'p'],

        // 表格标题和描述 - 基于HTML第1300行的figure.table figcaption
        ['figure.table figcaption', 'figcaption'],

        // 表格内容 - 基于HTML第1300行的table元素
        ['figure.table table', 'table'],

        // 表格注释 - 基于HTML中的figure.table .notes内容，统一解析为figcaption
        // 优先匹配有标签的注释，提取标签并格式化
        ['figure.table .notes div[role="doc-footnote"][data-has="label"]', 'figcaption', (el) => {
            const labelEl = $(el).find('.label sup').first()
            const contentEl = $(el).find('div[role="paragraph"]').first()
            const label = labelEl.text().trim()
            const content = contentEl.text().trim()
            return { tag: 'figcaption', children: label ? `${label}. ${content}` : content }
        }],
        // 匹配没有标签的注释（直接文本，如缩写说明）
        ['figure.table .notes div[role="doc-footnote"]:not([data-has="label"])', 'figcaption'],

        // 图片标题和描述 - 统一用figcaption
        ['figure.graphic figcaption', 'figcaption'],

        // 图片 - 基于HTML第1789行的figure.graphic img，使用location.href融合URL
        ['figure.graphic img', 'img', (el) => {
            const src = $(el).attr('src')
            if (src && !src.startsWith('http')) {
                return { tag: 'img', src: new URL(src, realBaseUrl).href }
            }
            return { tag: 'img', src: src }
        }],

        // 列表项内容 - 基于HTML中的div[role="listitem"]
        ['div[role="listitem"] .content div[role="paragraph"]', 'p'],

        // 盒装文本内容 - 基于HTML中的figure.boxed-text
        ['figure.boxed-text h3', 'h2'],
        ['figure.boxed-text h4', 'h3'],
        ['figure.boxed-text div[role="paragraph"]', 'p']
    ])

    console.log('原始解析内容数量:', content.length)
    console.log('原始解析内容示例:', content.slice(0, 3))

    // 使用精确选择器后，只需要最小化的过滤
    const filteredContent = content.filter(item => {
        if (typeof item.children === 'string') {
            const text = item.children.trim()

            // 只排除纯数字（可能是页面统计）和空内容
            if (text.match(/^\d+$/) || text.length === 0) {
                return false
            }
        }
        return true
    })

    console.log('过滤后内容数量:', filteredContent.length)
    console.log('过滤后内容示例:', filteredContent.slice(0, 3))

    return filteredContent
}

// 参考文献提取 - 基于实际ASCO DOM结构
export const getRef = () => {
    // ASCO使用 #bibliography div[role="listitem"] 结构
    const refs = [...$('#bibliography div[role="listitem"]').map((i, el) => {
        const $el = $(el).clone()
        $el.find('a').remove() // 移除所有链接
        $el.find('.label').remove() // 移除编号标签
        const text = $el.text().trim().replace(/\s+/g, ' ')
            .replace(/CrossrefPubMedGoogle Scholar/gi, '')
            .replace(/CrossRef|PubMed|Google Scholar/gi, '')
            .replace(/\s+/g, ' ').trim()
        return text && text.length > 20 ? `[${i + 1}] ${text}` : null
    })].filter(Boolean)

    console.log('提取到参考文献数量:', refs.length)
    return refs
}

// 引用信息提取 - 基于HTML第1338行的引用信息
export const getCitation = () => {
    console.log('开始提取引用信息')

    // 尝试从文章信息部分提取引用
    const citation = $('.article_info').text().trim().replace(/\s+/g, ' ')
    console.log('从.article_info提取到的引用:', citation)
    if (citation) return citation

    // 备选方案：从meta标签提取
    const title = $('meta[property="og:title"]').attr('content') || $('title').text()
    const doi = $('meta[name="publication_doi"]').attr('content') ||
                $('.doi a').attr('href')?.replace('https://doi.org/', '')

    console.log('从meta标签提取到的标题:', title)
    console.log('从meta标签提取到的DOI:', doi)

    if (title && doi) {
        const finalCitation = `${title}. DOI: ${doi}`.replace(/\s+/g, ' ')
        console.log('最终引用信息:', finalCitation)
        return finalCitation
    }

    console.log('未能提取到引用信息')
    return ''
}

// 测试URL示例：
// https://ascopubs.org/doi/full/10.1200/JCO-24-02589
// http://ascopubs-org-s.zju.sjuku.top/doi/abs/10.1200/JCO-24-02834
// https://ascopubs.org/doi/10.1200/JCO.2025.43.16_suppl.5576