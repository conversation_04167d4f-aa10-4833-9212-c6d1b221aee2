/*
ScienceDirect 解析器
测试URL: https://www.sciencedirect.com/science/article/pii/S0923753425002017
*/
import $ from 'jquery'
import {contentParser} from '../utils/index.js'

// 图片统一基础URL
const imgBaseUrl = 'https://ars.els-cdn.com'

export const match = [
    'https://www.sciencedirect.com/science/article/*',
    'https://ars.els-cdn.com/*',
'https://poaktpgxscvunlguigifvixywtuyhdgrm-s.p.lib.tju.edu.cn/science/article/*',
]

export const connect = [
    'sciencedirect.com',
    'ars.els-cdn.com',
'tju.edu.cn',
]

// 截断词配置 - 在参考文献前截断
export const cut = [
    'References',
    'Acknowledgements',
    'Acknowledgments',
    'Data availability',
'Contributors',
'CRediT authorship contribution statement'
]

export const getCitation = () => {
    // 提取作者信息
    const authors = []
    $('.AuthorGroups .given-name, .AuthorGroups .surname').each((i, el) => {
        const text = $(el).text().trim()
        if (text) {
            if ($(el).hasClass('given-name')) {
                authors.push(text)
            } else if ($(el).hasClass('surname')) {
                const lastIndex = authors.length - 1
                if (lastIndex >= 0) {
                    // 格式化为 "姓氏 名字首字母" 格式
                    const givenName = authors[lastIndex]
                    const surname = text
                    authors[lastIndex] = `${surname} ${givenName}`
                }
            }
        }
    })

    // 格式化作者列表
    let authorString = ''
    if (authors.length > 0) {
        if (authors.length <= 6) {
            authorString = authors.join(', ')
        } else {
            authorString = authors.slice(0, 6).join(', ') + ', et al'
        }
    }

    // 提取文章标题
    const title = $('.title-text').text().trim()

    // 提取期刊名称
    const journal = $('.publication-title .anchor-text').text().trim() || 'Ann Oncol'

    // 提取发表信息
    const publicationInfo = $('.publication-volume .text-xs').first().text().trim()

    // 提取DOI
    const doi = $('.ArticleIdentifierLinks .doi .anchor-text').text().trim()

    // 构建引用格式
    let citation = ''

    if (authorString) {
        citation += authorString + '. '
    }

    if (title) {
        citation += title + '. '
    }

    if (journal) {
        citation += journal + '. '
    }

    if (publicationInfo && publicationInfo !== 'Available online 2 June 2025') {
        citation += publicationInfo + '. '
    }

    if (doi) {
        citation += `doi: ${doi.replace('https://doi.org/', '')}.`
    }

    return citation.trim()
}

export const getRef = () =>
    [...$('.bibliography .reference').map((i, el) => {
        // 获取参考文献文本，排除链接部分
        const $ref = $(el)

        // 移除链接元素（Crossref, Google Scholar等）
        const $clone = $ref.clone()
        $clone.find('.ReferenceLinks').remove()
        $clone.find('a').remove()

        // 获取纯文本内容
        let text = $clone.text().trim()

        // 将换行符替换为空格，并清理多余空格
        text = text.replace(/\n/g, ' ').replace(/\s+/g, ' ')

        return `[${i+1}] ${text}`
    })]

export const getContent = async () => {
    const content = await contentParser([
        // 文章标题
        ['article .Head .title-text', 'p'],

        // 主要章节标题 - Introduction, Methods, Results等
        ['article #body h2.u-h4', 'h1'],

        // 子章节标题
        ['article #body h3.u-h4', 'h2'],

        // 摘要部分的标题
        ['article .Abstracts h2.section-title', 'h1'],
        ['article .Abstracts h3.u-h4', 'h2'],

        // 摘要段落 - 优先处理，避免与其他选择器冲突
        ['article .Abstracts .u-margin-s-bottom:not(:has(ul.list)):not(:has(ol.list))', 'p'],

        // 文本框段落 - Research in context等特殊区域，只匹配文本框内的直接子元素
        ['article .article-textbox > .u-margin-s-bottom:not(:has(ul.list)):not(:has(ol.list))', 'p'],

        // 正文段落 - 排除图表标题、描述、列表容器、表格说明、下载链接、脚注、表格内容、摘要和文本框
        ['article #body .u-margin-s-bottom:not(.captions):not(figure .u-margin-s-bottom):not(.tables .captions):not(.legend .u-margin-s-bottom):not(.footnotes .u-margin-s-bottom):not(.tables .u-margin-s-bottom):not(:has(ul.list)):not(:has(ol.list)):not(:has(.download-link)):not(:has(table)):not(:has(.article-textbox)):not(.article-textbox .u-margin-s-bottom)', 'p'],

        // 其他段落 - 使用id="p0xxx"格式的段落，限制在article标签下
        ['article div[id^="p0"]:not(:has(figure)):not(:has(table))', 'p'],

        // 嵌套段落 - 在u-margin-s-bottom容器内的段落div
        ['article .u-margin-s-bottom > div[id^="para"]', 'p'],



        // 列表项内容 - 单独处理列表项
        ['article .list .list-content .u-margin-s-bottom', 'p'],

        // 图片 - 使用高分辨率下载链接
        ['article figure.figure img', 'img', (el) => {
            const src = $(el).attr('src')
            // 尝试获取高分辨率版本的下载链接
            const downloadLink = $(el).closest('figure').find('a[href*="_lrg.jpg"]').attr('href')
            const finalSrc = downloadLink || src

            // 统一使用imgBaseUrl作为基础URL
            let fullUrl
            if (finalSrc.startsWith('http')) {
                // 如果是完整URL，提取路径部分并使用统一的baseUrl
                const urlObj = new URL(finalSrc)
                fullUrl = imgBaseUrl + urlObj.pathname
            } else {
                // 如果是相对路径，直接拼接
                fullUrl = imgBaseUrl + (finalSrc.startsWith('/') ? finalSrc : '/' + finalSrc)
            }

            return { tag: 'img', src: fullUrl }
        }],

        // 图片标题和描述
        ['article figure.figure .captions', 'figcaption'],

        // 表格
        ['article .tables table', 'table', (el) => {
            const $tb = $(el).clone();

            // 先移除屏幕阅读器专用文本
            $tb.find('.screen-reader-only').remove();

            // 然后清理表格属性
            [...$tb.find('*')].map(el => [...el.attributes].forEach(attr => {
                if (!['rowspan', 'colspan'].includes(attr.name.toLowerCase())) el.removeAttribute(attr.name)
            }))

            return { tag: 'table', children: $tb.html() }
        }],

        // 表格标题
        ['article .tables .captions', 'figcaption'],

        // 表格说明 - Values are n (%) unless stated otherwise 等
        ['article .tables .legend .u-margin-s-bottom', 'figcaption'],

        // 表格和图片脚注 - 带编号的说明
        ['article .footnotes dd .u-margin-s-bottom', 'figcaption', (el) => {
            // 获取对应的编号
            const $dd = $(el).closest('dd')
            const $dt = $dd.prev('dt')
            const label = $dt.text().trim()
            const content = $(el).text().trim()

            return {
                tag: 'figcaption',
                children: `${label}. ${content}`
            }
        }]
    ])

    // 过滤掉明显的噪音内容
    return content.filter(item => {
        if (typeof item.children === 'string') {
            const text = item.children.trim()
            // 排除空内容、纯数字、纯符号等
            if (text.length === 0 ||
                text.match(/^[\d\s•·]+$/) ||
                text.match(/^[^\w\s]+$/) ||
                text.length <= 3) {
                return false
            }

            // 排除下载提示文本
            if (text.includes('Download') && text.includes('high-res') && text.includes('image')) {
                return false
            }

            return true
        }
        return true
    })
}
