/*
https://link.springer.com/article/10.1007/s12672-025-02205-y
https://link.springer.com/article/10.1186/s13148-022-01285-9
*/
import $ from 'jquery'
import {Req,contentParser,} from '../utils/index.js'

const req=new Req()

export const match=[
	'https://link.springer.com/article/*',
	'https://www.nature.com/*',
	'https://*.biomedcentral.com/*',
	'https://idp.springer.com/*',
	'https://media.springernature.com/*',
]

export const connect=[
	'media.springernature.com',
	'springer.com',
];

// 截断词配置 - 定义在这些标题前截断内容
export const cut = [
	'Availability of data and materials',
	'Data availability'
];

export const getCitation = () => $('.c-bibliographic-information__citation').text().trim().replace(/\n/g, ' ').replace(/\s+/g, ' ') ||
	$('.c-article-header').text().trim().replace(/\n/g, ' ').replace(/\s+/g, ' ')

export const getRef=()=>[...$('.c-article-references__text').map((i,el)=>`[${i+1}] ${el.innerText.trim()}`)]

export const getContent=async ()=>{

	const content=(await contentParser([
		['.c-article-title','p',],
		['.c-article-section__title','h1',],
		['.c-article__sub-heading','h2',],
		['.c-article-section__content>p','p',],
		['.c-article-section__figure-caption','strong',],
		['.c-article-section__figure-link img','img',async el=>{
			return {tag:'img',src:$(el).prop('src')}
			const sourceSrc=$(el).prop('src').replace('/lw685/','/full/')
			const resp=await req(sourceSrc)
			return {tag:'img',src:resp['data']}
		}],
		['.c-article-table__figcaption','strong',],
		['.c-article-section__figure-description','figcaption',],
		['.c-article-table a.c-article__pill-button','table',async el=>{
			const resp=await req($(el).prop('href'))
			const $t=$($.parseHTML(resp['data']))
			const $table=$t.find('table.data');
			[...$table.find('*')].map(el=>[...el.attributes].forEach(attr=>{
				if(!['rowspan','colspan'].includes(attr.name.toLowerCase()))el.removeAttribute(attr.name)
			}))
			const children=$table.html();
			const footer=$t.find('.c-article-table-footer').text();

			return [
				{tag:'table',children,},
				{tag:'p',children:footer},
			]
		}],
	]))

	return content;
}