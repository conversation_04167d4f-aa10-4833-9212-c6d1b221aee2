import $ from 'jquery'
import { Req, contentParser } from '../utils/index.js'

const req = new Req()

export const match = [
	'https://www.biorxiv.org/content/*',
	'https://biorxiv.org/content/*',
	'https://www.biorxiv.org/highwire/markup/*',
	'https://biorxiv.org/highwire/markup/*'
]

export const connect = [
	'biorxiv.org',
	'www.biorxiv.org'
]

export const getContent = async () => {
	const content = await contentParser([
		// 文章标题 - 基于HTML第341行的.highwire-cite-title
		['.highwire-cite-title', 'p'],

		// 章节标题 - 基于HTML第374行开始的.section h2
		['.section h2', 'h1'],

		// 子章节标题 - 基于HTML第375行的.subsection h3
		['.subsection h3', 'h2'],

		// 正文段落 - 基于HTML第374行开始的.section p
		['.section p', 'p'],

		// 图片标题 - 基于HTML第399行的.fig-label
		['.fig-label', 'strong'],

		// 图片描述 - 基于HTML第399行的.fig-caption p
		['.fig-caption p:not(.fig-label)', 'figcaption'],

		// 图片 - 选择 "Open in new tab" 链接（没有查询参数的直接链接）
		['a[href*=".large.jpg"]:not([href*="?"])', 'img', (el) => {
			const href = $(el).attr('href');
			if (href) {
				const fullUrl = href.startsWith('http') ? href : new URL(href, location.href).href;
				return { tag: 'img', src: fullUrl };
			}
			return null;
		}],

		// 表格标题 - 基于HTML第383行的.table-label
		['.table-label', 'strong'],

		// 表格描述 - 基于HTML第383行的.table-caption p
		['.table-caption p:not(.table-label)', 'figcaption'],

		// 表格链接 - 处理 "View popup" 链接
		['a[href*="/highwire/markup/"]', null, async (el) => {
			const href = $(el).attr('href');
			const url = href.startsWith('http') ? href : new URL(href, location.href).href;
			const resp = await req(url);
			const urls = new Set(); // 用Set去重

			// 方法1：直接提取 .large.jpg 的URL
			const largeMatches = resp.data.match(/https:\/\/[^"'\s]+\.large\.jpg/g);
			if (largeMatches) {
				largeMatches.forEach(url => urls.add(url));
			}

			// 方法2：提取 .medium.gif 并替换为 .large.jpg
			const mediumMatches = resp.data.match(/https:\/\/[^"'\s]+\.medium\.gif/g);
			if (mediumMatches) {
				mediumMatches.forEach(url => {
					const largeUrl = url.replace('.medium.gif', '.large.jpg');
					urls.add(largeUrl);
				});
			}

			// 转换为结果数组
			const result = [];
			urls.forEach(url => {
				result.push({ tag: 'img', src: url });
			});
			return result;
		}],


	])

	// 截断逻辑 - 在参考文献章节前截断
	const cutI = content.findIndex(item =>
		item.tag == 'h1' &&
		item.children &&
		item.children.toLowerCase().includes('reference')
	)
	if (cutI > 0) return content.slice(0, cutI)

	return content
}

export const getCitation = () => $('.highwire-citation-export .highwire-cite').text().trim().replace(/\n/g, ' ').replace(/\s+/g, ' ')

export const getRef = () =>
	// 基于HTML第715行开始的.ref-list .cit结构
	[...$('.ref-list .cit').map((i, el) => `[${i+1}] ${$(el).text().trim()}`)].filter(ref => ref.length > 10)

// 测试URL:
// https://www.biorxiv.org/content/10.1101/2022.10.12.511895v1.full