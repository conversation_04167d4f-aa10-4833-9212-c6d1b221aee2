# 解析器开发指南

本指南教你如何为学术网站编写解析器，提取文章标题、作者、摘要、章节、段落、图表、表格和参考文献等结构化内容。

> ⭐ **核心原则**: 好的解析器依赖精确的CSS选择器，而不是大量的后期过滤规则。如果你需要超过5行过滤规则，说明选择器不够精确！

## 快速开始

### 1. 基本文件结构
```javascript
// parsers/mysite.js
import $ from 'jquery'
import {Req, contentParser} from '../util.js'

const req = new Req()

export const match = ['https://mysite.com/articles/*']
export const connect = ['mysite.com']

// 截断配置（可选）- 优先使用数组
export const cut = [
    'References',           // 在参考文献前截断
    'Acknowledgments',      // 在致谢前截断
    'Data Sharing Statement' // 在数据共享声明前截断
]

// 或者使用函数（复杂逻辑时）
// export const cut = (content) => {
//     const cutI = content.findIndex(item =>
//         item.tag === 'h1' && item.children.includes('References'))
//     return cutI > 0 ? content.slice(0, cutI) : content
// }

export const getContent = async () => {
    const content = await contentParser([
        ['.article-title', 'p'],           // 文章标题
        ['.section-title', 'h1'],          // 章标题
        ['.subsection-title', 'h2'],       // 节标题
        ['.content p', 'p'],               // 正文段落
        ['.figure-caption', 'strong'],     // 图片标题
        ['.figure-description', 'figcaption'], // 图片描述
        ['img', 'img'],                    // 图片
        ['table', 'table'],                // 表格
    ])

    // 截断逻辑由 userscript 统一处理，这里直接返回
    return content
}

export const getRef = () =>
    [...$('.references li').map((i,el) => `[${i+1}] ${el.innerText.trim()}`)]

export const getCitation = () =>
    $('.citation-text').text().trim().replace(/\n/g, ' ')
```

## 核心工具

### 2. match 和 connect 配置
```javascript
// 必须导出这两个数组
export const match = [
    'https://mysite.com/articles/*',  // 脚本激活的URL模式
    'https://mysite.com/papers/*',
    'https://mysite.com/api/*'        // 如果asyncFn需要请求其他路径
]

export const connect = [
    'mysite.com',           // 允许请求的域名
    'cdn.mysite.com'        // 如果需要请求其他域名
]
```

#### 重要：asyncFn 请求的URL必须在 match 中
如果你的 asyncFn 需要请求其他页面，这些URL必须包含在 `match` 数组中：

```javascript
// ❌ 错误：asyncFn 请求的URL不在 match 中
export const match = ['https://example.com/articles/*']
// 这会导致 "URL needs to be included by @match tag" 错误
['.table-link', 'table', async (el) => {
    const resp = await req('https://example.com/tables/123')  // 不在 match 中！
}]

// ✅ 正确：包含所有可能的URL模式
export const match = [
    'https://example.com/articles/*',
    'https://example.com/tables/*'     // 添加表格页面的URL模式
]
```

### 3. Req 工具（异步请求）

Req 是一个强大的HTTP请求工具，专为解析器设计，支持Cookie管理、重定向处理和多种内容类型。

#### 基本用法
```javascript
import { Req } from '../utils/index.js'
const req = new Req()

// 基本请求
const resp = await req('https://example.com/page')
console.log(resp.data)  // 响应内容
```

#### 响应对象结构
```javascript
const resp = await req('https://example.com')
// resp 包含以下属性：
// - resp.data: 响应内容（HTML字符串、图片base64等）
// - resp.headers: 响应头对象
// - resp.status: HTTP状态码
// - resp.ok: 请求是否成功（布尔值）
```

#### 内容类型自动处理
```javascript
// HTML页面 - 自动解析为字符串
const htmlResp = await req('https://example.com/page.html')
const $page = $(htmlResp.data)  // 可直接用jQuery解析

// 图片 - 自动转换为base64
const imgResp = await req('https://example.com/image.jpg')
// imgResp.data 是 "data:image/jpeg;base64,..." 格式

// 其他类型 - 返回原始响应
```

#### 请求选项
```javascript
// GET请求（默认）
const resp1 = await req('https://example.com')

// POST请求
const resp2 = await req('https://example.com/api', {
    method: 'POST',
    body: JSON.stringify({key: 'value'}),
    headers: {
        'Content-Type': 'application/json'
    }
})

// 自定义头部
const resp3 = await req('https://example.com', {
    headers: {
        'User-Agent': 'Custom Agent',
        'Referer': 'https://example.com'
    }
})
```

#### Cookie 和会话管理
```javascript
// Req 自动管理Cookie，支持跨请求会话保持
const req = new Req()

// 第一个请求可能设置Cookie
await req('https://example.com/login')

// 后续请求自动携带Cookie
await req('https://example.com/protected-page')  // 自动带上登录Cookie
```

#### 重定向处理
```javascript
// 自动处理重定向（最多8次）
const resp = await req('https://example.com/redirect-url')
// 自动跟随重定向到最终页面
```

#### 错误处理
```javascript
try {
    const resp = await req('https://example.com/page')
    if (resp.ok) {
        // 请求成功
        const $page = $(resp.data)
    } else {
        console.error('请求失败:', resp.status)
    }
} catch (error) {
    console.error('网络错误:', error)
}
```

#### 在 asyncFn 中的典型用法
```javascript
// 表格请求示例
['.table-link', 'table', async (el) => {
    const href = $(el).attr('href')
    const resp = await req(href)
    const $page = $(resp.data)
    return { tag: 'table', children: $page.find('table').html() }
}],

// 图片请求示例
['.image-link', 'img', async (el) => {
    const src = $(el).attr('href')
    const resp = await req(src)
    return { tag: 'img', src: resp.data }  // base64格式
}],

// 图片URL处理示例
['img', 'img', (el) => {
    const src = $(el).attr('src')
    const fullUrl = src.startsWith('http') ? src : new URL(src, location.href).href
    return { tag: 'img', src: fullUrl }
}],

// 多内容请求示例
['.popup-link', null, async (el) => {
    const href = $(el).attr('href')
    const resp = await req(href)
    const $page = $(resp.data)

    return [
        { tag: 'table', children: $page.find('table').html() },
        { tag: 'figcaption', children: $page.find('.caption').text() }
    ]
}]
```

#### 注意事项
- Req 实例会自动管理Cookie存储
- 支持相对URL和绝对URL
- 自动处理常见的重定向场景
- 图片请求返回base64格式，可直接用作img src
- HTML请求返回字符串，可用jQuery解析

### 4. contentParser（内容解析器）

**格式**: `[selector, targetTag, asyncFn]`

```javascript
const content = await contentParser([
    // 基本用法：[选择器, 目标标签]
    ['.article-title', 'p'],           // 提取文本，包装为p标签
    ['img', 'img'],                    // 提取图片src属性
    ['table', 'table'],                // 提取表格HTML

    // 高级用法：使用asyncFn
    ['.table-link', 'table', async (el) => {
        const resp = await req($(el).attr('href'))
        return { tag: 'table', children: $(resp.data).find('table').html() }
    }],

    // 返回数组：targetTag设为null
    ['.complex-element', null, (el) => [
        { tag: 'strong', children: $(el).find('.title').text() },
        { tag: 'figcaption', children: $(el).find('.desc').text() }
    ]],

    // 图片URL处理：返回正确的img格式
    ['img', 'img', (el) => {
        const src = $(el).attr('src')
        const fullUrl = src.startsWith('http') ? src : new URL(src, location.href).href
        return { tag: 'img', src: fullUrl }
    }]
])
```

#### AsyncFn返回值格式重要说明

**对于不同标签类型，asyncFn的返回格式不同**：

```javascript
// ✅ 正确：img标签返回 {tag: 'img', src: 'URL'}
['img', 'img', (el) => {
    const src = $(el).attr('src')
    return { tag: 'img', src: new URL(src, location.href).href }
}]

// ✅ 正确：其他标签返回 {tag: 'tagName', children: 'content'}
['.title', 'p', (el) => {
    return { tag: 'p', children: $(el).text().trim() }
}]

// ✅ 正确：返回数组时targetTag设为null
['.complex', null, (el) => [
    { tag: 'strong', children: 'title' },
    { tag: 'figcaption', children: 'description' }
]]

// ❌ 错误：img标签不要用children
// ['img', 'img', (el) => ({ tag: 'img', children: 'URL' })]
```


## 标签使用规定

**严格遵守，不允许例外：**

| 标签 | 用途 | 示例 |
|------|------|------|
| **h1** | 章标题 | Introduction, Methods, Results |
| **h2** | 节标题 | 章节下的子标题 |
| **h3** | 三级标题 | 节标题下的子标题 |
| **p** | 所有其他文本 | 文章标题、作者、摘要、正文段落 |
| **strong** | 图片/表格标题 | 仅当同时存在标题和描述时使用 |
| **figcaption** | 图片/表格描述 | 图表说明文字，或单独的标题/描述 |
| **img** | 图片 | 只返回src，**图片形式的表格也用img** |
| **table** | HTML表格 | 真正的HTML表格（非图片） |

**禁止使用**: div标签及其他未列出的标签

### 标题层级说明

标题层级应该按照学术文章的逻辑结构进行映射：

```
学术文章结构          →    HTML标签
─────────────────────────────────────
章标题 (Chapter)      →    h1
节标题 (Section)      →    h2
三级标题 (Subsection) →    h3
```

**示例映射**：
```javascript
// ASCO文章的标题层级映射
['section h2', 'h1'],           // "Introduction", "Methods" → h1
['section h3', 'h2'],           // "Guideline Development Process" → h2
['section h4', 'h3'],           // "Target Population", "Target Audience" → h3
```

**注意事项**：
- 文章标题本身使用 `p` 标签，不是 `h1`
- 摘要标题通常映射为 `h1`
- 保持层级的逻辑性和一致性

### 图片/表格标题和描述处理规则

图片和表格的标题与描述应根据实际情况灵活处理：

**规则1：标题和描述在同一行时统一处理**
```javascript
// 标题和描述在同一行时，统一使用figcaption
['figure figcaption', 'figcaption']
```

**规则2：只有同时存在独立的标题和描述时才分别处理**
```javascript
// 只有当标题和描述是独立的元素时，才分别使用strong和figcaption
// 这种情况比较少见，大多数情况下应该使用规则1
['figure .title', 'strong'],           // 独立的标题元素
['figure .description', 'figcaption']  // 独立的描述元素
```

**处理逻辑**：
- **标题和描述在同一行**：统一使用 `figcaption`
- **只有标题**：使用 `figcaption`
- **只有描述**：使用 `figcaption`
- **独立的标题和描述元素**：标题用 `strong`，描述用 `figcaption`

**示例**：
```
输入: "Fig 1. Algorithm. aOptions include bevacizumab..."
输出: <figcaption>Fig 1. Algorithm. aOptions include bevacizumab...</figcaption>

输入: "Table 1. Patient characteristics"
输出: <figcaption>Table 1. Patient characteristics</figcaption>

输入: <div class="title">Table 1</div> + <div class="desc">Patient characteristics</div>
输出: <strong>Table 1</strong>
      <figcaption>Patient characteristics</figcaption>
```

**重要提醒**：
- 大多数情况下应该使用规则1，统一用figcaption
- 只有在HTML结构中标题和描述是完全独立的元素时，才考虑分别处理


## 核心设计原则

### 1. 选择器特异性原则 ⭐ **重要**

**好的解析器应该依赖精确的CSS选择器，而不是大量的后期过滤规则。**

#### ✅ 正确做法：精确选择器
```javascript
// 利用网站的语义化结构，使用精确的选择器
export const getContent = async () => {
    const content = await contentParser([
        // 精确定位文章内容区域
        ['section:not(#abstract) div[role="paragraph"]:not(figure.table .notes div[role="paragraph"])', 'p'],
        ['figure.table .notes div[role="doc-footnote"][data-has="label"]', 'figcaption'],
        ['#abstract div[role="paragraph"]', 'p'],
        ['section:not(#abstract) h2', 'h1']
    ])

    // 最小化过滤：只排除明显的噪音
    return content.filter(item => {
        if (typeof item.children === 'string') {
            const text = item.children.trim()
            return !text.match(/^\d+$/) // 只排除纯数字
        }
        return true
    })
}
```

#### ❌ 错误做法：宽泛选择器 + 大量过滤
```javascript
// 不要这样做：使用宽泛选择器然后用大量规则过滤
export const getContent = async () => {
    const content = await contentParser([
        ['div', 'p'],  // 太宽泛！
        ['p', 'p'],    // 会匹配所有段落
        ['span', 'p']  // 包括界面元素
    ])

    // 39行过滤规则 - 这是过度防御的信号！
    return content.filter(item => {
        if (typeof item.children === 'string') {
            const text = item.children.toLowerCase().trim()
            if (text.includes('total downloads') ||
                text.includes('total citations') ||
                text.includes('article metrics') ||
                text.includes('view options') ||
                text.includes('download pdf') ||
                text.includes('view pdf') ||
                text.includes('share article') ||
                text.includes('copy link') ||
                text.includes('subscribe') ||
                // ... 还有30多行过滤规则
                ) {
                return false
            }
        }
        return true
    })
}
```

#### 🎯 判断标准
**如果你的解析器需要超过5行的过滤规则，说明选择器不够精确！**

- ✅ **好的解析器**：90%以上的匹配都是正确内容
- ❌ **差的解析器**：需要大量过滤规则排除噪音

#### 📊 实际测试案例（ASCO解析器）
```javascript
// 测试结果显示选择器精确性很高：
// 正文段落选择器：44个元素中只有1个可疑元素（2.3%噪音率）
// 标题选择器：完全干净，0个可疑元素
// 摘要选择器：完全干净，0个可疑元素

// 结论：学术期刊网站的语义化做得很好，应该信任并利用这些结构
```

#### 💡 优化策略
1. **研究HTML结构**：学术网站通常有良好的语义化标记
2. **利用属性选择器**：`[role="paragraph"]`、`[data-has="label"]` 等
3. **使用结构选择器**：`:not()`、`:has()` 等排除特定区域
4. **测试选择器精确性**：检查匹配结果的噪音比例
5. **最小化过滤**：只排除明显的统计数字等噪音

#### 🔍 经验教训
- **ASCO网站特异性很好**：作为学术期刊网站，HTML结构规范
- **过度防御没必要**：39行过滤规则可以简化为3行
- **精确选择器胜过复杂过滤**：这是解析器设计的核心原则

### 2. 优先级：直接定位 > asyncFn
```javascript
// ✅ 首选：直接定位
['.article-title', 'p'],
['.figure-caption', 'strong'],
['.figure-description', 'figcaption'],
['table', 'table'],
['.table-image img', 'img'],  // 图片形式的表格

// ⚠️ 只有实在没办法才用asyncFn：
// - 内容在另一个页面
// - 需要修改src路径
// - 需要复杂处理
```

### 3. 何时返回数组
```javascript
// ✅ 必要：内容在不同页面，一次请求获取多个片段
['.table-link', null, async (el) => {
    const resp = await req($(el).attr('href'))
    const $page = $(resp.data)
    return [
        { tag: 'table', children: $page.find('table').html() },
        { tag: 'figcaption', children: $page.find('.desc').text() }
    ]
}]

// ❌ 错误：内容在同一页面却返回数组
// ✅ 正确：分别直接定位
['.container .title', 'strong'],
['.container .desc', 'figcaption']
```

### 4. 截断逻辑配置（推荐）

**新的统一截断方式**：通过导出 `cut` 配置定义截断逻辑，由 userscript 统一处理。支持数组和函数两种方式。

#### 方式1：数组配置（推荐，优先使用）

```javascript
// 导出截断词配置 - 简单易用
export const cut = [
    'Data Sharing Statement',           // ASCO 文章截断点
    'Availability of data and materials', // Springer 文章截断点
    'Data availability',                // Springer 备选截断点
    'References',                       // 通用参考文献截断点
    'Acknowledgments'                   // 通用致谢截断点
]
```

#### 方式2：函数配置（复杂逻辑时使用）

```javascript
// 导出截断函数 - 用于复杂截断逻辑
export const cut = (content) => {
    // 自定义截断逻辑
    const cutI = content.findIndex(item => {
        if (item.tag === 'h1') {
            // 复杂的匹配条件
            return item.children.includes('References') ||
                   item.children.includes('Data Sharing') ||
                   item.children.match(/^(Acknowledgment|Funding)/i)
        }
        return false
    })

    return cutI > 0 ? content.slice(0, cutI) : content
}

// 也支持异步函数
export const cut = async (content) => {
    // 可以进行异步操作
    const shouldCut = await someAsyncCheck()
    if (!shouldCut) return content

    // 执行截断逻辑
    return content.filter(item => !item.children.includes('unwanted'))
}
```

#### 使用原则

- **优先使用数组**：能用数组解决的截断需求就用数组
- **复杂逻辑用函数**：只有在需要复杂匹配、条件判断或异步操作时才使用函数
- **函数输入输出**：函数接收 `content` 数组，返回处理后的 `content` 数组

#### getContent 函数简化

```javascript
// getContent 函数中不再需要截断逻辑
export const getContent = async () => {
    const content = await contentParser([
        // ... 解析规则
    ])

    // 直接返回，截断由 userscript 统一处理
    return content
}
```

**优势**：
- 统一管理：所有解析器的截断逻辑在 userscript 中统一处理
- 灵活配置：支持简单数组和复杂函数两种方式
- 易于维护：修改截断逻辑不需要改动每个解析器
- 向后兼容：没有 `cut` 配置的解析器仍然正常工作

## 解析器文件结构和最佳实践

一致的结构和对最佳实践的遵守将使解析器更易于维护和理解。

### 推荐文件结构

每个解析器都应该是一个 JavaScript 模块（例如 `mywebsite.js`），导出几个关键函数和配置：

```javascript
// parsers/springer.js (基于实际实现)

import $ from 'jquery'
import {Req,contentParser,} from '../utils/index.js'

const req=new Req()

// 1. URL 匹配 (基于实际 springer.js)
export const match=[
    'https://link.springer.com/article/*',
    'https://www.nature.com/*',
    'https://*.biomedcentral.com/*',
    'https://idp.springer.com/*',
    'https://media.springernature.com/*',
]

// 2. 连接配置 (基于实际 springer.js)
export const connect=[
    'media.springernature.com',
    'springer.com',
];

// 3. 截断词配置 (基于实际 springer.js)
export const cut = [
    'Availability of data and materials',
    'Data availability'
];

// 4. 主要内容提取 (基于实际 springer.js 实现)
export const getContent=async ()=>{

    // 基于springer.js的实际解析规则
    const content=(await contentParser([
        ['.c-article-title','p',], // 文章标题使用p标签
        ['.c-article-section__title','h1',], // 章标题使用h1标签
        ['.c-article__sub-heading','h2',], // 节标题使用h2标签
        ['.c-article-section__content>p','p',], // 正文段落使用p标签
        ['.c-article-section__figure-caption','strong',], // 图片标题使用strong标签
        ['.c-article-section__figure-link img','img',async el=>{
            return {tag:'img',src:$(el).prop('src')} // 图片返回正确的格式
            // 注释掉的代码：原本计划修改src
            // const sourceSrc=$(el).prop('src').replace('/lw685/','/full/')
            // const resp=await req(sourceSrc)
            // return {tag:'img',src:resp['data']}
        }],
        ['.c-article-table__figcaption','strong',], // 表格标题使用strong标签
        ['.c-article-section__figure-description','figcaption',], // 图片描述使用figcaption标签
        ['.c-article-table a.c-article__pill-button','table',async el=>{
            const resp=await req($(el).prop('href'))
            const $t=$($.parseHTML(resp['data']))
            const $table=$t.find('table.data');
            [...$table.find('*')].map(el=>[...el.attributes].forEach(attr=>{
                if(!['rowspan','colspan'].includes(attr.name.toLowerCase()))el.removeAttribute(attr.name)
            }))
            const children=$table.html();
            const footer=$t.find('.c-article-table-footer').text();

            return [
                {tag:'table',children,},
                {tag:'p',children:footer},
            ]
        }],
    ]))

    // 截断逻辑由 userscript 统一处理，这里直接返回

    // 可选: 后期处理，例如，根据特定标题（如“参考文献”）截断内容数组。


    return content;
}

// 4. 参考文献提取 (基于实际 springer.js 实现)
export const getRef=()=>[...$('.c-article-references__text').map((i,el)=>`[${i+1}] ${el.innerText.trim()}`)]

// 5. 引用信息提取
export const getCitation = () =>
    $('.citation-text').text().trim().replace(/\n/g, ' ').replace(/\s+/g, ' ')

// 6. 其他特定提取函数 (可选)
// export async function getAuthors() { /* ... */ }
// export async function getAbstract() { /* ... */ }

// 5. 测试 URL (基于实际 springer.js 注释中的示例)
// 测试 URL 示例：
// https://link.springer.com/article/10.1007/s12672-025-02205-y
// https://link.springer.com/article/10.1186/s13148-022-01285-9
```

## 开发步骤

1. **分析网站结构**: 用浏览器开发者工具检查HTML结构
2. **创建解析器文件**: 在`parsers/`目录下创建`mysite.js`
3. **定义基本配置**: 设置`match`和`connect`数组
4. **实现内容提取**: 使用`contentParser`定义提取规则
5. **添加参考文献**: 实现`getRef`函数
6. **测试和优化**: 使用测试工具验证选择器精确性

## 测试工具

项目提供了专门的测试工具来验证解析器的选择器精确性和内容提取质量。

### 使用方法

```bash
# 测试指定解析器
node tests/parser.js <parser_name>

# 示例
node tests/parser.js asco
node tests/parser.js springer
```

### 准备测试数据

在测试解析器之前，需要准备HTML测试文件：

1. **访问目标网站**：打开要解析的文章页面
2. **保存HTML**：右键 → "查看页面源代码" → 全选复制 → 保存为 `parsers/<parser_name>.html`
3. **运行测试**：`node tests/parser.js <parser_name>`

### 测试报告解读

测试工具会生成详细的分析报告：

#### 1. 选择器精确性测试
```
1. 选择器: "section:not(#abstract) div[role="paragraph"]"
   匹配元素数量: 44
   可疑元素数量: 1/10
   选择器精确性: 90.0%
   ✅ 选择器精确性很高
```

**评估标准**：
- ✅ **90%以上**：选择器精确性很高
- ⚠️ **70-90%**：精确性中等，建议优化
- ❌ **70%以下**：精确性较低，需要重新设计

#### 2. 内容提取测试
```
提取到 67 个内容项

内容类型分布:
  p: 45 个
  h1: 12 个
  figcaption: 7 个
  table: 2 个
  img: 1 个

可疑内容比例: 3.0%
✅ 可疑内容比例较低，选择器精确性良好
```

**评估标准**：
- ✅ **5%以下**：精确性良好
- ⚠️ **5-10%**：可以考虑优化
- ❌ **10%以上**：需要优化选择器

#### 3. 表格注释结构测试
```
注释 1:
HTML结构: <div role="doc-footnote" data-has="label">...
文本内容: aAssessed in patients with measurable disease...
  上标 1: a
  标签属性: data-has="label"
```

帮助理解复杂结构的HTML组织方式。

### 测试驱动开发

推荐的开发流程：

1. **创建基础解析器**：实现基本的选择器
2. **运行测试**：`node tests/parser.js mysite`
3. **分析报告**：查看精确性和可疑内容
4. **优化选择器**：根据测试结果调整选择器
5. **重复测试**：直到精确性达到90%以上

### 示例：优化过程

```bash
# 第一次测试 - 发现问题
$ node tests/parser.js asco
选择器精确性: 60.0%
❌ 选择器精确性较低，需要重新设计

# 优化选择器后再次测试
$ node tests/parser.js asco
选择器精确性: 95.0%
✅ 选择器精确性很高
```

这个测试工具帮助你：
- **量化选择器质量**：用数据说话，不凭感觉
- **发现潜在问题**：提前发现可疑内容
- **指导优化方向**：明确知道哪里需要改进
- **验证改进效果**：优化后立即看到效果

## 常见问题

**Q: 我的解析器需要很多过滤规则，这正常吗？**
A: **不正常！** 如果需要超过5行过滤规则，说明选择器不够精确。学术网站通常有良好的语义化结构，应该利用这些结构而不是依赖大量过滤。

```javascript
// ❌ 错误信号：需要大量过滤
const filteredContent = content.filter(item => {
    const text = item.children.toLowerCase().trim()
    if (text.includes('total downloads') ||
        text.includes('total citations') ||
        text.includes('article metrics') ||
        // ... 还有30多行过滤规则) {
        return false
    }
    return true
})

// ✅ 正确做法：精确选择器 + 最小过滤
['section:not(#abstract) div[role="paragraph"]:not(figure.table .notes div[role="paragraph"])', 'p']
// 只需要排除纯数字等明显噪音
return content.filter(item => !item.children.trim().match(/^\d+$/))
```

**Q: 什么时候用asyncFn？**
A: 只有在内容在另一个页面、需要修改属性、或需要复杂处理时才用。

**Q: 什么时候返回数组？**
A: 只有在一次异步请求获取多个不同类型内容时才返回数组。

**Q: 图片形式的表格怎么处理？**
A: 使用`img`标签而不是`table`标签：`['.table-image img', 'img']`

**Q: 如何配置截断逻辑？**
A: 使用新的统一截断方式，支持数组和函数两种配置：

```javascript
// 方式1：数组配置（推荐，优先使用）
export const cut = [
    'Data Sharing Statement',
    'References',
    'Acknowledgments'
]

// 方式2：函数配置（复杂逻辑时使用）
export const cut = (content) => {
    // 自定义截断逻辑
    const cutI = content.findIndex(item =>
        item.tag === 'h1' && item.children.match(/^(References|Funding)/i)
    )
    return cutI > 0 ? content.slice(0, cutI) : content
}

// 方式3：异步函数配置
export const cut = async (content) => {
    // 可以进行异步操作
    return content.filter(item => !item.children.includes('unwanted'))
}

// getContent 中不需要截断逻辑，由 userscript 统一处理
export const getContent = async () => {
    const content = await contentParser([...])
    return content  // 直接返回，截断由 userscript 处理
}
```

**使用原则**：数组优先，能用数组不用函数。只有在需要复杂匹配、条件判断或异步操作时才使用函数。

**Q: Req 请求失败怎么办？**
A: 使用 try-catch 处理错误，检查 resp.ok 状态：
```javascript
try {
    const resp = await req(url)
    if (!resp.ok) {
        console.error('请求失败:', resp.status)
        return null
    }
    // 处理成功响应
} catch (error) {
    console.error('网络错误:', error)
    return null
}
```

**Q: 如何处理相对URL？**
A: Req 支持相对URL，但建议构建完整URL：
```javascript
const href = $(el).attr('href')
const fullUrl = href.startsWith('http') ? href : 'https://example.com' + href
const resp = await req(fullUrl)
```

**Q: Cookie 会自动保存吗？**
A: 是的，Req 实例会自动管理Cookie，支持跨请求会话保持。

**Q: 如何获取图片的base64数据？**
A: 直接请求图片URL，Req 会自动转换：
```javascript
const resp = await req('https://example.com/image.jpg')
// resp.data 是 "data:image/jpeg;base64,..." 格式
return { tag: 'img', src: resp.data }
```

**Q: 如何实现 getCitation 功能？**
A: 直接从页面的引用工具提取，确保单行输出并清理多余空格：
```javascript
// 基本实现（推荐）
export const getCitation = () =>
    $('.citation-text').text().trim().replace(/\n/g, ' ').replace(/\s+/g, ' ')

// 带备选方案
export const getCitation = () =>
    $('.c-bibliographic-information__citation').text().trim().replace(/\n/g, ' ').replace(/\s+/g, ' ') ||
    $('.c-article-header').text().trim().replace(/\n/g, ' ').replace(/\s+/g, ' ')

// biorxiv 示例（一行式）
export const getCitation = () =>
    $('.highwire-citation-export .highwire-cite').text().trim().replace(/\n/g, ' ').replace(/\s+/g, ' ')
```

**Q: 出现 "URL needs to be included by @match tag" 错误怎么办？**
A: 这表示 asyncFn 请求的URL不在 `match` 数组中，需要添加：
```javascript
// 添加 asyncFn 可能请求的所有URL模式
export const match = [
    'https://example.com/articles/*',    // 主页面
    'https://example.com/tables/*',      // 表格弹窗页面
    'https://example.com/images/*'       // 图片页面
]
```