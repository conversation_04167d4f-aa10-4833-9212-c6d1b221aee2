/**
 * 解析器测试工具
 * 用于测试和验证解析器的选择器精确性
 *
 * 使用方法：
 * node tests/parser.js <parser_name>
 *
 * 示例：
 * node tests/parser.js asco
 * node tests/parser.js springer
 */

import { JSDOM } from 'jsdom'
import fs from 'fs'
import jquery from 'jquery'

// 设置全局环境
const setupGlobalEnvironment = async (html) => {
    const dom = new JSDOM(html, {
        url: 'https://example.com',
        pretendToBeVisual: false,
        resources: 'usable'
    })

    global.window = dom.window
    global.document = dom.window.document
    global.location = dom.window.location

    // 使用正确的 jQuery 设置方式
    global.$ = jquery(dom.window)

    // 确保jQuery正确绑定到window
    dom.window.$ = global.$

    console.log('✅ jQuery 环境设置完成')
}

// 测试选择器精确性
const testSelectorPrecision = () => {
    console.log('\n=== 测试选择器精确性 ===')

    // 测试主要的内容选择器
    const contentSelectors = [
        'section:not(#abstract) div[role="paragraph"]:not(figure.table .notes div[role="paragraph"])',
        'h1[property="name"]',
        '#abstract div[role="paragraph"]',
        'section:not(#abstract) h2',
        'section:not(#abstract) h3'
    ]

    contentSelectors.forEach((selector, index) => {
        try {
            const elements = document.querySelectorAll(selector)
            console.log(`\n${index + 1}. 选择器: "${selector}"`)
            console.log(`   匹配元素数量: ${elements.length}`)

            // 检查是否有可疑的界面元素
            let suspiciousCount = 0
            const suspiciousKeywords = ['download', 'share', 'citation', 'view', 'subscribe', 'total']

            for (let i = 0; i < Math.min(10, elements.length); i++) {
                const element = elements[i]
                const text = element.textContent.toLowerCase().trim()

                const isSuspicious = suspiciousKeywords.some(keyword => text.includes(keyword)) ||
                                   text.match(/^\d+$/)

                if (isSuspicious) {
                    suspiciousCount++
                    console.log(`   [可疑] ${text.substring(0, 80)}...`)
                }
            }

            const sampleSize = Math.min(10, elements.length)
            const precision = sampleSize > 0 ?
                ((sampleSize - suspiciousCount) / sampleSize * 100).toFixed(1) : 100

            console.log(`   可疑元素数量: ${suspiciousCount}/${Math.min(10, elements.length)}`)
            console.log(`   选择器精确性: ${precision}%`)

            // 精确性评估
            if (precision >= 90) {
                console.log(`   ✅ 选择器精确性很高`)
            } else if (precision >= 70) {
                console.log(`   ⚠️  选择器精确性中等，建议优化`)
            } else {
                console.log(`   ❌ 选择器精确性较低，需要重新设计`)
            }
        } catch (error) {
            console.log(`   ❌ 选择器语法错误: ${error.message}`)
        }
    })
}

// 测试表格注释结构（针对ASCO等网站）
const testTableNotes = () => {
    console.log('\n=== 测试表格注释结构 ===')

    const footnoteElements = document.querySelectorAll('figure.table .notes div[role="doc-footnote"]')
    if (footnoteElements.length === 0) {
        console.log('未找到表格注释元素')
        return
    }

    footnoteElements.forEach((element, index) => {
        console.log(`\n注释 ${index + 1}:`)
        console.log('HTML结构:', element.outerHTML.substring(0, 200) + '...')
        console.log('文本内容:', element.textContent.trim().substring(0, 100) + '...')

        // 检查是否有上标元素
        const supElements = element.querySelectorAll('sup')
        if (supElements.length > 0) {
            supElements.forEach((sup, supIndex) => {
                console.log(`  上标 ${supIndex + 1}:`, sup.textContent)
            })
        }

        // 检查是否有标签属性
        if (element.hasAttribute('data-has')) {
            console.log(`  标签属性: data-has="${element.getAttribute('data-has')}"`)
        }
    })
}

// 主测试函数
const runTests = async (parserName, searchTerm) => {
    try {
        console.log(`\n=== 测试 ${parserName} 解析器 ===`)

        // 检查解析器文件是否存在
        const parserPath = `./parsers/${parserName}.js`
        if (!fs.existsSync(parserPath)) {
            console.error(`❌ 解析器文件不存在: ${parserPath}`)
            return
        }

        // 检查HTML测试文件是否存在
        const htmlPath = `./parsers/${parserName}.html`
        if (!fs.existsSync(htmlPath)) {
            console.error(`❌ HTML测试文件不存在: ${htmlPath}`)
            console.log(`💡 请将测试页面的HTML保存为: ${htmlPath}`)
            return
        }

        // 读取HTML文件
        const html = fs.readFileSync(htmlPath, 'utf-8')
        console.log(`\nHTML文件大小: ${html.length} 字符`)

        // 设置全局环境
        await setupGlobalEnvironment(html)

        // 导入 utils 模块并设置全局变量
        const utils = await import('../utils/index.js')
        global.contentParser = utils.contentParser
        console.log('✅ Utils 模块导入成功')

        // 导入解析器模块
        console.log(`\n正在导入解析器模块: ${parserPath}`)
        const parser = await import(`../${parserPath}`)
        console.log('✅ 解析器模块导入成功')
        console.log('导出的函数:', Object.keys(parser))

        // 确保解析器可以访问全局的 jQuery
        console.log('\nJQuery 全局状态检查:')
        console.log('- global.$ 存在:', typeof global.$ !== 'undefined')
        console.log('- global.window 存在:', typeof global.window !== 'undefined')
        console.log('- global.document 存在:', typeof global.document !== 'undefined')

        // 运行测试
        testSelectorPrecision()
        testTableNotes()

        // 如果解析器有getContent函数，测试内容提取
        if (parser.getContent) {
            console.log('\n=== 测试内容提取 ===')
            try {
                console.log('正在测试解析器的选择器规则...')

                // 直接测试 sd.js 中的选择器规则，包括异步函数
                const testSelectors = [
                    ['article .Head .title-text', 'p'],
                    ['article #body h2.u-h4', 'h1'],
                    ['article #body h3.u-h4', 'h2'],
                    ['article .Abstracts h2.section-title', 'h1'],
                    ['article .Abstracts .u-margin-s-bottom:not(:has(ul.list)):not(:has(ol.list))', 'p'],
                    ['article #body .u-margin-s-bottom:not(.captions):not(figure .u-margin-s-bottom):not(.tables .captions):not(.legend .u-margin-s-bottom):not(.footnotes .u-margin-s-bottom):not(.tables .u-margin-s-bottom):not(:has(ul.list)):not(:has(ol.list)):not(:has(.download-link)):not(:has(table)):not(:has(.article-textbox)):not(.article-textbox .u-margin-s-bottom)', 'p'],
                    ['article figure.figure img', 'img'],
                    ['article figure.figure .captions', 'figcaption'],
                    ['article .tables table', 'table', (el) => {
                        const $tb = global.$(el).clone();

                        // 先移除屏幕阅读器专用文本
                        $tb.find('.screen-reader-only').remove();

                        // 然后清理表格属性
                        [...$tb.find('*')].map(el => [...el.attributes].forEach(attr => {
                            if (!['rowspan', 'colspan'].includes(attr.name.toLowerCase())) el.removeAttribute(attr.name);
                        }));

                        return { tag: 'table', children: $tb.html() };
                    }],
                    ['article .tables .captions', 'figcaption']
                ]

                const content = []
                let totalElements = 0

                for (const [selector, tag, fn] of testSelectors) {
                    try {
                        const elements = global.$(selector)
                        console.log(`  选择器 "${selector.substring(0, 60)}..." 匹配到 ${elements.length} 个元素`)
                        totalElements += elements.length

                        for (let i = 0; i < Math.min(elements.length, 10); i++) {
                            const el = elements[i]
                            if (tag === 'img') {
                                const src = global.$(el).attr('src')
                                if (src) {
                                    content.push({ tag, src })
                                }
                            } else if (fn) {
                                // 调用异步函数处理
                                const result = await fn(el)
                                if (result) {
                                    content.push(result)
                                }
                            } else if (tag === 'table') {
                                const html = global.$(el).html()
                                if (html && html.trim()) {
                                    content.push({ tag, children: html })
                                }
                            } else {
                                const text = global.$(el).text().trim()
                                if (text) {
                                    content.push({ tag, children: text })
                                }
                            }
                        }
                    } catch (error) {
                        console.log(`    ⚠️ 选择器出错: ${error.message}`)
                    }
                }

                console.log(`\n✅ 总共匹配到 ${totalElements} 个元素，提取到 ${content.length} 个内容项`)

                // 显示所有提取的内容（用于调试）
                if (searchTerm) {
                    console.log('\n📋 所有提取的内容:')
                    content.forEach((item, index) => {
                        const preview = typeof item.children === 'string' ?
                            item.children.substring(0, 100) :
                            JSON.stringify(item.children || item.src || '').substring(0, 100)
                        console.log(`  ${index + 1}. [${item.tag}] ${preview}${preview.length >= 100 ? '...' : ''}`)


                    })
                }

                // 应用 sd.js 中的过滤规则
                const filteredContent = content.filter(item => {
                    if (typeof item.children === 'string') {
                        const text = item.children.trim()
                        // 排除空内容、纯数字、纯符号等
                        if (text.length === 0 ||
                            text.match(/^[\d\s•·]+$/) ||
                            text.match(/^[^\w\s]+$/) ||
                            text.length <= 3) {
                            return false
                        }

                        // 排除下载提示文本
                        if (text.includes('Download') && text.includes('high-res') && text.includes('image')) {
                            return false
                        }

                        return true
                    }
                    return true
                })

                console.log(`🔧 应用过滤规则后，剩余 ${filteredContent.length} 个内容项`)

                // 使用过滤后的内容进行分析
                content.length = 0
                content.push(...filteredContent)

                // 分析内容类型分布
                const tagCounts = {}
                content.forEach(item => {
                    tagCounts[item.tag] = (tagCounts[item.tag] || 0) + 1
                })

                console.log('\n内容类型分布:')
                Object.entries(tagCounts).forEach(([tag, count]) => {
                    console.log(`  ${tag}: ${count} 个`)
                })

                // 检查可疑内容
                const suspiciousItems = content.filter(item => {
                    if (typeof item.children === 'string') {
                        const text = item.children.toLowerCase().trim()
                        return text.includes('download') || text.includes('share') ||
                               text.includes('citation') || text.includes('view') ||
                               text.includes('subscribe') || text.includes('empty cell') ||
                               text.match(/^\d+$/)
                    }
                    return false
                })

                // 如果提供了搜索词，专门搜索
                if (searchTerm) {
                    console.log(`\n🔍 在HTML中直接搜索 "${searchTerm}"...`)

                    // 先在原始HTML中搜索
                    const htmlContent = global.document.documentElement.innerHTML
                    const htmlMatches = (htmlContent.match(new RegExp(searchTerm, 'gi')) || []).length
                    console.log(`  HTML中找到 ${htmlMatches} 个匹配`)

                    // 然后在提取的内容中搜索（包括表格HTML内容）
                    const searchItems = content.filter(item => {
                        if (typeof item.children === 'string') {
                            return item.children.includes(searchTerm)
                        }
                        // 检查表格的HTML内容
                        if (item.tag === 'table' && item.children) {
                            return item.children.includes(searchTerm)
                        }
                        return false
                    })

                    if (searchItems.length > 0) {
                        console.log(`\n🔍 在提取内容中发现 ${searchItems.length} 个包含 "${searchTerm}" 的项:`)
                        searchItems.forEach((item, index) => {
                            if (item.tag === 'table') {
                                console.log(`  ${index + 1}. [${item.tag}] 表格HTML内容包含 "${searchTerm}"`)
                                console.log(`     完整表格内容: ${item.children}`)
                            } else {
                                console.log(`  ${index + 1}. [${item.tag}] ${item.children}`)
                            }
                        })
                    } else {
                        console.log(`\n✅ 在提取内容中未发现包含 "${searchTerm}" 的内容`)
                        if (htmlMatches > 0) {
                            console.log(`⚠️  但HTML中存在该文本，说明选择器没有提取到它`)
                        }
                    }
                }

                if (suspiciousItems.length > 0) {
                    console.log(`\n⚠️  发现 ${suspiciousItems.length} 个可疑内容项:`)
                    suspiciousItems.slice(0, 10).forEach((item, index) => {
                        const preview = typeof item.children === 'string' ?
                            item.children.substring(0, 80) :
                            JSON.stringify(item.children).substring(0, 80)
                        console.log(`  ${index + 1}. [${item.tag}] ${preview}...`)
                    })

                    const suspiciousRate = (suspiciousItems.length / content.length * 100).toFixed(1)
                    console.log(`\n可疑内容比例: ${suspiciousRate}%`)

                    if (suspiciousRate > 10) {
                        console.log('❌ 可疑内容比例过高，建议优化选择器')
                    } else if (suspiciousRate > 5) {
                        console.log('⚠️  可疑内容比例中等，可以考虑优化')
                    } else {
                        console.log('✅ 可疑内容比例较低，选择器精确性良好')
                    }
                } else {
                    console.log('\n✅ 未发现可疑内容，选择器精确性很好')
                }

            } catch (error) {
                console.error('❌ 内容提取测试失败:', error.message)
                console.error('错误堆栈:', error.stack)
            }
        } else {
            console.log('\n⚠️  解析器没有 getContent 函数')
        }

        console.log('\n=== 测试完成 ===')

    } catch (error) {
        console.error('测试过程中出错:', error.message)
        console.error(error.stack)
    }
}

// 命令行参数处理
const parserName = process.argv[2]
const searchTerm = process.argv[3]

if (!parserName) {
    console.log('使用方法: node tests/parser.js <parser_name> [search_term]')
    console.log('示例: node tests/parser.js asco')
    console.log('示例: node tests/parser.js sd "Empty Cell"')
    process.exit(1)
}

// 运行测试
runTests(parserName, searchTerm)
