import { Sequelize, DataTypes } from 'sequelize';

// 创建数据库连接
const sequelize = new Sequelize('mysql://MVDEjXWJFb7ccMK.root:<EMAIL>:4000/tsg', {
  dialect: 'mysql',
  dialectOptions: {
    ssl: {
      require: true,
      rejectUnauthorized: false
    },
  },
  pool: {
    max: 5,
    min: 0,
    acquire: 60000, // 增加获取连接的超时时间（毫秒）
    idle: 10000     // 连接在释放之前可以空闲的最长时间（毫秒）
  },
  // 添加重试配置
  retry: {
    max: 5,         // 最大重试次数
    timeout: 10000  // 重试超时时间
  },
  logging: false
});

// 定义用户模型
const User = sequelize.define('users', {
    uuid: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    auth0Id: {
      type: DataTypes.STRING,
      allowNull: false
      // unique: true // Temporarily removed to allow sync
    },
    config: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {} // config 的 defaultValue 应该为空对象
    }
    // email, username, nickname, avatar, systemPrompt, userPrompt, other 等字段已移入 config
    // createdAt 和 updatedAt 由 timestamps: true 自动处理
  }, {
    timestamps: true, // 自动添加 createdAt 和 updatedAt
    getterMethods: {
      // 通用 getter，用于访问顶层属性或 config 内的属性
      get(key) {
        // 优先从 dataValues 获取（如 uuid, auth0Id, config, createdAt, updatedAt）
        if (Object.prototype.hasOwnProperty.call(this.dataValues, key)) {
          return this.dataValues[key];
        }
        // 如果 key 不在 dataValues 中，则尝试从 config 中获取
        if (this.config && typeof this.config === 'object' && this.config !== null && Object.prototype.hasOwnProperty.call(this.config, key)) {
          return this.config[key];
        }
        // 对于嵌套在 config 内的默认值，例如 defaultApiModel, concurrencyLevel, chunkSize
        // 这些默认值应该在 server.js 中处理，而不是在模型层面
        return undefined;
      }
    }
  });

// toJSON 方法用于控制模型实例序列化为 JSON 时的行为
User.prototype.toJSON = function() {
  const values = { ...this.dataValues };

  // 将 config 中的所有属性展开到顶层
  // 如果 config 中的键与顶层键冲突，顶层键优先（例如，不应覆盖 uuid, auth0Id 等）
  if (values.config && typeof values.config === 'object') {
    for (const key in values.config) {
      if (Object.prototype.hasOwnProperty.call(values.config, key) && !Object.prototype.hasOwnProperty.call(values, key)) {
        values[key] = values.config[key];
      }
    }
  }
  // 根据要求，config 字段本身可以不出现在最终的 JSON 输出中，如果需要，可以取消下面这行注释
  // delete values.config;
  return values;
};

// 定义文章模型
const Article = sequelize.define('articles', {
  uuid: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  content: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []  // 设置默认值为空数组
  },
  translated: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []  // 设置默认值为空数组
  },
  title: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: '' // 默认为空字符串
  },
  summary: {
    type: DataTypes.TEXT,
    allowNull: true,
    defaultValue: null // AI总结内容
  },
  refs: {
    type: DataTypes.TEXT,
    allowNull: true,
    defaultValue: null // 参考文献内容
  },
  citation: {
    type: DataTypes.TEXT,
    allowNull: true,
    defaultValue: null // 来源内容
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false
  }
}, {
  timestamps: true
});

// 定义API模型
const Api = sequelize.define('apis', {
  uuid: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  provider: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'API供应商名称，如OpenAI、Anthropic等'
  },
  apiUrl: {
    type: DataTypes.STRING,
    allowNull: false
  },
  apiKey: {
    type: DataTypes.STRING,
    allowNull: false
  },
  models: {
    type: DataTypes.JSON,
    allowNull: false
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false
  },
  order: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '排序顺序，数字越小排序越靠前'
  }
}, {
  timestamps: true,
  hooks: {
    beforeDestroy: async (instance) => {
      console.log('正在删除API:', instance.uuid);
    },
    afterDestroy: async (instance) => {
      console.log('API删除成功:', instance.uuid);
    }
  }
});

// 定义关联关系
User.hasMany(Article, { foreignKey: 'userId' });
Article.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(Api, { foreignKey: 'userId' });
Api.belongsTo(User, { foreignKey: 'userId' });

const File = sequelize.define('files', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  dataUri: { // 存储此块的实际Data URI数据，或完整Data URI（如果未分块）
    type: DataTypes.TEXT('long'),
    allowNull: false,
  },
  md5: {
    type: DataTypes.STRING,
    allowNull: true, // 允许 MD5 为空，因为后续块的 MD5 将为空
    unique: true, // 确保 MD5 的唯一性以进行去重 (NULL 值通常不参与唯一性比较)
  },
  // mimeType 字段已移除，将从 dataUri 中解析
  nextChunkId: { // 指向下一个分块的ID (files.id)，如果为null则表示这是最后一个/唯一块
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'files', // 自引用
      key: 'id',
    },
    // onDelete: 'SET NULL' or 'CASCADE' - 需谨慎考虑对TiDB的影响和实际需求
  }
  // originalTotalSize 字段已移除
  // createdAt 和 updatedAt 会自动添加
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['nextChunkId'], // 方便按链查询
    }
  ]
});

// FileChunk 模型及其关联已被移除。


// 数据库连接状态管理
let dbConnectionStatus = {
  isConnected: false,
  isConnecting: false,
  lastError: null,
  connectedAt: null
};

// 获取数据库连接状态
function getDatabaseStatus() {
  return { ...dbConnectionStatus };
}

// 检查数据库是否已连接
function isDatabaseConnected() {
  return dbConnectionStatus.isConnected;
}

// 持续重连的数据库同步函数
let syncDbPromise = null; // 用于确保 syncDatabase 核心逻辑只执行一次
let reconnectTimer = null; // 重连定时器

async function syncDatabase() {
  if (syncDbPromise) {
    console.log('数据库同步已启动或已完成，返回现有 Promise。');
    return syncDbPromise;
  }

  syncDbPromise = (async () => {
    // 标记正在连接
    dbConnectionStatus.isConnecting = true;
    dbConnectionStatus.isConnected = false;
    dbConnectionStatus.lastError = null;

    await attemptDatabaseConnection();
    return true; // 总是返回true，因为我们会持续尝试
  })();

  return syncDbPromise;
}

// 尝试数据库连接的核心函数
async function attemptDatabaseConnection() {
  let attempt = 0;
  const retryInterval = 5000; // 5秒

  while (true) {
    attempt++;

    try {
      console.log(`尝试连接数据库 (尝试 ${attempt})...`);

      await sequelize.authenticate();
      console.log('数据库连接成功');

      // sequelize.sync() will sync all defined models
      await sequelize.sync();
      console.log('数据库同步成功 (User, Article, Api, File)'); // FileChunk 已移除

      // 更新连接状态
      dbConnectionStatus.isConnected = true;
      dbConnectionStatus.isConnecting = false;
      dbConnectionStatus.connectedAt = new Date();
      dbConnectionStatus.lastError = null;

      // 清除重连定时器
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
      }

      console.log('✅ 数据库连接建立，系统可以正常工作');
      return; // 连接成功，退出循环

    } catch (error) {
      console.error(`数据库连接或同步失败 (尝试 ${attempt}):`, error.message);

      // 更新错误状态
      dbConnectionStatus.lastError = error.message;
      dbConnectionStatus.isConnected = false;
      dbConnectionStatus.isConnecting = true; // 保持连接中状态

      console.log(`将在 ${retryInterval/1000} 秒后重试...`);
      await new Promise(resolve => setTimeout(resolve, retryInterval));
    }
  }
}

// 监听数据库连接断开事件并自动重连
function setupDatabaseReconnection() {
  // 使用Sequelize的钩子来监听连接状态
  sequelize.addHook('afterConnect', () => {
    console.log('🔗 数据库连接建立');
  });

  sequelize.addHook('beforeDisconnect', () => {
    console.warn('⚠️ 数据库连接即将断开');
  });

  // 定期检查连接状态
  const connectionCheckInterval = setInterval(async () => {
    if (dbConnectionStatus.isConnected) {
      try {
        await sequelize.authenticate();
      } catch (error) {
        console.warn('⚠️ 数据库连接检查失败，开始自动重连...');

        // 更新连接状态
        dbConnectionStatus.isConnected = false;
        dbConnectionStatus.isConnecting = true;
        dbConnectionStatus.lastError = 'Connection lost during health check';

        // 启动重连
        if (!reconnectTimer) {
          reconnectTimer = setTimeout(() => {
            attemptDatabaseConnection();
          }, 1000);
        }
      }
    }
  }, 30000); // 每30秒检查一次

  // 返回清理函数
  return () => {
    clearInterval(connectionCheckInterval);
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }
  };
}

export {
  sequelize,
  User,
  Article,
  Api,
  File,
  // FileChunk 已被移除
  syncDatabase,
  getDatabaseStatus,
  isDatabaseConnected,
  setupDatabaseReconnection
};
