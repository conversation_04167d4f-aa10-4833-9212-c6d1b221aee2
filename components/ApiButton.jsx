import React, { useState, useEffect } from 'react';
import { Button, Modal, Table, Tag, message } from 'antd';
import EnhanceTable from './EnhanceTable';
import _ from 'lodash';
import { apiBaseUrl } from '../utils/constant.js';
// 从统一的store文件中导入useStore
import { useStore } from '../stores/index.js';

const ApiButton = ({ className = '' }) => {
  console.log('[ApiButton] 组件正在渲染');
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFirstOpen, setIsFirstOpen] = useState(true);
  const {
    apis,
    setApis,
    defaultApiModel,
    setDefaultApiModel,
    fetchApis,
    saveApi,
    updateApi,
    deleteApi
  } = useStore();

  // 打开模态框时加载API列表
  useEffect(() => {
    if (open) {
      if (isFirstOpen && apis && apis.length > 0) {
        // 首次打开且已有API数据，不刷新
        console.log('首次打开，已有API数据，跳过刷新');
        setIsFirstOpen(false);
      } else {
        // 非首次打开或没有API数据，加载API列表
        loadApis();
        if (isFirstOpen) {
          setIsFirstOpen(false);
        }
      }
    }
  }, [open]);

  // 加载API列表
  const loadApis = async () => {
    setIsLoading(true);
    console.log('加载API列表...');
    try {
      await fetchApis();
    } catch (error) {
      console.error('加载API失败:', error);
      message.error('加载API列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  const columns = [
    { title: '供应商', dataIndex: 'provider', key: 'provider' },
    { title: 'API URL', dataIndex: 'apiUrl', key: 'apiUrl' },
    { title: 'API KEY', dataIndex: 'apiKey', key: 'apiKey' },
    {
      title: '模型',
      dataIndex: 'models',
      key: 'models',
      render: (models) => (
        <>
          {Array.isArray(models) && models.map(model => (
            <Tag key={model}>{model}</Tag>
          ))}
        </>
      )
    }
  ];

  // 处理行数据变化（编辑现有行或添加新行）
  const handleRowsChange = async (newData) => {
    // 确保models字段在每行中都是数组
    const validatedData = newData.map(row => ({
      ...row,
      models: Array.isArray(row.models) ? row.models : []
    }));

    // 如果数据集长度增加，说明是新增行
    if (validatedData.length > apis.length) {
      // 检查新行是否为空行
      const newRow = validatedData[validatedData.length - 1]; // 获取新增的行
      const isEmptyRow = Object.entries(newRow).every(([key, value]) => {
        if (key === 'key') return true; // 跳过key字段的检查
        if (Array.isArray(value)) return value.length === 0;
        return !value || value === 'default' || value === 'http://';
      });

      // 只有在新行不是空行或者之前没有空行的情况下才保存
      if (!isEmptyRow) {
        try {
          // 设置默认值以确保API请求不会失败
          const apiDataToSave = {
            ...newRow,
            provider: newRow.provider || 'default',
            apiUrl: newRow.apiUrl || 'http://',
            apiKey: newRow.apiKey || ''
          };
          const savedApi = await saveApi(apiDataToSave);

          if (!savedApi) {
            message.error('保存API失败');
            // 如果保存失败，重新加载API列表
            await loadApis();
          }
          // 移除手动更新本地状态的代码，因为saveApi函数已经处理了状态更新
        } catch (error) {
          console.error('保存API出错:', error);
          message.error('保存过程中出现错误');
          // 保存失败时重新加载API列表
          await loadApis();
        }
      } else {
        // 即使是空行，也更新本地状态以保持UI一致
        setApis(validatedData);
      }
    }
    // 如果数据集长度不变，可能是编辑现有行
    else if (newData.length === apis.length) {
      // 找出变更的行
      for (let i = 0; i < newData.length; i++) {
        const oldRow = apis[i];
        const updatedRow = newData[i];

        // 检查是否有变更
        if (
          oldRow.provider !== updatedRow.provider ||
          oldRow.apiUrl !== updatedRow.apiUrl ||
          oldRow.apiKey !== updatedRow.apiKey ||
          !_.isEqual(oldRow.models, updatedRow.models)
        ) {
          try {
            const success = await updateApi(updatedRow.key, updatedRow);
            if (!success) {
              message.error('更新API失败');
              // 如果更新失败，重新加载API列表
              await loadApis();
            }
          } catch (error) {
            console.error('更新API出错:', error);
            message.error('更新过程中出现错误');
            // 更新失败时重新加载API列表
            await loadApis();
          }
          break; // 找到并处理第一个变更的行后跳出循环
        }
      }
    }
  };

  return (
    <>
      <Button onClick={() => setOpen(true)} size="small" className={`custom-small-font ${className}`}>模型API</Button>
      <Modal
        title="API设置"
        open={open}
        width={'80%'}
        footer={[
          <Button key="close" onClick={() => setOpen(false)}>
            关闭
          </Button>
        ]}
        onCancel={() => setOpen(false)}
      >
        <div style={{ width: '100%', overflowX: 'auto' }}>
          <EnhanceTable
            pagination={false}
            dataSource={apis}
            columns={columns}
            loading={isLoading}
            editableCells={{
              provider: 'text',
              apiUrl: 'text',
              apiKey: 'text',
              models: 'tags',
            }}
            onRowsChange={handleRowsChange}
            onDeleteRow={async (key) => {
              try {
                // deleteApi函数内部已经会显示成功或失败提示，这里不需要再显示
                const success = await deleteApi(key);
                return success;
              } catch (error) {
                console.error('删除API出错:', error);
                message.error('删除过程中出现错误');
                return false;
              }
            }}
            onMoveRow={async (key, dragIndex, hoverIndex, newData) => {
              console.log('dragIndex:', dragIndex, 'hoverIndex:', hoverIndex);
              console.log('排序前的APIs:', apis);

              try {
                // 为所有API生成排序数据
                const orderedApis = newData.map((api, index) => ({
                  uuid: api.key,
                  order: index
                }));

                console.log('发送到后端的排序数据:', orderedApis);

                // 调用后端排序接口
                const response = await fetch(`${apiBaseUrl}api/apis/reorder`, {
                  method: 'PUT',
                  credentials: 'include',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(orderedApis)
                });

                if (!response.ok) {
                  const errorData = await response.json().catch(() => ({}));
                  const errorMsg = errorData.error || '更新失败';
                  message.error(`更新API顺序失败: ${errorMsg}`);
                  // 如果更新失败，重新加载API列表
                  await loadApis();
                  return false; // 返回false表示操作失败
                } else {
                  console.log('API顺序更新成功');

                  // 在后端成功后，立即更新本地状态以确保顺序正确
                  const updatedApis = [...newData]; // 创建新的数组引用，确保状态更新
                  console.log('更新后的APIs:', updatedApis);
                  setApis(updatedApis);

                  // 返回true表示操作成功
                  return true;
                }
              } catch (error) {
                console.error('更新API顺序出错:', error);
                message.error('更新顺序过程中出现错误');
                // 出错时重新加载API列表
                await loadApis();
                return false; // 返回false表示操作失败
              }
            }}
            onCellChange={async (key, dataIndex, oldValue, newValue, record) => {
              try {
                // 确保models字段是数组
                if (dataIndex === 'models' && !Array.isArray(newValue)) {
                  console.log('修正models字段值为数组:', newValue);
                  newValue = Array.isArray(newValue) ? newValue : [];
                }

                // 创建更新的记录副本，确保数据完整性
                const updatedRecord = { ...record };

                // 更新字段值
                updatedRecord[dataIndex] = newValue;

                // 构建API数据，确保所有字段都有合适的值
                const apiData = {
                  provider: updatedRecord.provider || '',
                  apiUrl: updatedRecord.apiUrl || '',
                  apiKey: updatedRecord.apiKey || '',
                  models: Array.isArray(updatedRecord.models) ? updatedRecord.models : []
                };

                console.log('单元格变更, 字段:', dataIndex, '旧值:', oldValue, '新值:', newValue);
                console.log('API数据:', apiData);

                // 检查是否为新行（key是否以'new-'开头）
                if (key.toString().startsWith('new-')) {
                  // 对于新行，使用saveApi创建新记录
                  const savedApi = await saveApi(apiData);
                  if (!savedApi) {
                    message.error('创建API失败');
                    await loadApis();
                  } else {
                    // 创建成功后，不删除临时行，而是更新它的key为真实UUID
                    // 这样用户界面上不会出现行消失的情况
                    const updatedApis = apis.map(api =>
                      api.key === key
                        ? { ...api, key: savedApi.key, ...apiData }
                        : api
                    );
                    setApis(updatedApis);

                    // 显示成功消息
                    message.success('API创建成功');
                  }
                } else {
                  // 对于现有行，使用updateApi更新
                  const success = await updateApi(key, apiData);
                  if (!success) {
                    message.error('更新API失败');
                    await loadApis();
                  } else {
                    message.success('API更新成功');
                  }
                }
              } catch (error) {
                console.error('更新API出错:', error);
                message.error('更新过程中出现错误');
                // 更新失败时重新加载API列表
                await loadApis();
              }
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default ApiButton;
