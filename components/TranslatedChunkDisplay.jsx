import { useEffect, useCallback, memo, useRef } from 'react';
import _ from 'lodash'

// Helper component for displaying translated content when not in editing mode
const TranslatedChunkDisplayInner = ({ chunkIndex, translatedItems, isEditingTranslatedFlag, updateDOMFunc, containerRefs, isActivelyTranslating }) => { // Added isActivelyTranslating
  const prevItemsRef = useRef(null); // 用于比较 translatedItems 引用是否变化

  // 记录组件渲染和 props 变化
  useEffect(() => {
    // const itemsChanged = prevItemsRef.current !== translatedItems; // Keep for potential future debugging
    // if (itemsChanged) {
      // console.log(`[TCD] translatedItems引用变化: chunkIndex=${chunkIndex}`);
    // }
    prevItemsRef.current = translatedItems;
  }, [chunkIndex, translatedItems, isEditingTranslatedFlag, isActivelyTranslating]); // Added isActivelyTranslating to dependency array

  // setRef回调，负责注册和注销容器引用
  const setRef = useCallback((el) => {
    if (el) {
      containerRefs.current.set(chunkIndex, el);
      if (typeof window !== 'undefined') {
        window._translationContainers = window._translationContainers || {};
        window._translationContainers[chunkIndex] = el;
      }
    } else {
      if (containerRefs.current.has(chunkIndex)) {
        containerRefs.current.delete(chunkIndex);
      }
    }
  }, [chunkIndex, containerRefs]); // 依赖项仅为 chunkIndex 和 containerRefs

  // useEffect 统一处理 DOM 更新
  useEffect(() => {
    if (isEditingTranslatedFlag) {
      return;
    }

    let container = null;
    if (typeof window !== 'undefined' && window._translationContainers && window._translationContainers[chunkIndex]) {
      container = window._translationContainers[chunkIndex];
    } else if (containerRefs.current.has(chunkIndex)) {
      container = containerRefs.current.get(chunkIndex);
      if (container && typeof window !== 'undefined') {
        window._translationContainers = window._translationContainers || {};
        window._translationContainers[chunkIndex] = container;
      }
    }

    if (!container) {
      return;
    }

    try {
      updateDOMFunc(chunkIndex, translatedItems || [], isActivelyTranslating);

      if (typeof window !== 'undefined') {
        window._lastTranslationItems = window._lastTranslationItems || {};
        window._lastTranslationItems[chunkIndex] = translatedItems || [];
      }

    } catch (error) {
      console.error(`[TCD] DOM更新useEffect: updateDOMFunc调用失败, chunkIndex=${chunkIndex}, 错误=${error.message}`);
    }

    return () => {
      // Optional cleanup log, can be removed if not needed
      // console.log(`[TCD] DOM更新useEffect 清理: chunkIndex=${chunkIndex}`);
    };
  }, [chunkIndex, translatedItems, isEditingTranslatedFlag, updateDOMFunc, containerRefs, isActivelyTranslating]); // Added isActivelyTranslating to dependency array

  return (
    <div
      className="translated-chunk-content"
      style={{ minHeight: '200px' }} // 保持样式
      ref={setRef}
      data-chunk-index={chunkIndex}
    />
  );
};

const TranslatedChunkDisplay = memo(TranslatedChunkDisplayInner);

export default TranslatedChunkDisplay;