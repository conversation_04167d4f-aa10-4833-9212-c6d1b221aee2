/* Consolidated styles for ArticleEditPage (including former ArticleTopBar, ArticleControls, and ArticleChunk) */

/* General styles */
.article-edit-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.article-edit-container > div:first-of-type {
  /* This padding is no longer needed as the toolbar is position:fixed */
}

/* Controls styles */
.article-controls {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  min-height: 60px; /* 确保最小高度 */
  gap: 8px; /* 添加间距 */
  align-items: flex-start; /* 顶部对齐 */
}

.controls-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* Content styles */
.t-content {
  width: 100%;
}

.t-content img {
  width: 90%;
}

.t-content table,
.t-content td,
.t-content table th {
  border: 1px solid #000;
  border-collapse: collapse;
}

.t-content div:has(> table) {
  zoom: 0.6;
  width: 100%;
  max-height: 400px;
  overflow: auto;
}

.t-content p{
  font-size: 13px;
}

/* AI总结区域字体大小与原文、译文保持一致 */
.summary-content-container p,
.summary-content-container div,
.summary-content-container span {
  font-size: 13px !important;
}

.t-content figcaption{
  font-size:12px;
color:gray;
display:block;
}

.t-content h1{
  font-size:17px;
}

.t-content h2{
  font-size:15px;
}
/* Chunk styles */
.chunk-row {
  width: 100%;
  align-items: stretch;
  margin-bottom: 10px;
}

/* Content container styles */
.original-content-container,
.translated-content-container,
.summary-content-container {
  display: flex;
  flex-direction: column;
  min-height: 200px;
  flex-grow: 1;
}

/* Summary content container specific styles */
.summary-content-container {
  min-height: 60px;
}

/* Editing mode styles */
.original-content-container.editing-mode,
.translated-content-container.editing-mode,
.summary-content-container.editing-mode {
  height: 100%;
  min-height: 300px;
}

.summary-content-container.editing-mode {
  min-height: 120px;
}

.original-content-container.editing-mode .ant-input-textarea,
.translated-content-container.editing-mode .ant-input-textarea,
.summary-content-container.editing-mode .ant-input-textarea {
  height: 100%;
}

.original-content-container.editing-mode .ant-input,
.translated-content-container.editing-mode .ant-input {
  height: 100% !important;
  min-height: 300px !important;
  resize: vertical;
}

/* Reduce vertical spacing within translated table cells */
.translated-content-container table p {
  margin-block-start: 0.2em;
  margin-block-end: 0.2em;
  padding: 0;
  line-height: 1.3;
}

/* Ensure table cells don't add excessive padding */
.translated-content-container table td,
.translated-content-container table th {
  padding: 2px 4px;
}

/* Style for the streaming content */
.streaming-content-pre {
  overflow-y: auto;
  display: block;
  box-sizing: border-box;
}

/* Reduce margin around table images */
.table-image-container {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

/* Tag count mismatch warning */
.tag-count-mismatch-warning {
  background-color: #FFFBE6;
  padding: 12px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #000000;
}

.tag-count-mismatch-warning .anticon {
  margin-right: 4px;
}
/* Style for icon buttons in the title section to have no hover/focus background */
.icon-title-button:hover,
.icon-title-button:focus {
  background-color: transparent !important;
}

.custom-small-font *{
  font-size: 13px !important; /* Use !important to ensure override */
}

/* Additional styles moved from index.css */

/* Target the content wrappers inside each chunk row */
.original-chunk-content,
.translated-chunk-content {
  width: 100%; /* Ensure they take full width of their container */
  min-height: 2.5em; /* Set a minimum height (adjust as needed, approx 2 lines) */
  padding: 5px; /* Add some internal padding */
  vertical-align: top; /* Align content to the top */
  /* border: 1px dashed #eee; */ /* Optional: Add border for visualization during development */
  margin-bottom: 10px; /* Add space below each chunk content */
}

/* Ensure elements within the wrappers start from the top */
.original-chunk-content > *,
.translated-chunk-content > * {
  margin-top: 0; /* Remove default top margin of first element */
}
.content-titles-row {
  /* This class now only serves as a semantic marker and for the gutter property in JSX. */
  /* All visual and positioning styles are moved to the wrapper. */
}

.sticky-header-wrapper {
  position: sticky;
  top: calc(var(--toolbar-height, 80px) - 12px);
  background-color: #f5f5f5;
  z-index: 999;

  /* Compensate for parent's padding to achieve full-width */
  margin-left: -24px;
  margin-right: -24px;

  /* Add padding back to align inner content */
  padding: 8px 24px;

  border-bottom: 1px solid #e8e8e8;
}

.sticky-header-wrapper.is-not-sticky {
  /* 当标题行未吸顶时，在其下方增加外边距以创建空白 */
  background-color: #fff;
  padding-left: 16px; /* 调整为16px以与下方正文内容对齐 */
padding-top:0px;
margin-top:-4px;
margin-left:0!important;
margin-right:0!important;
margin-bottom:16px;
  /*
    原有的 `padding: 8px 24px;` 依然生效，提供内部的上下左右边距。
    其中左右24px的padding满足了“左、右需留一定空白”的需求。
    此处的 `margin-bottom: 20px;` 专门用于满足“下需留一定空白”的需求，
    它会在 `sticky-header-wrapper` 元素的边框外部创建20px的空白。
  */
}

.sticky-header-wrapper.is-disabled {
  /* 当滚动到chunks区域之外时，完全禁用sticky定位 */
  position: static !important;
  background-color: transparent !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  z-index: auto !important;
  display: none !important; /* 完全隐藏 */
}

.sticky-header-wrapper.is-actually-sticky {
  /* 当标题行处于吸顶状态时，移除上边距以与上方工具条融为一体 */
  margin-top: 0;
  /* 移除上边框，让它与上方的工具条无缝连接 */
  border-top: none;
  /* 确保与上方工具条的背景色一致 */
  background-color: #f5f5f5;
}

/* sticky状态下原文译文标题字体大小调整为15px */
.sticky-header-wrapper.is-actually-sticky h1 {
  font-size: 15px !important;
}

.toolbar-no-border {
  border-bottom: none !important;
}

/* 调整原文、译文标题与正文内容的对齐 */
.sticky-header-wrapper.is-not-sticky .content-titles-row .ant-col:first-child {
  /* 原文标题列：减少左边距以与正文对齐 */
  padding-left: 0px !important;
}

.sticky-header-wrapper.is-not-sticky .content-titles-row .ant-col:last-child {
  /* 译文标题列：减少左边距以与正文对齐 */
  padding-left: 8px !important;
}
