import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { apiBaseUrl } from '../utils/constant.js';
import HtmlToImage from './HtmlToImage'; // Import HtmlToImage component

// 样式对象定义
const styles = {
  // 字体定义
  fontFace: `
    @font-face {
      font-family: 'Noto Sans SC';
      font-style: normal;
      font-weight: 400;
      font-display: swap;
      src: url(https://fonts.gstatic.com/s/notosanssc/v37/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf) format('truetype');
    }
  `,

  // 主容器样式
  articlePreviewContainer: {
    position: 'relative',
    padding: '25px 20px',
    maxWidth: '677px',
    margin: '0 auto',
    backgroundColor: '#fff',
    fontFamily: '-apple-system-font, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif',
    lineHeight: '1.75',
    color: '#3e3e3e',
    letterSpacing: '0.544px',
    fontSize: '14px',
    wordWrap: 'break-word'
  },

  // 文章头部样式
  articleHeader: {
    marginBottom: '32px',
    textAlign: 'left'
  },

  articleTitle: {
    fontSize: '22px',
    fontWeight: 'bold',
    lineHeight: '1.4',
    color: '#2c2c2c',
    margin: '0 0 4px 0',
    padding: '0',
    border: 'none'
  },

  articleDate: {
    fontSize: '14px',
    color: '#888',
    margin: '0',
    padding: '0',
    fontWeight: 'normal'
  },

  // 加载状态
  articlePreviewLoading: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    fontSize: '1.2em',
    color: '#666'
  },

  // 错误状态
  articlePreviewError: {
    border: '1px solid #ffccc7',
    backgroundColor: '#fff2f0',
    color: '#d4380d',
    padding: '15px',
    margin: '20px',
    borderRadius: '4px'
  },

  articlePreviewNoContent: {
    border: '1px solid #ffccc7',
    backgroundColor: '#fff2f0',
    color: '#d4380d',
    padding: '15px',
    margin: '20px',
    borderRadius: '4px'
  },

  errorStrong: {
    display: 'block',
    marginBottom: '5px',
    fontWeight: 'bold'
  },

  // 标题样式
  h1: {
    fontSize: '17px',
    marginTop: '16px',
    marginBottom: '16px',
    fontWeight: 'bold',
    lineHeight: '1.4',
    color: '#2c2c2c'
  },

  h2: {
    fontSize: '16px',
    marginTop: '16px',
    marginBottom: '16px',
    fontWeight: 'bold',
    lineHeight: '1.4',
    color: '#2c2c2c'
  },

  h3: {
    fontSize: '15px',
    marginTop: '16px',
    marginBottom: '16px',
    fontWeight: 'bold',
    lineHeight: '1.4',
    color: '#2c2c2c'
  },

  // 段落样式
  paragraph: {
    margin: '1.4em 0',
    fontSize: '15px',
    lineHeight: '1.5',
    color: '#3e3e3e',
    textAlign: 'justify'
  },

summaryParagrah:{
  lineHeight:'1.5',
fontSize:'15px',
margin:'4px 0',
},

  // 目录段落样式
  tocParagraph: {
    margin: '0',
    fontSize: '13px',
    lineHeight: '1.3',
    color: '#3e3e3e'
  },

  // 链接样式
  link: {
    color: '#576b95',
    textDecoration: 'none'
  },

  linkHover: {
    textDecoration: 'underline'
  },

  // 列表样式
  listItem: {
    margin: '3px',
    lineHeight: '1.5',
    fontSize: '14px'
  },

  list: {
    listStyleType: 'none',
    paddingLeft: '0',
    margin: '16px 0'
  },

  // 图片样式
  image: {
    maxWidth: '800px',
    width:'90%',
    height: 'auto',
    display: 'block',
    margin: '8px auto',
    borderRadius: '0'
  },

  // 分隔线样式
  hr: {
    border: 'none',
    borderTop: '1px solid #e5e5e5',
    margin: '32px 0'
  },

  // 原始HTML内容样式
  rawHtmlContent: {
    marginBottom: '1em'
  },

  // 全局字体样式
  globalFont: {
    fontFamily: "'Noto Sans SC', sans-serif"
  },

  // 图片说明样式
  figcaption: {
    fontSize: '12px',
    color: '#888',
    textAlign: 'left',
    marginTop: '4px',
    marginBottom: '4px',
    lineHeight: '1.2'
  },

  figcaptionTitle: {
    fontSize: '15px',
    color: '#2c2c2c',
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: '1.6'
  },

  figcaptionStrong: {
    fontWeight: 'bold',
    color: '#2c2c2c'
  },

  // 目录容器样式
  tocContainer: {
    backgroundColor: 'transparent',
    border: 'none',
    padding: '0',
    marginBottom: '20px',
    borderRadius: '0',
    borderBottom: '1px solid #e5e5e5',
    paddingBottom: '16px'
  },

  // 目录内容包装器（用于整体滚动）
  tocContent: {
    overflowX: 'auto',
    overflowY: 'hidden',
    whiteSpace: 'nowrap'
  },

  tocList: {
    listStyle: 'none',
    paddingLeft: '0',
    margin: '0',
    display: 'inline-block',
    whiteSpace: 'normal'
  },

  tocListItem: {
    lineHeight: '1.4'
  },

  tocItem: {
    marginBottom: '0',
    lineHeight: '1'
  },

  // 目录层级样式
  tocLevel2: {
    paddingLeft: '20px'
  },

  tocSeparator: {
    display: 'none'
  },

  // 引用块样式
  blockquote: {
    margin: '24px 0',
    padding: '16px 20px',
    backgroundColor: '#f7f8fa',
    borderLeft: '4px solid #576b95',
    borderRadius: '0 6px 6px 0',
    fontStyle: 'italic',
    color: '#666'
  },

  // 代码样式
  code: {
    backgroundColor: '#f7f8fa',
    padding: '2px 6px',
    borderRadius: '3px',
    fontFamily: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
    fontSize: '15px',
    color: '#d73a49'
  },

  pre: {
    backgroundColor: '#f7f8fa',
    padding: '16px 20px',
    borderRadius: '6px',
    overflowX: 'auto',
    margin: '24px 0',
    border: '1px solid #e5e5e5'
  },

  preCode: {
    backgroundColor: 'transparent',
    padding: '0',
    color: '#2c2c2c',
    fontSize: '14px'
  },

  // 强调文本样式
  strong: {
    fontWeight: 'bold',
    color: '#2c2c2c'
  },

  em: {
    fontStyle: 'italic',
    color: '#666'
  },

  // AI总结样式
  summaryContainer: {
    marginBottom: '32px'
  },

  summaryContent: {
    marginBottom: '16px'
  },

  summarySeparator: {
    border: 'none',
    borderTop: '1px solid #e5e5e5',
    margin: '16px 0 0 0'
  },

  // 引用样式
  citationContainer: {
    marginTop: '32px',
    marginBottom: '24px'
  },

  // 参考文献样式
  refsContainer: {
    marginTop: '24px',
    marginBottom: '24px'
  },

  // 复制按钮样式
  copyButton: {
    position: 'fixed',
    top: '20px',
    right: '20px',
    backgroundColor: '#576b95',
    color: 'white',
    border: 'none',
    borderRadius: '6px',
    padding: '10px 16px',
    fontSize: '14px',
    fontWeight: '500',
    cursor: 'pointer',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
    zIndex: '1000',
    transition: 'all 0.2s ease'
  },

  copyButtonHover: {
    backgroundColor: '#4a5d85',
    transform: 'translateY(-1px)',
    boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
  },

  copyButtonActive: {
    backgroundColor: '#3d4f73',
    transform: 'translateY(0)',
    boxShadow: '0 2px 6px rgba(0,0,0,0.15)'
  },

  copyButtonDisabled: {
    backgroundColor: '#ccc',
    cursor: 'default',
    transform: 'none',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
  },

  // 控制按钮样式
  controlButton: {
    backgroundColor: '#f7f8fa',
    border: '1px solid #e5e5e5',
    borderRadius: '4px',
    padding: '8px 12px',
    fontSize: '12px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  },

  controlButtonActive: {
    backgroundColor: '#576b95',
    color: 'white',
    borderColor: '#576b95'
  },

  // 可选择区块样式
  selectableBlock: {
    position: 'relative',
    border: '2px solid transparent',
    borderRadius: '4px',
    transition: 'all 0.2s ease'
  },

  selectableBlockSelected: {
    borderColor: '#576b95',
    backgroundColor: 'rgba(87, 107, 149, 0.05)'
  },

  selectableBlockHover: {
    borderColor: '#e5e5e5'
  },

  blockSelector: {
    position: 'absolute',
    top: '-2px',
    left: '-2px',
    width: '20px',
    height: '20px',
    backgroundColor: '#fff',
    border: '2px solid #576b95',
    borderRadius: '3px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    fontSize: '12px',
    color: '#576b95',
    zIndex: '10'
  },

  blockSelectorSelected: {
    backgroundColor: '#576b95',
    color: 'white'
  },

  // 表格图片渲染样式（用于HtmlToImage）
  tableImageWrapper: {
    width: '100%',
    overflowX: 'auto',
    margin: '4px 0',
    WebkitOverflowScrolling: 'touch',
    boxSizing: 'border-box'
  },
  tableImageCss: `
    .table-container {
      display: inline-block;
      min-width: 800px;
      max-width: 1400px;
      overflow-x: visible !important;
      margin: 0;
      padding: 8px;
      background-color: #fff;
    }
    .table-container table {
      border-collapse: collapse !important;
      width: 100% !important;
      min-width: 800px !important;
      table-layout: fixed !important;
      margin: 0 !important;
      font-size: 16px !important;
      line-height: 1.5 !important;
      font-family: -apple-system-font, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', Arial, sans-serif !important;
      border: 1px solid #000 !important;
    }
    .table-container table th,
    .table-container table td {
      border: 1px solid #000 !important;
      padding: 4px 6px !important;
      text-align: left !important;
      vertical-align: top !important;
      font-size: 12px !important;
      white-space: normal !important;
      word-wrap: break-word !important;
      overflow-wrap: break-word !important;
      min-width: 120px !important;
      line-height: 1.2 !important;
    }
    .table-container table th {
      background-color: #f8f9fa !important;
      font-weight: bold !important;
      text-align: center !important;
      font-size: 12px !important;
    }
    .table-container table sup,
    .table-container table sup a,
    .table-container table a sup {
      color: #000 !important;
      text-decoration: none !important;
      vertical-align: 0.3em !important;
      font-size: 8px !important;
      line-height: 1 !important;
    }
  .table-container sup {
    color: #000;
    text-decoration: none;
    vertical-align: super;
    font-size: 10px;
  }
  `,

  // 参考文献图片渲染样式（用于HtmlToImage）
  refsImageWrapper: {
    width: '100%',
    overflowX: 'auto',
    margin: '0',
    WebkitOverflowScrolling: 'touch',
    boxSizing: 'border-box'
  },

  // 参考文献滚动容器样式
  refsScrollContainer: {
    maxHeight: '200px',
    overflowY: 'auto',
    border: '0',
    borderRadius: '4px',
    WebkitOverflowScrolling: 'touch'
  },
  refsImageCss: `
    .refs-container {
      display: inline-block;
      min-width: 400px;
      max-width: 800px;
      overflow-x: visible !important;
      margin: 0;
      padding: 8px;
      background-color: #fff;
    }
    .refs-container p {
      margin: 4px 0 !important;
      font-size: 15px !important;
      line-height: 1.5 !important;
      color: #3e3e3e !important;
      text-align: justify !important;
      font-family: -apple-system-font, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', Arial, sans-serif !important;
      word-wrap: break-word !important;
      overflow-wrap: break-word !important;
    }
    .refs-container sup,
    .refs-container sup a,
    .refs-container a sup {
      color: #000 !important;
      text-decoration: none !important;
      vertical-align: 0.3em !important;
      font-size: 10px !important;
      line-height: 1 !important;
    }
  `,

  // 全局字体样式（用于应用到所有元素）
  globalFontStyles: `
    .article-preview-container * {
      font-family: 'Noto Sans SC', sans-serif !important;
    }
  `,

  // 选择模式按钮样式
  selectModeButton: {
    // Initial properties from styles.copyButton
    position: 'fixed',
    top: '20px',
    right: '140px', // Overridden
    color: 'white',
    border: 'none',
    borderRadius: '6px',
    padding: '10px 16px',
    fontSize: '14px',
    fontWeight: '500',
    cursor: 'pointer',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
    zIndex: '1000',
    transition: 'all 0.2s ease',
    // Specific overrides
    // backgroundColor will be dynamic, so handle in component
  },

  // 快捷操作按钮容器样式
  quickActionsContainer: {
    position: 'fixed',
    top: '70px',
    right: '20px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    zIndex: '999'
  },

  // 快捷操作按钮（全选/全不选）的基础样式 - 结合 controlButton 和可能的 active 状态
  quickActionButton: { // Base for selectAll/None to be combined with controlButton
    minWidth: '80px'
  },

  selectableCursor: {
    cursor: 'pointer'
  },

  blockSelectorPointerEventsNone: {
    pointerEvents: 'none'
  },

  selectModeButtonActiveBg: {
    backgroundColor: '#28a745'
  },
  selectModeButtonInactiveBg: {
    backgroundColor: '#6c757d'
  },

};

// Removed extractText function as it's not needed for the simple { tag: '...', text: '...' } structure

// Simple image component without proxy
const SimpleImage = ({ src, alt, ...props }) => {
  return (
    <img
      src={src}
      alt={alt}
      style={styles.image}
      {...props}
    />
  );
};

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-');
  } catch (error) {
    return '';
  }
};

// Helper function to extract text from children, similar to how it was done for TOC
const extractTextFromChildren = (children, defaultTextPrefix, counter) => {
  if (typeof children === 'string') {
    return children;
  }
  if (Array.isArray(children)) {
    return children.map(c => typeof c === 'string' ? c : (c && c.text) || '').join('');
  }
  return `${defaultTextPrefix} ${counter}`;
};

// Function to generate TOC structure AND a new content array with IDs
// Does NOT mutate the original content array.
const generateTocAndContentWithIds = (originalContent) => {
  const toc = [];
  const contentWithIds = [];
  let currentH1 = null;
  let h1Counter = 0;
  let h2Counter = 0;
  let h3Counter = 0;
  let blockCounter = 0;

  originalContent.forEach((item) => {
    if (!item) {
      contentWithIds.push(null);
      return;
    }
    const newItem = JSON.parse(JSON.stringify(item)); // Deep clone

    // 为所有区块生成唯一ID
    if (!newItem.id) {
      if (newItem.tag === 'h1') {
        newItem.id = `block-h1-${h1Counter}`;
        const text = extractTextFromChildren(newItem.children, 'H1', h1Counter);
        const newH1Node = { id: newItem.id, level: 1, text: text, children: [] };
        toc.push(newH1Node);
        currentH1 = newH1Node;
        h1Counter++;
        h2Counter = 0;
        h3Counter = 0;
      } else if (newItem.tag === 'h2') {
        newItem.id = `block-h2-${h1Counter > 0 ? h1Counter - 1 : 0}-${h2Counter}`;
        const text = extractTextFromChildren(newItem.children, 'H2', h2Counter);
        const newH2Node = { id: newItem.id, level: 2, text: text, children: [] };
        if (currentH1) {
          currentH1.children.push(newH2Node);
        } else {
          toc.push(newH2Node);
          console.warn("Found H2 without preceding H1:", text);
        }
        h2Counter++;
        h3Counter = 0;
      } else if (newItem.tag === 'h3') {
        newItem.id = `block-h3-${h1Counter > 0 ? h1Counter - 1 : 0}-${h2Counter > 0 ? h2Counter - 1 : 0}-${h3Counter}`;
        h3Counter++;
      } else {
        // 为其他类型的区块生成ID
        const blockType = newItem.tag || (newItem.html ? 'html' : 'unknown');
        newItem.id = `block-${blockType}-${blockCounter}`;
        blockCounter++;
      }
    }

    contentWithIds.push(newItem);
  });

  // console.log("Generated TOC structure:", toc);
  return { toc, contentWithIds };
};

// 生成区块信息用于选择面板
const generateBlockInfo = (contentWithIds, summary, citation, refs) => {
  const blocks = [];

  // 添加AI总结区块
  if (summary && summary.trim()) {
    blocks.push({
      id: 'block-summary',
      type: 'summary',
      title: 'AI总结',
      content: summary.substring(0, 50) + (summary.length > 50 ? '...' : '')
    });
  }

  // 添加目录区块
  blocks.push({
    id: 'block-toc',
    type: 'toc',
    title: '目录',
    content: '文章目录'
  });

  // 添加内容区块
  contentWithIds.forEach((item, index) => {
    if (!item || !item.id) return;

    let type = 'other';
    let title = '';
    let content = '';

    if (item.tag) {
      type = item.tag;
      if (['h1', 'h2', 'h3'].includes(item.tag)) {
        title = extractTextFromChildren(item.children || item.text, item.tag.toUpperCase(), index);
        content = title;
      } else if (item.tag === 'p') {
        const text = extractTextFromChildren(item.children || item.text, '段落', index);
        title = '段落';
        content = text.substring(0, 30) + (text.length > 30 ? '...' : '');
      } else if (item.tag === 'img') {
        title = '图片';
        content = item.alt || '图片';
      } else if (item.tag === 'table') {
        title = '表格';
        content = '数据表格';
      } else if (item.tag === 'figcaption') {
        title = '图表说明';
        content = extractTextFromChildren(item.children || item.text, '说明', index);
      } else {
        title = item.tag;
        content = extractTextFromChildren(item.children || item.text, item.tag, index);
      }
    } else if (item.html) {
      type = 'html';
      title = 'HTML内容';
      content = 'HTML区块';
    }

    blocks.push({
      id: item.id,
      type,
      title,
      content: content.substring(0, 50) + (content.length > 50 ? '...' : '')
    });
  });

  // 添加引用区块
  if (citation && citation.trim()) {
    blocks.push({
      id: 'block-citation',
      type: 'citation',
      title: '来源',
      content: citation.substring(0, 50) + (citation.length > 50 ? '...' : '')
    });
  }

  // 添加参考文献区块
  if (refs && refs.trim()) {
    blocks.push({
      id: 'block-refs',
      type: 'refs',
      title: '参考',
      content: refs.substring(0, 50) + (refs.length > 50 ? '...' : '')
    });
  }

  return blocks;
};

// Function to render TOC nodes recursively
const renderTocNode = (node) => {
  const levelStyle = node.level === 2 ? styles.tocLevel2 : {};

  return (
    <li key={node.id} style={{ ...styles.tocItem, ...levelStyle }}>
      <p style={styles.tocParagraph}>
        {node.text}
      </p>
      {node.children && node.children.length > 0 && (
        <ul style={styles.tocList}>
          {node.children.map(renderTocNode)}
        </ul>
      )}
    </li>
  );
};

// Helper function to render content items recursively
const renderContentItem = (item, index, allItems = [], handleTableImageLoadingChange = null, selectedBlocks = new Set(), onBlockToggle = null, showSelectors = false) => {
  if (!item) return null;

  if (typeof item === 'object' && item !== null) {
    // Case 1: HTML content
    if (item.html && typeof item.html === 'string') {
      const blockId = item.id || `block-html-${index}`;
      const isSelected = selectedBlocks.has(blockId);

      if (showSelectors) {
        return (
          <section
            key={index}
            style={{
              ...styles.selectableBlock,
              ...(isSelected ? styles.selectableBlockSelected : {}),
            }}
            onMouseEnter={(e) => {
              if (!isSelected && e.currentTarget) {
                // Directly apply hover style defined in styles object
                Object.assign(e.currentTarget.style, styles.selectableBlockHover);
              }
            }}
            onMouseLeave={(e) => {
              if (!isSelected && e.currentTarget) {
                // Reset to base selectableBlock style (which includes transparent border)
                Object.assign(e.currentTarget.style, styles.selectableBlock);
              }
            }}
          >
            <section
              style={{
                ...styles.blockSelector,
                ...(isSelected ? styles.blockSelectorSelected : {})
              }}
              onClick={() => onBlockToggle && onBlockToggle(blockId)}
            >
              {isSelected ? '✓' : ''}
            </section>
            <section dangerouslySetInnerHTML={{ __html: item.html }} />
          </section>
        );
      }

      return <section key={index} dangerouslySetInnerHTML={{ __html: item.html }} />;
    }

    // Case 2 & 3 combined: Handling items with a 'tag'
    if (item.tag) {
      const TagComponent = item.tag;
      // Destructure known properties, including potential content sources (children, text)
      // and specific attributes for tags like 'a' (href) or 'img' (src, alt).
      const { tag, id, children, text, href, src, alt, ...props } = item;

      const blockId = id || `block-${tag}-${index}`;
      const isSelected = selectedBlocks.has(blockId);

      let contentToRender;

      if (typeof children === 'string') {
        // If children is a string, use it directly.
        contentToRender = children;
      } else if (typeof text === 'string') {
        // Else, if text is a string, use it. This handles cases like { tag: 'p', text: '...' }
        contentToRender = text;
      } else if (Array.isArray(children)) {
        // If children is an array, map over it and render each child item.
        contentToRender = children.map((child, childIndex) => renderContentItem(child, `${index}-${childIndex}`, allItems, handleTableImageLoadingChange, selectedBlocks, onBlockToggle, showSelectors));
      } else if (typeof children === 'object' && children !== null) {
        // If children is an object, it could be a simple { text: '...' } wrapper or another complex element.
        if (children.text && typeof children.text === 'string' && Object.keys(children).length === 1) {
          // Handle simple { text: '...' } objects by extracting the text.
          contentToRender = children.text;
        } else {
          // Otherwise, attempt to render the child object itself (e.g., a nested element like { tag: 'span', ... }).
          contentToRender = renderContentItem(children, `${index}-0`, allItems, handleTableImageLoadingChange, selectedBlocks, onBlockToggle, showSelectors);
        }
      } else {
        // If none of the above, there's no direct content to render from children or text.
        contentToRender = null;
      }

      // 创建可选择的包装器函数
      const createSelectableWrapper = (element) => {
        if (!showSelectors) {
          // 不显示选择器时，直接返回原始元素
          return React.cloneElement(element, {
            key: index
          });
        }

        return (
          <section
            key={index}
            style={{
              ...styles.selectableBlock,
              ...(isSelected ? styles.selectableBlockSelected : {}),
              ...styles.selectableCursor
            }}
            onClick={() => onBlockToggle && onBlockToggle(blockId)}
            onMouseEnter={(e) => {
              if (!isSelected && e.currentTarget) {
                Object.assign(e.currentTarget.style, styles.selectableBlockHover);
              }
            }}
            onMouseLeave={(e) => {
              if (!isSelected && e.currentTarget) {
                Object.assign(e.currentTarget.style, styles.selectableBlock);
              }
            }}
          >
            <section
              data-block-selector="true"
              style={{
                ...styles.blockSelector,
                ...(isSelected ? styles.blockSelectorSelected : {}),
                ...styles.blockSelectorPointerEventsNone
              }}
            >
              {isSelected ? '✓' : ''}
            </section>
            {element}
          </section>
        );
      };

      // Handle specific HTML tags that have unique props or behaviors.
      if (tag === 'a') {
        // Map <a> tags to <span> tags (remove link functionality)
        const { className: aClassName, id: aId, ...cleanProps } = props;
        const element = typeof contentToRender === 'string' ?
          <span key={index} style={styles.link} {...cleanProps} dangerouslySetInnerHTML={{ __html: contentToRender }} /> :
          <span key={index} style={styles.link} {...cleanProps}>{contentToRender}</span>;
        return createSelectableWrapper(element);
      }
      if (tag === 'img') {
        // Img tags are self-closing and use src/alt; contentToRender is usually not applicable.
        const { className: imgClassName, id: imgId, ...cleanProps } = props;
        const element = <SimpleImage
          key={index}
          src={src || ''}
          alt={alt || 'image'}
          {...cleanProps}
        />;
        return createSelectableWrapper(element);
      }
      if (tag === 'figcaption') {
        // Check if this figcaption is above an image or table
        const nextItem = allItems[index + 1];
        const isFigureTitle = nextItem && (nextItem.tag === 'img' || nextItem.tag === 'table');
        const figcaptionStyle = isFigureTitle ? styles.figcaptionTitle : styles.figcaption;
        const { className: figClassName, id: figId, ...cleanProps } = props;

        const element = typeof contentToRender === 'string' ?
          <figcaption key={index} style={figcaptionStyle} {...cleanProps} dangerouslySetInnerHTML={{ __html: contentToRender }} /> :
          <figcaption key={index} style={figcaptionStyle} {...cleanProps}>{contentToRender}</figcaption>;
        return createSelectableWrapper(element);
      }
      if (tag === 'table') {
        // Convert table to image using HtmlToImage component
        const tableHtml = children || text || '';

        if (typeof tableHtml === 'string' && tableHtml.trim()) {
          // 有表格内容的情况
          const completeTableHtml = `
            <style>${styles.tableImageCss}</style>
            <section class="table-container">
              <table>${tableHtml}</table>
            </section>
          `;
          const element = (
            <section
              key={id || index}
              style={styles.tableImageWrapper}
            >
              <HtmlToImage
                htmlString={completeTableHtml}
                onLoadingChange={handleTableImageLoadingChange ? (isLoading) => handleTableImageLoadingChange(`table-${id || index}`, isLoading) : undefined}
              />
            </section>
          );
          return createSelectableWrapper(element);
        } else {
          // 空表格或非HTML内容的情况
          const fallbackTableHtml = `
            <style>${styles.tableImageCss}</style>
            <section class="table-container">
              <table>
                <tr><td>${contentToRender || '空表格'}</td></tr>
              </table>
            </section>
          `;
          const element = (
            <section
              key={id || index}
              style={styles.tableImageWrapper}
            >
              <HtmlToImage
                htmlString={fallbackTableHtml}
                onLoadingChange={handleTableImageLoadingChange ? (isLoading) => handleTableImageLoadingChange(`table-fallback-${id || index}`, isLoading) : undefined}
              />
            </section>
          );
          return createSelectableWrapper(element);
        }
      }

      // Handle heading tags (h1, h2, h3, h4, h5, h6) with appropriate inline styles
      if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tag)) {
        const headingStyle = styles[tag] || styles.h6;
        const combinedStyle = { ...headingStyle, ...props.style };
        const { className: hClassName, style: hStyle, id: hId, ...otherProps } = props; // Remove className, style, and id from props

        const element = typeof contentToRender === 'string' ?
          <TagComponent key={index} style={combinedStyle} {...otherProps} dangerouslySetInnerHTML={{ __html: contentToRender }} /> :
          <TagComponent key={index} style={combinedStyle} {...otherProps}>{contentToRender}</TagComponent>;
        return createSelectableWrapper(element);
      }

      // Handle paragraph tags with consistent inline styles
      if (tag === 'p') {
        const combinedStyle = { ...styles.paragraph, ...props.style };
        const { className: pClassName, style: pStyle, id: pId, ...otherProps } = props; // Remove className, style, and id from props

        const element = typeof contentToRender === 'string' ?
          <p key={index} style={combinedStyle} {...otherProps} dangerouslySetInnerHTML={{ __html: contentToRender }} /> :
          <p key={index} style={combinedStyle} {...otherProps}>{contentToRender}</p>;
        return createSelectableWrapper(element);
      }

      // Default rendering for other tags.
      const { className: defaultClassName, id: defaultId, ...cleanProps } = props;
      const element = typeof contentToRender === 'string' ?
        <TagComponent key={index} {...cleanProps} dangerouslySetInnerHTML={{ __html: contentToRender }} /> :
        <TagComponent key={index} {...cleanProps}>{contentToRender}</TagComponent>;
      return createSelectableWrapper(element);
    }

    // Fallback for unrecognized object structure
    console.warn("Unrecognized object item structure:", item);
    return <span key={index}>{JSON.stringify(item)}</span>;

  }

  console.warn("Unrecognized content item type:", typeof item, item);
  return null;
};

// Function to get initial state and potentially preloaded content
const getInitialSetup = (uuid) => {
  let usedPreloaded = false;
  let initialContent = [];
  let initialState = {
    loading: true,
    error: null,
    articleUuid: uuid,
    title: null,
    createdAt: null,
    summary: null,
    refs: null,
    citation: null
  };

  if (typeof window !== 'undefined' && window.__PRELOADED_STATE__) {
    const preloadedData = window.__PRELOADED_STATE__;
    delete window.__PRELOADED_STATE__;

    if (preloadedData && Array.isArray(preloadedData.translated) && preloadedData.articleUuid === uuid) {
      console.log("Using valid preloaded state.");
      initialContent = preloadedData.translated.filter(item => item !== null);
      initialState = {
        ...initialState,
        title: preloadedData.title || null,
        createdAt: preloadedData.createdAt || null,
        summary: preloadedData.summary || null,
        refs: preloadedData.refs || null,
        citation: preloadedData.citation || null,
        loading: false,
        error: null
      };
      usedPreloaded = true;
    } else if (preloadedData) {
      console.warn("Preloaded state invalid or UUID mismatch. Will fetch.", preloadedData);
    } else {
      console.log("No preloaded state found, will attempt client-side fetch.");
    }
  } else {
    console.log("Window or preloaded state not found, will attempt client-side fetch.");
  }

  return { initialState, initialContent, usedPreloaded };
};

const ArticlePreviewPage = () => {
  // 添加全局字体样式到 head - 必须在所有其他 hooks 之前
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = styles.fontFace;
    document.head.appendChild(styleElement);

    // 应用全局字体到所有元素
    const globalStyle = document.createElement('style');
    globalStyle.textContent = styles.globalFontStyles;
    document.head.appendChild(globalStyle);

    return () => {
      document.head.removeChild(styleElement);
      document.head.removeChild(globalStyle);
    };
  }, []);

  const { uuid } = useParams();
  const { initialState, initialContent, usedPreloaded } = useMemo(() => getInitialSetup(uuid), [uuid]);

  const [content, setContent] = useState(initialContent);
  const [loading, setLoading] = useState(initialState.loading);
  const [error, setError] = useState(initialState.error);
  const [articleTitle, setArticleTitle] = useState(initialState.title);
  const [createdAt, setCreatedAt] = useState(initialState.createdAt);
  const [summary, setSummary] = useState(initialState.summary);
  const [refs, setRefs] = useState(initialState.refs);
  const [citation, setCitation] = useState(initialState.citation);
  const [isCopying, setIsCopying] = useState(false);
  const [tableImagesLoading, setTableImagesLoading] = useState(new Set());

  // 区块选择相关状态
  const [selectedBlocks, setSelectedBlocks] = useState(new Set());
  const [showSelectors, setShowSelectors] = useState(false);

  // 创建ref来引用内容区域
  const contentRef = useRef(null);

  // 处理表格图片加载状态
  const handleTableImageLoadingChange = (tableId, isLoading) => {
    setTableImagesLoading(prev => {
      const newSet = new Set(prev);
      if (isLoading) {
        newSet.add(tableId);
      } else {
        newSet.delete(tableId);
      }
      return newSet;
    });
  };

  // 检查是否有表格图片正在加载
  const hasLoadingTableImages = tableImagesLoading.size > 0;

  // 生成区块信息
  const { toc: tocData, contentWithIds } = useMemo(() => {
    if (!content || content.length === 0) {
      return { toc: [], contentWithIds: [] };
    }
    return generateTocAndContentWithIds(content);
  }, [content]);

  const blockInfo = useMemo(() => {
    return generateBlockInfo(contentWithIds, summary, citation, refs);
  }, [contentWithIds, summary, citation, refs]);

  // 初始化选中的区块（默认全选）
  useEffect(() => {
    if (blockInfo.length > 0) {
      const allBlockIds = new Set(blockInfo.map(block => block.id));
      setSelectedBlocks(allBlockIds);
    }
  }, [blockInfo]);

  // 区块选择处理函数
  const handleBlockToggle = (blockId) => {
    setSelectedBlocks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(blockId)) {
        newSet.delete(blockId);
      } else {
        newSet.add(blockId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    const allBlockIds = new Set(blockInfo.map(block => block.id));
    setSelectedBlocks(allBlockIds);
  };

  const handleSelectNone = () => {
    setSelectedBlocks(new Set());
  };



  // 过滤选中内容的辅助函数
  const filterSelectedContent = (clonedContent) => {
    // 获取所有需要检查的区块元素
    const elementsToCheck = [];

    // AI总结
    if (!selectedBlocks.has('block-summary')) {
      const summarySection = clonedContent.querySelector('section:has(h1)');
      if (summarySection && summarySection.querySelector('h1')?.textContent === 'AI总结') {
        elementsToCheck.push(summarySection);
      }
    }

    // 目录
    if (!selectedBlocks.has('block-toc')) {
      const tocSection = clonedContent.querySelector('nav[aria-label="目录"]');
      if (tocSection) {
        elementsToCheck.push(tocSection);
      }
    }

    // 来源
    if (!selectedBlocks.has('block-citation')) {
      const h1Elements = clonedContent.querySelectorAll('h1');
      for (let h1 of h1Elements) {
        if (h1.textContent === '来源') {
          elementsToCheck.push(h1.parentElement);
          break;
        }
      }
    }

    // 参考文献
    if (!selectedBlocks.has('block-refs')) {
      const h1Elements = clonedContent.querySelectorAll('h1');
      for (let h1 of h1Elements) {
        if (h1.textContent === '参考') {
          elementsToCheck.push(h1.parentElement);
          break;
        }
      }
    }

    // 内容区块 - 查找主要内容容器
    const contentSections = clonedContent.querySelectorAll('section > section');
    if (contentSections.length > 1) {
      const contentContainer = contentSections[1]; // 主要内容容器
      const contentItems = Array.from(contentContainer.children);

      // 反向遍历，避免删除元素时索引变化的问题
      for (let i = contentItems.length - 1; i >= 0; i--) {
        const item = contentItems[i];
        const correspondingContentItem = contentWithIds[i];

        if (correspondingContentItem && correspondingContentItem.id && !selectedBlocks.has(correspondingContentItem.id)) {
          elementsToCheck.push(item);
        }
      }
    }

    // 移除未选中的元素
    elementsToCheck.forEach(element => {
      if (element && element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });
  };

  // 清理元素样式的辅助函数
  const cleanElementStyles = (element) => {
    // 移除选择相关的样式属性
    if (element.style) {
      element.style.removeProperty('border');
      element.style.removeProperty('background-color');
      element.style.removeProperty('cursor');
    }

    // 递归清理子元素
    Array.from(element.children).forEach(child => {
      cleanElementStyles(child);
    });
  };

  // 精简的复制富文本功能 - 统一处理全文和选择模式
  const copyAsRichText = async () => {
    if (!contentRef.current || isCopying) return;

    setIsCopying(true);
    try {
      console.log('开始复制富文本...');

      // 克隆整个内容区域
      const clonedContent = contentRef.current.cloneNode(true);

      // 移除所有选择器元素和清理样式
      const selectors = clonedContent.querySelectorAll('[data-block-selector]');
      selectors.forEach(selector => selector.remove());
      cleanElementStyles(clonedContent);

      // 如果是选择模式且没有全选，则过滤内容
      if (showSelectors && selectedBlocks.size > 0 && selectedBlocks.size < blockInfo.length) {
        filterSelectedContent(clonedContent);
      }

      // 检查是否有userscript提供的图片转base64功能
      if (typeof window.convertImagesToBase64 === 'function') {
        console.log('检测到userscript，使用base64转换功能');
        await window.convertImagesToBase64(clonedContent);
      } else {
        console.log('未检测到userscript，使用原始图片URL');
      }

      // 获取最终HTML
      const finalHtml = clonedContent.outerHTML;

      // 使用浏览器原生复制功能
      await copyWithNativeAPI(finalHtml);

      console.log('✅ 富文本已复制到剪贴板！');
      console.log('📄 复制的内容长度:', finalHtml.length, '字符');
      console.log('📋 已选择区块数量:', selectedBlocks.size);

    } catch (error) {
      console.error('❌ 复制富文本失败:', error);
    } finally {
      setIsCopying(false);
    }
  };

  // 原生浏览器复制功能（备用）
  const copyWithNativeAPI = async (htmlContent) => {
    try {
      if (navigator.clipboard && navigator.clipboard.write) {
        const textContent = htmlToPlainText(htmlContent);
        const clipboardItem = new ClipboardItem({
          'text/html': new Blob([htmlContent], { type: 'text/html' }),
          'text/plain': new Blob([textContent], { type: 'text/plain' })
        });
        await navigator.clipboard.write([clipboardItem]);
      } else {
        // 降级到纯文本复制
        const textContent = htmlToPlainText(htmlContent);
        await navigator.clipboard.writeText(textContent);
      }
    } catch (error) {
      console.error('原生复制API失败:', error);
      throw error;
    }
  };

  // 将HTML转换为纯文本的辅助函数
  const htmlToPlainText = (html) => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    const images = tempDiv.querySelectorAll('img');
    images.forEach(img => {
      const alt = img.getAttribute('alt') || '[图片]';
      img.replaceWith(document.createTextNode(alt));
    });
    const tables = tempDiv.querySelectorAll('table');
    tables.forEach(table => {
      table.replaceWith(document.createTextNode('[表格]'));
    });
    const brs = tempDiv.querySelectorAll('br');
    brs.forEach(br => {
      br.replaceWith(document.createTextNode('\n'));
    });
    const blocks = tempDiv.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6, section');
    blocks.forEach(block => {
      block.insertAdjacentText('afterend', '\n');
    });
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  useEffect(() => {
    if (!uuid) {
      setError('无效的文章标识符');
      setLoading(false);
      setContent([]); // Clear content if no UUID
      setArticleTitle(null);
      setCreatedAt(null);
      setSummary(null);
      setRefs(null);
      setCitation(null);
      return;
    }

    // The getInitialSetup is already called via useMemo and its results (initialState, initialContent, usedPreloaded)
    // are used to set the initial state of content, doi, loading, error.
    // So, if usedPreloaded is true, the content is already set from preloaded data, and loading should be false.

    if (usedPreloaded) {
        // Data was preloaded, states are already set from initialState
        // No need to fetch data again
        return;
    }

    // If not preloaded, then fetch
    const fetchPreviewData = async () => {
      setLoading(true); // Set loading true just before fetch
      setError(null);   // Reset error just before fetch
      console.log(`Client-side fetching data for UUID: ${uuid}`);
      try {
        const response = await fetch(`${apiBaseUrl}api/article/${uuid}`);
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: `HTTP error! status: ${response.status}` }));
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data && Array.isArray(data.translated)) {
          const filteredContent = data.translated.filter(item => item !== null);
          setContent(filteredContent);
          setArticleTitle(data.title || '');
          setCreatedAt(data.createdAt || null);
          setSummary(data.summary || null);
          setRefs(data.refs || null);
          setCitation(data.citation || null);
        } else {
          throw new Error('从服务器获取的数据格式无效');
        }
      } catch (err) {
        setError(err.message || '加载预览时发生错误');
        setContent([]); // Clear content on error
        setArticleTitle(null);
        setCreatedAt(null);
        setSummary(null);
        setRefs(null);
        setCitation(null);
      } finally {
        setLoading(false);
      }
    };

    fetchPreviewData();

  }, [uuid, usedPreloaded]); // Removed initialState dependencies



  if (loading) {
    return <section style={styles.articlePreviewLoading}>正在加载...</section>;
  }

  if (error) {
    return (
      <section style={styles.articlePreviewError}>
        <strong style={styles.errorStrong}>加载错误</strong>
        <p>{error}</p>
      </section>
    );
  }

  if (!contentWithIds || contentWithIds.length === 0) {
     return (
       <section style={styles.articlePreviewNoContent}>
         <strong style={styles.errorStrong}>无内容</strong>
         <p>未找到该文章的翻译内容。</p>
       </section>
     );
  }

  return (
    <section style={styles.articlePreviewContainer}>
      {/* 复制富文本按钮 */}
      <button
        onClick={copyAsRichText}
        disabled={isCopying || hasLoadingTableImages}
        style={{
          ...styles.copyButton,
          ...((isCopying || hasLoadingTableImages) ? styles.copyButtonDisabled : {})
        }}
        onMouseEnter={(e) => {
          if (!isCopying && !hasLoadingTableImages && e.target) {
            Object.assign(e.target.style, styles.copyButtonHover);
          }
        }}
        onMouseLeave={(e) => {
          if (!isCopying && !hasLoadingTableImages && e.target) {
            Object.assign(e.target.style, styles.copyButton);
          }
        }}
        onMouseDown={(e) => {
          if (!isCopying && !hasLoadingTableImages && e.target) {
            Object.assign(e.target.style, styles.copyButtonActive);
          }
        }}
        onMouseUp={(e) => {
          if (!isCopying && !hasLoadingTableImages && e.target) {
            // On mouse up, revert to hover state if mouse is still over, otherwise base
             Object.assign(e.target.style, styles.copyButtonHover);
          }
        }}
      >
        {showSelectors ? `复制选中 (${selectedBlocks.size})` : '复制全文'}
      </button>

      {/* 选择模式切换按钮 */}
      <button
        onClick={() => {
          setShowSelectors(!showSelectors);
        }}
        style={{
          ...styles.selectModeButton,
          ...(showSelectors ? styles.selectModeButtonActiveBg : styles.selectModeButtonInactiveBg)
        }}
      >
        {showSelectors ? '退出选择' : '选择区块'}
      </button>

      {/* 快捷操作按钮 */}
      {showSelectors && (
        <section style={styles.quickActionsContainer}>
          <button
            onClick={handleSelectAll}
            style={{
              ...styles.controlButton,
              ...(selectedBlocks.size === blockInfo.length ? styles.controlButtonActive : {}),
              ...styles.quickActionButton
            }}
          >
            全选
          </button>
          <button
            onClick={handleSelectNone}
            style={{
              ...styles.controlButton,
              ...(selectedBlocks.size === 0 ? styles.controlButtonActive : {}),
              ...styles.quickActionButton
            }}
          >
            全不选
          </button>
        </section>
      )}

      {/* Article title and date */}
      <section style={styles.articleHeader}>
        {articleTitle && (
          <h1 style={styles.articleTitle}>{articleTitle}</h1>
        )}
        {createdAt && (
          <section style={styles.articleDate}>{formatDate(createdAt)}</section>
        )}
      </section>

      <section ref={contentRef}>
        {/* AI Summary */}
        {summary && summary.trim() && (
        <section
          style={{
            ...styles.summaryContainer,
            ...(showSelectors ? styles.selectableBlock : {}),
            ...(showSelectors && selectedBlocks.has('block-summary') ? styles.selectableBlockSelected : {}),
            ...(showSelectors ? styles.selectableCursor : {})
          }}
          onClick={showSelectors ? () => handleBlockToggle('block-summary') : undefined}
          onMouseEnter={(e) => {
            if (showSelectors && !selectedBlocks.has('block-summary') && e.currentTarget) {
              Object.assign(e.currentTarget.style, styles.selectableBlockHover);
            }
          }}
          onMouseLeave={(e) => {
            if (showSelectors && !selectedBlocks.has('block-summary') && e.currentTarget) {
               Object.assign(e.currentTarget.style, styles.selectableBlock);
            }
          }}
        >
          {showSelectors && (
            <section
              data-block-selector="true"
              style={{
                ...styles.blockSelector,
                ...(selectedBlocks.has('block-summary') ? styles.blockSelectorSelected : {}),
                ...styles.blockSelectorPointerEventsNone
              }}
            >
              {selectedBlocks.has('block-summary') ? '✓' : ''}
            </section>
          )}
          <h1 style={styles.h1}>AI总结</h1>
          <section style={styles.summaryContent}>
            {summary.split('\n').map((line, index) => (
              <p key={index} style={{...styles.summaryParagrah}}>{line}</p>
            ))}
          </section>
          <hr style={styles.summarySeparator} />
        </section>
      )}

      {tocData && tocData.length > 0 && (
        <nav
          style={{
            ...styles.tocContainer,
            ...(showSelectors ? styles.selectableBlock : {}),
            ...(showSelectors && selectedBlocks.has('block-toc') ? styles.selectableBlockSelected : {}),
            ...(showSelectors ? styles.selectableCursor : {})
          }}
          aria-label="目录"
          onClick={showSelectors ? () => handleBlockToggle('block-toc') : undefined}
          onMouseEnter={(e) => {
            if (showSelectors && !selectedBlocks.has('block-toc') && e.currentTarget) {
              Object.assign(e.currentTarget.style, styles.selectableBlockHover);
            }
          }}
          onMouseLeave={(e) => {
            if (showSelectors && !selectedBlocks.has('block-toc') && e.currentTarget) {
              Object.assign(e.currentTarget.style, styles.selectableBlock);
            }
          }}
        >
          {showSelectors && (
            <section
              data-block-selector="true"
              style={{
                ...styles.blockSelector,
                ...(selectedBlocks.has('block-toc') ? styles.blockSelectorSelected : {}),
                ...styles.blockSelectorPointerEventsNone
              }}
            >
              {selectedBlocks.has('block-toc') ? '✓' : ''}
            </section>
          )}
          <h1 style={styles.h1}>目录</h1>
          <section style={styles.tocContent}>
            <ul style={styles.tocList}>
              {tocData.map(renderTocNode)}
            </ul>
          </section>
          <hr style={styles.tocSeparator} />
        </nav>
      )}

      {/* Main article content */}
      <section>
        <section>
          {contentWithIds.map((item, index) => renderContentItem(item, index, contentWithIds, handleTableImageLoadingChange, selectedBlocks, handleBlockToggle, showSelectors))}
        </section>
      </section>

      {/* Citation */}
      {citation && citation.trim() && (
        <section
          style={{
            ...styles.citationContainer,
            ...(showSelectors ? styles.selectableBlock : {}),
            ...(showSelectors && selectedBlocks.has('block-citation') ? styles.selectableBlockSelected : {}),
            ...(showSelectors ? styles.selectableCursor : {})
          }}
          onClick={showSelectors ? () => handleBlockToggle('block-citation') : undefined}
          onMouseEnter={(e) => {
            if (showSelectors && !selectedBlocks.has('block-citation') && e.currentTarget) {
              Object.assign(e.currentTarget.style, styles.selectableBlockHover);
            }
          }}
          onMouseLeave={(e) => {
            if (showSelectors && !selectedBlocks.has('block-citation') && e.currentTarget) {
              Object.assign(e.currentTarget.style, styles.selectableBlock);
            }
          }}
        >
          {showSelectors && (
            <section
              data-block-selector="true"
              style={{
                ...styles.blockSelector,
                ...(selectedBlocks.has('block-citation') ? styles.blockSelectorSelected : {}),
                ...styles.blockSelectorPointerEventsNone
              }}
            >
              {selectedBlocks.has('block-citation') ? '✓' : ''}
            </section>
          )}
          <h1 style={styles.h1}>来源</h1>
          <section>
            {citation.split('\n').map((line, index) => (
              <p key={index} style={styles.paragraph}>{line}</p>
            ))}
          </section>
        </section>
      )}

      {/* References */}
      {refs && refs.trim() && (
        <section
          style={{
            ...styles.refsContainer,
            ...(showSelectors ? styles.selectableBlock : {}),
            ...(showSelectors && selectedBlocks.has('block-refs') ? styles.selectableBlockSelected : {}),
            ...(showSelectors ? styles.selectableCursor : {})
          }}
          onClick={showSelectors ? () => handleBlockToggle('block-refs') : undefined}
          onMouseEnter={(e) => {
            if (showSelectors && !selectedBlocks.has('block-refs') && e.currentTarget) {
              Object.assign(e.currentTarget.style, styles.selectableBlockHover);
            }
          }}
          onMouseLeave={(e) => {
            if (showSelectors && !selectedBlocks.has('block-refs') && e.currentTarget) {
              Object.assign(e.currentTarget.style, styles.selectableBlock);
            }
          }}
        >
          {showSelectors && (
            <section
              data-block-selector="true"
              style={{
                ...styles.blockSelector,
                ...(selectedBlocks.has('block-refs') ? styles.blockSelectorSelected : {}),
                ...styles.blockSelectorPointerEventsNone
              }}
            >
              {selectedBlocks.has('block-refs') ? '✓' : ''}
            </section>
          )}
          <h1 style={styles.h1}>参考</h1>
          <section style={styles.refsScrollContainer}>
            {(() => {
              const refsLines = refs.split('\n').filter(line => line.trim());
              const chunks = [];
              for (let i = 0; i < refsLines.length; i += 20) {
                chunks.push(refsLines.slice(i, i + 20));
              }
              return chunks.map((chunk, chunkIndex) => (
                <section key={chunkIndex} style={styles.refsImageWrapper}>
                  <HtmlToImage
                    htmlString={`
                      <style>${styles.refsImageCss}</style>
                      <section class="refs-container">
                        ${chunk.map(line => `<p>${line}</p>`).join('')}
                      </section>
                    `}
                    contentType="refs"
                    onLoadingChange={handleTableImageLoadingChange ? (isLoading) => handleTableImageLoadingChange(`refs-content-${chunkIndex}`, isLoading) : undefined}
                  />
                </section>
              ));
            })()}
          </section>
        </section>
      )}
      </section>
    </section>
  );
};

export default ArticlePreviewPage;
