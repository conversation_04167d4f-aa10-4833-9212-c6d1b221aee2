import React, { memo, useRef, useState, useEffect, useCallback } from 'react';
import { Row, Col, Input, Space, Button, Popconfirm, Divider } from 'antd';
import { EditorView } from '@codemirror/view'; // Import EditorView
import {
  EditOutlined, SaveOutlined, CloseOutlined, ArrowRightOutlined,
  PauseOutlined, DeleteOutlined, ClearOutlined
} from '@ant-design/icons';
import { isEqual } from 'lodash';
import { useStore } from '../stores/index.js';
import ThinkDisplay from './ThinkDisplay';
import EnhanceTextArea from './EnhanceTextArea';

// 合并编辑显示组件
const MergedEditDisplay = React.forwardRef(({ type, mergedText, setMergedText, onSave, onCancel, toolbarHeight = 80 }, ref) => {
  // 按钮可见性状态
  const [isButtonsFixed, setIsButtonsFixed] = useState(false);
  const [fixedButtonPosition, setFixedButtonPosition] = useState({ bottom: '20px', right: '20px' });
  const buttonsRef = useRef(null);
  const containerRef = useRef(null);
  // const textAreaRef = useRef(null); // 将使用 forwarded ref


  // 处理输入变化 - 直接传递给 setMergedText，防抖由 EnhanceTextArea 处理
  const handleTextChange = (e) => {
    const value = e.target.value;
    setMergedText(value);
  };

  // 检查按钮是否需要固定，并计算固定位置
  const checkButtonVisibility = useCallback(() => {
    if (!buttonsRef.current || !containerRef.current) return;

    const buttonsRect = buttonsRef.current.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // 如果按钮底部超出了视口底部，则固定按钮
    const shouldFix = buttonsRect.bottom > viewportHeight - 20; // 留20px缓冲区

    // 无论是否需要固定，都预先计算位置，避免闪烁
    const visibleBottom = Math.min(containerRect.bottom, viewportHeight);
    const visibleRight = Math.min(containerRect.right, viewportWidth);

    const newPosition = {
      bottom: `${viewportHeight - visibleBottom + 10}px`, // 距离视口底部的距离
      right: `${viewportWidth - visibleRight + 10}px`     // 距离视口右侧的距离
    };

    // 同时更新位置和固定状态，避免状态更新的时间差
    setFixedButtonPosition(newPosition);
    setIsButtonsFixed(shouldFix);


  }, []);

  // 搜索最佳匹配位置的函数
  const findBestMatchPosition = useCallback((keywords, codeMirrorView) => {
    if (!keywords || !codeMirrorView) return null;

    try {
      const doc = codeMirrorView.state.doc;
      const lineCount = doc.lines;
      const searchKeywords = keywords.substring(0, 30).trim();

      let bestMatch = null;
      let bestScore = 0;

      // 搜索整个文档
      for (let i = 1; i <= lineCount; i++) {
        try {
          const lineObj = doc.line(i);
          const lineText = lineObj.text;

          // 计算匹配分数
          let score = 0;
          if (lineText.includes(searchKeywords)) {
            score = 100; // 完全匹配
          } else {
            // 部分匹配 - 计算相似度
            const words = searchKeywords.split(' ').filter(w => w.length > 2);
            for (const word of words) {
              if (lineText.includes(word)) {
                score += 20;
              }
            }
          }

          if (score > bestScore) {
            bestScore = score;
            bestMatch = { line: i, score, text: lineText };
          }
        } catch (e) {
          // 忽略单行错误
        }
      }

      return bestMatch;
    } catch (error) {
      console.error('[MergedEditDisplay] 搜索过程出错:', error);
      return null;
    }
  }, []);

  // 验证定位是否准确的函数
  const verifyPositionAccuracy = useCallback((targetLine, keywords, codeMirrorView) => {
    if (!keywords || !codeMirrorView) return { isAccurate: true, bestMatch: null };

    try {
      const doc = codeMirrorView.state.doc;
      const lineCount = doc.lines;

      // 检查目标行周围的内容（前后各3行）
      const searchRange = 3;
      const startLine = Math.max(1, targetLine - searchRange);
      const endLine = Math.min(lineCount, targetLine + searchRange);

      let foundMatch = false;
      let searchText = '';

      for (let i = startLine; i <= endLine; i++) {
        try {
          const lineObj = doc.line(i);
          const lineText = lineObj.text;
          searchText += lineText + ' ';

          // 检查是否包含关键词
          if (lineText.includes(keywords.substring(0, 20))) {
            foundMatch = true;

            break;
          }
        } catch (e) {
          // 忽略单行错误，继续检查其他行
        }
      }

      let bestMatch = null;
      if (!foundMatch) {


        // 搜索最佳匹配位置
        bestMatch = findBestMatchPosition(keywords, codeMirrorView);
      }

      const result = { isAccurate: foundMatch, bestMatch, searchText };
      console.log('[MergedEditDisplay DEBUG] verifyPositionAccuracy - Result:', result);
      return result;
    } catch (error) {
      console.error('[MergedEditDisplay] 验证过程出错:', error);
      return { isAccurate: false, bestMatch: null };
    }
  }, [findBestMatchPosition]);

  // 监听滚动定位事件
  useEffect(() => {
    const handleScrollToLine = (event) => {
      const { line, type: eventType, keywords, chunkIndex } = event.detail;
      console.log('[MergedEditDisplay DEBUG] Received scrollToLine event:', { line, eventType, keywords, chunkIndex, type });

      // 只处理匹配当前类型的事件
      if (eventType !== type) return;



      // 尝试立即执行，或使用极短延迟
      setTimeout(() => {
        if (ref && ref.current) { // 使用传递进来的 ref
          // 尝试获取 CodeMirror 实例 - 使用正确的 API
          const codeMirrorView = ref.current.view; // 使用传递进来的 ref

          if (codeMirrorView) {
            // 使用 CodeMirror 6 API 定位到指定行
            const targetLine = Math.max(1, line); // CodeMirror 6 行号从1开始
            const doc = codeMirrorView.state.doc;
            const lineCount = doc.lines;
            const actualLine = Math.min(targetLine, lineCount);



            try {
              // 验证定位准确性
              const { isAccurate, bestMatch } = verifyPositionAccuracy(actualLine, keywords, codeMirrorView);

              // 如果验证失败且找到了更好的匹配位置，使用最佳匹配
              let finalLine = actualLine;
              if (!isAccurate && bestMatch && bestMatch.score > 50) {
                console.log('[MergedEditDisplay DEBUG] Accuracy check failed, using bestMatch.line:', bestMatch.line);
                finalLine = bestMatch.line;
              }

              // 获取最终行的位置
              const lineObj = doc.line(finalLine);
              const pos = lineObj.from;

              // 设置光标位置
              console.log('[MergedEditDisplay DEBUG] Dispatching scroll. Final target line:', finalLine, 'Initial line was:', actualLine, 'Pos:', pos);
              // 尝试获取工具栏高度，如果存在的话
              let toolbarHeight = 80; // 默认工具栏高度
              const articleControlsToolbar = document.querySelector('.article-controls');
              if (articleControlsToolbar) {
                toolbarHeight = articleControlsToolbar.getBoundingClientRect().height;
              }
              
              codeMirrorView.dispatch({
                effects: EditorView.scrollIntoView(pos, {
                  y: "start" // 移除了 yMargin，先尝试滚动到纯粹的顶部
                })
              });
              // 滚动后可能需要再次设置selection，因为scrollIntoView本身可能不移动光标
              codeMirrorView.dispatch({
                 selection: { anchor: pos, head: pos }
              });

              // 聚焦编辑器
              codeMirrorView.focus();

              // 新增日志：获取并打印 CodeMirror 当前光标所在行的信息
              const currentSelection = codeMirrorView.state.selection.main;
              if (currentSelection) {
                const currentLineNumber = codeMirrorView.state.doc.lineAt(currentSelection.head).number;
                const currentLineText = codeMirrorView.state.doc.line(currentLineNumber).text;
                console.log('[MergedEditDisplay DEBUG] CodeMirror actual new caret position - Line Number:', currentLineNumber, 'Line Text:', currentLineText);
              }

              // 新增：延迟后再次检查视口和光标
              requestAnimationFrame(() => {
                if (codeMirrorView && codeMirrorView.state && !codeMirrorView.isDestroyed) { // 确保 view 仍然有效且未销毁
                  const finalSelection = codeMirrorView.state.selection.main;
                  if (finalSelection) {
                     const finalLineNumber = codeMirrorView.state.doc.lineAt(finalSelection.head).number;
                     const finalLineText = codeMirrorView.state.doc.line(finalLineNumber).text;
                     const visibleRanges = codeMirrorView.visibleRanges; // 获取可见范围
                     console.log('[MergedEditDisplay DEBUG] After animation frame - Final Caret Line:', finalLineNumber, 'Text:', finalLineText);
                     console.log('[MergedEditDisplay DEBUG] After animation frame - Visible Ranges:', JSON.stringify(visibleRanges.map(r => ({from: r.from, to: r.to, fromLine: codeMirrorView.state.doc.lineAt(r.from).number, toLine: codeMirrorView.state.doc.lineAt(r.to).number }))));
                     if (visibleRanges.length > 0) {
                        const topLineInView = codeMirrorView.state.doc.lineAt(visibleRanges[0].from).number;
                        console.log('[MergedEditDisplay DEBUG] After animation frame - Top line in view:', topLineInView);
                     }
                     // Add more debug info about CodeMirror's state
                     console.log('[MergedEditDisplay DEBUG] CM scrollDOM.scrollTop:', codeMirrorView.scrollDOM.scrollTop);
                     console.log('[MergedEditDisplay DEBUG] CM scrollDOM.scrollHeight:', codeMirrorView.scrollDOM.scrollHeight);
                     console.log('[MergedEditDisplay DEBUG] CM dom.clientHeight:', codeMirrorView.dom.clientHeight);
                     console.log('[MergedEditDisplay DEBUG] CM contentHeight:', codeMirrorView.contentHeight);
                     try {
                       const line33Info = codeMirrorView.state.doc.line(33);
                       // To get the top position of a line, we can use lineBlockAt(position).top
                       // However, lineBlockAt requires a character position, not a line number directly.
                       // We'll get the 'from' position of line 33.
                       const line33BlockInfo = codeMirrorView.lineBlockAt(line33Info.from);
                       console.log('[MergedEditDisplay DEBUG] CM Line 33 info: from=', line33Info.from, 'length=', line33Info.length, 'text=', line33Info.text.substring(0,30) + '...');
                       console.log('[MergedEditDisplay DEBUG] CM Line 33 block: top=', line33BlockInfo.top, 'height=', line33BlockInfo.height);
                     } catch (e) {
                       console.warn('[MergedEditDisplay DEBUG] Could not get info for line 33:', e.message);
                     }

                     // Attempt to scroll the main page body
                     try {
                       const targetCmLineNumber = finalLineNumber; // The line we want at the top (after CM internal scroll)
                       const targetCmLineInfo = codeMirrorView.state.doc.line(targetCmLineNumber);
                       const targetCmLineBlock = codeMirrorView.lineBlockAt(targetCmLineInfo.from);
                       const targetLineTopInCmContent = targetCmLineBlock.top; // Top of the target line relative to CM content start

                       const cmEditorDom = codeMirrorView.dom;
                       const cmEditorRect = cmEditorDom.getBoundingClientRect(); // CM editor's position relative to viewport

                       let toolbarH = 0;
                       const toolbarElem = document.querySelector('.article-controls');
                       if (toolbarElem) {
                         toolbarH = toolbarElem.getBoundingClientRect().height;
                       }
                       const desiredMarginFromToolbar = 60; // px, Further increased margin

                       // Calculate where the target line currently IS relative to the document top
                       // document.body.scrollTop: how much the body is already scrolled
                       // cmEditorRect.top: where the CM editor's top is in the current viewport
                       // targetLineTopInCmContent: where the target line is within the CM content (assuming CM scrollTop is 0, which it is in this case)
                       const currentTargetLineAbsoluteY = document.body.scrollTop + cmEditorRect.top + targetLineTopInCmContent;
                       
                       // We want the target line to be at (toolbarH + desiredMarginFromToolbar) from the viewport top
                       const desiredTargetLineViewportY = toolbarH + desiredMarginFromToolbar;
                       
                       // So, the body needs to scroll by the difference
                       const newBodyScrollTop = currentTargetLineAbsoluteY - desiredTargetLineViewportY;

                       console.log(`[MergedEditDisplay DEBUG] Body Scroll Attempt:
                         Target CM Line: ${targetCmLineNumber}
                         Line Top in CM Content: ${targetLineTopInCmContent}
                         CM Editor Viewport Top: ${cmEditorRect.top}
                         Current Body ScrollTop: ${document.body.scrollTop}
                         Toolbar Height: ${toolbarH}
                         Current Target Line Absolute Y: ${currentTargetLineAbsoluteY}
                         Desired Target Line Viewport Y: ${desiredTargetLineViewportY}
                         New Body ScrollTop: ${newBodyScrollTop}`);

                       if (newBodyScrollTop >= 0 && Math.abs(document.body.scrollTop - newBodyScrollTop) > 5) { // Only scroll if significantly different
                         document.body.scrollTo({ top: newBodyScrollTop, behavior: 'auto' }); // Changed to 'auto'
                         console.log('[MergedEditDisplay DEBUG] Executed document.body.scrollTo:', newBodyScrollTop);
                       } else {
                         console.log('[MergedEditDisplay DEBUG] document.body.scrollTo not executed or no significant change needed.');
                       }
                     } catch(e) {
                       console.warn('[MergedEditDisplay DEBUG] Error during body scroll attempt:', e);
                     }
                  }
                }
              });

              // 如果使用了最佳匹配，显示信息
              if (finalLine !== actualLine) {
                console.info(`[MergedEditDisplay] 已自动调整到最佳匹配位置：第${finalLine}行`);
              } else if (!isAccurate && keywords) {
                console.warn('[MergedEditDisplay] 定位可能不准确，请检查内容是否匹配');
              }
            } catch (error) {
              console.error('[MergedEditDisplay] CodeMirror 定位失败:', error);
            }
          }
        }
      }, 0); //  大幅减少延迟，尝试使其更直接
    };

    window.addEventListener('scrollToLine', handleScrollToLine);

    return () => {
      window.removeEventListener('scrollToLine', handleScrollToLine);
    };
  }, [type, verifyPositionAccuracy, findBestMatchPosition]); // Added findBestMatchPosition to dependencies

  // 监听按钮可见性
  useEffect(() => {
    const buttonsElement = buttonsRef.current;
    if (!buttonsElement) return;

    // 使用 Intersection Observer 监听按钮是否在视口中
    const observer = new IntersectionObserver(
      ([entry]) => {
        // 当按钮不在视口中时，固定在底部
        const shouldFix = !entry.isIntersecting;


        // 如果需要固定，立即计算位置
        if (shouldFix && containerRef.current) {
          const containerRect = containerRef.current.getBoundingClientRect();
          const viewportHeight = window.innerHeight;
          const viewportWidth = window.innerWidth;

          const visibleBottom = Math.min(containerRect.bottom, viewportHeight);
          const visibleRight = Math.min(containerRect.right, viewportWidth);

          setFixedButtonPosition({
            bottom: `${viewportHeight - visibleBottom + 10}px`,
            right: `${viewportWidth - visibleRight + 10}px`
          });
        }

        setIsButtonsFixed(shouldFix);
      },
      {
        root: null, // 相对于视口
        rootMargin: '-20px', // 提前20px触发
        threshold: 0 // 只要有一点不可见就触发
      }
    );

    observer.observe(buttonsElement);

    // 初始检查
    setTimeout(checkButtonVisibility, 100);

    // 监听滚动和窗口大小变化
    const handleScroll = () => {
      setTimeout(checkButtonVisibility, 10);
    };
    const handleResize = () => {
      setTimeout(checkButtonVisibility, 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize, { passive: true });

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [checkButtonVisibility]);

  // 渲染按钮组件
  const renderButtons = () => (
    <Space>
      <Button
        icon={<SaveOutlined />}
        onClick={onSave}
        size="small"
      >
        保存
      </Button>
      <Button
        icon={<CloseOutlined />}
        onClick={onCancel}
        size="small"
      >
        取消
      </Button>
    </Space>
  );

  return (
    <div ref={containerRef} style={{
      display: 'flex',
      flexDirection: 'column',
      width: '100%',
      maxWidth: '100%',
      overflow: 'hidden' // 防止容器溢出
    }}>
      {/* 自适应高度的 textarea */}
      <EnhanceTextArea
        ref={ref} // 使用 forwarded ref
        value={mergedText}
        onChange={handleTextChange}
        showLineNumbers={true}
        style={{
          width: '100%',
          maxWidth: '100%',
          fontSize: '13px', // 调小字体到11px
          lineHeight: '1.6',
          resize: 'none', // 禁用手动调整大小，让内容自适应
          boxSizing: 'border-box' // 确保边框包含在宽度内
        }}
        autoSize={{ minRows: 10 }} // 自动调整高度，最少10行，无最大行数限制
        placeholder={`编辑${type === 'original' ? '原文' : '译文'}内容，支持 HTML 标签...`}
      />

      {/* 正常位置的按钮（当固定时隐藏） */}
      <div
        ref={buttonsRef}
        style={{
          marginTop: '10px',
          display: 'flex',
          justifyContent: 'flex-end',
          visibility: isButtonsFixed ? 'hidden' : 'visible'
        }}
      >
        {renderButtons()}
      </div>

      {/* 固定在 textarea 可视区域右下方的按钮 */}
      {isButtonsFixed && (
        <div
          style={{
            position: 'fixed',
            bottom: fixedButtonPosition.bottom,
            right: fixedButtonPosition.right,
            zIndex: 1001, // 确保在工具栏之上
            backgroundColor: '#fff',
            padding: '10px',
            borderRadius: '6px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            border: '1px solid #d9d9d9'
          }}
        >
          {renderButtons()}
        </div>
      )}
    </div>
  );
});
MergedEditDisplay.displayName = 'MergedEditDisplay';

// 渲染计数器，用于跟踪每个区块的渲染次数
const renderCounts = {};

const ChunkRenderer = memo((props) => {
  // 增加渲染计数
  const renderCountRef = useRef(0);
  renderCountRef.current += 1;
  const renderCount = renderCountRef.current;



  // 获取 think 相关状态
  const chunkThinkContents = useStore(state => state.chunkThinkContents);
  const chunkThinkStates = useStore(state => state.chunkThinkStates);

  // 获取当前区块的 think 状态
  const currentThinkContent = chunkThinkContents.get(props.chunkIndex) || '';
  const currentThinkState = chunkThinkStates.get(props.chunkIndex) || { isThinking: false, isCompleted: false };


  const {
    items,
    translatedItemsForChunk,
    chunkIndex,
    editingState,
    editText,
    debouncedSetEditText,
    handleEditStart,
    handleEditSave,
    handleEditCancel,
    isActivelyTranslating,
    disableTranslateButton,
    stoppingChunks,
    handleTranslateChunk,
    handleStopChunkTranslation,
    handleClearTranslatedChunk,
    handleDeleteChunk,
    showTranslationOnly,
    renderOriginalContentInternal, // This prop might need to be removed if logic is fully self-contained or passed differently
    renderTranslatedContentInternal, // This prop is now handled by TranslatedChunkDisplay and specific logic here
    translationContainerRefs,
    updateTranslationDOM,
    // 新增的合并编辑状态
    isMergedOriginalEditing = false,
    isMergedTranslatedEditing = false,
    // 动态高度信息
    toolbarHeight = 80,
    fullPageEditorRef, // 新增：接收从 ArticleEditPage 传递过来的 ref
    rowId, // 新增：接收用于设置 Row id 的 prop
    // checkTagCountMismatch, // This logic might be internal to renderTranslatedContentForChunkRenderer or passed to it
    // formatConversionNeeded, // REMOVED
    // handleAttemptAIFormatConversion, // REMOVED
    // clearFormatConversionNeeded, // REMOVED
  } = props; // Destructure all props here

  // Original component logic starts here, using destructured props
  const isEditingOriginal = editingState.type === 'original' && editingState.index === chunkIndex;
  const isEditingTranslated = editingState.type === 'translated' && editingState.index === chunkIndex;

  const renderOriginalContentDisplay = () => {
    if (isEditingOriginal) {
      return (
        <EnhanceTextArea
          defaultValue={editText}
          onChange={debouncedSetEditText}
          showLineNumbers={true}
          autoSize={{ minRows: 6, maxRows: 25 }}
          style={{ width: '100%', resize: 'vertical' }}
        />
      );
    } else {
      return (
        <div className="original-chunk-content" style={{ minHeight: '200px' }}>
          {items.map((item, idx) => {
            // 跳过 null 或 undefined 的 item，不在正常渲染时显示
            if (item === null || item === undefined) {
              return null;
            }

            const { tag, id, ...props } = item;
            const Tag = `${tag}`;
            if (tag === 'img') {
              return <Tag key={id} {...props} loading="lazy" />;
            }
            if (tag === 'table' && props.children) {
              const tableHtml = props.children;
              delete props.children;
              return <Tag key={id} {...props} dangerouslySetInnerHTML={{ __html: tableHtml }} />;
            }
            if (props.children) {
              const htmlContent = props.children;
              delete props.children;
              return <Tag key={id} {...props} dangerouslySetInnerHTML={{ __html: htmlContent }} />;
            }
            return <Tag key={id} {...props} />;
          })}
        </div>
      );
    }
  };

  const renderEditButtonsDisplay = (type) => {
    const isEditingThis = editingState.type === type && editingState.index === chunkIndex;

    if (isEditingThis) {
      return (
        <Space style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
          <Button icon={<SaveOutlined />} size="small" onClick={handleEditSave}>保存</Button>
          <Button icon={<CloseOutlined />} size="small" onClick={handleEditCancel}>取消</Button>
        </Space>
      );
    }

    // For non-editing mode, always show the edit button regardless of content
    return (
      <Button
        type="text"
        icon={<EditOutlined />}
        size="small"
        onClick={() => handleEditStart(type, chunkIndex)}
        title={type === 'original' ? '编辑原文' : '编辑译文'}
      />
    );
  };



  if (showTranslationOnly) {
    return (
      <Row key={`chunk-row-${chunkIndex}-translation-only`} id={rowId} className="chunk-row" gutter={16}>
        <Col span={24} style={{ display: 'flex' }}>
          <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
            {/* Think 过程显示 */}
            {(currentThinkContent || currentThinkState.isThinking) && (
              <ThinkDisplay
                thinkContent={currentThinkContent}
                isThinking={currentThinkState.isThinking}
                isCompleted={currentThinkState.isCompleted}
                style={{ marginBottom: '12px' }}
              />
            )}

            <div
              className={`translated-content-container ${isEditingTranslated ? 'editing-mode' : ''}`}
              style={{ flexGrow: 1, minHeight: isEditingTranslated ? '300px' : '200px', display: 'flex', flexDirection: 'column' }}
            >
              {/* renderTranslatedContentInternal is called here */}
              {renderTranslatedContentInternal(items, translatedItemsForChunk, chunkIndex)}
            </div>
            <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
              {isEditingTranslated
                ? renderEditButtonsDisplay('translated')
                : (
                  <Space align="baseline" size={0}>
                    <Popconfirm
                      title="确认清空此区块译文?"
                      description="此操作将移除当前译文内容，但原文保留。"
                      onConfirm={() => handleClearTranslatedChunk(chunkIndex)}
                      okText="确认清空"
                      cancelText="取消"
                      okButtonProps={{ danger: true }}
                      disabled={!translatedItemsForChunk || translatedItemsForChunk.length === 0}
                    >
                      <Button
                        type="text"
                        danger
                        icon={<ClearOutlined />}
                        size="small"
                        title="清空译文"
                        disabled={!translatedItemsForChunk || translatedItemsForChunk.length === 0}
                      />
                    </Popconfirm>
                    {renderEditButtonsDisplay('translated')}
                  </Space>
                )
              }
            </div>
          </div>
        </Col>
        <Col span={24}><Divider style={{ marginTop: '10px', marginBottom: '10px'}} /></Col>
      </Row>
    );
  } else {
    // 合并编辑模式下，只渲染第一个chunk，其他chunk不渲染
    if ((isMergedOriginalEditing || isMergedTranslatedEditing) && chunkIndex !== 0) {
      return null;
    }

    // 根据合并编辑状态决定列的显示
    const showOriginal = !isMergedOriginalEditing;
    const showTranslated = !isMergedTranslatedEditing;
    // 保持原有的12/12布局，即使在合并编辑模式下也不改变宽度
    const originalSpan = 12;
    const translatedSpan = 12;

    return (
      <Row key={`chunk-row-${chunkIndex}-dual`} id={rowId} className="chunk-row" gutter={16}>
        {/* 原文列 */}
        <Col span={originalSpan} style={{ display: 'flex' }}>
          {isMergedOriginalEditing && chunkIndex === 0 ? (
            /* 原文合并编辑模式 - 只在第一个chunk显示textarea */
            <MergedEditDisplay
              ref={fullPageEditorRef}
              type="original"
              mergedText={props.mergedOriginalText}
              setMergedText={props.setMergedOriginalText}
              onSave={props.handleSaveMergedOriginalEdit}
              onCancel={props.handleCancelMergedOriginalEdit}
              toolbarHeight={toolbarHeight}
            />
          ) : (
            /* 正常原文显示模式 */
            <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
              <div
                className={`original-content-container ${isEditingOriginal ? 'editing-mode' : ''}`}
                style={{ flexGrow: 1, minHeight: isEditingOriginal ? '300px' : '200px', display: 'flex', flexDirection: 'column' }}
              >
                {renderOriginalContentDisplay()}
              </div>
              <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
                {isEditingOriginal
                  ? renderEditButtonsDisplay('original')
                  : (
                    <Space align="baseline" size={0}>
                      <Popconfirm
                        title="确认删除此区块?"
                        description="将同时删除原文和译文，此操作无法撤销。"
                        onConfirm={() => handleDeleteChunk(chunkIndex)}
                        okText="确认删除"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                      >
                        <Button type="text" danger icon={<DeleteOutlined />} size="small" title="删除此块" />
                      </Popconfirm>
                      {renderEditButtonsDisplay('original')}
                      {isActivelyTranslating ? (
                        <Button
                          type="text"
                          danger
                          size="small"
                          icon={<PauseOutlined />}
                          onClick={() => handleStopChunkTranslation(chunkIndex)}
                          title="停止翻译"
                          loading={stoppingChunks.has(chunkIndex)}
                          disabled={stoppingChunks.has(chunkIndex)}
                        />
                      ) : (
                        <Button
                          type="text"
                          size="small"
                          icon={<ArrowRightOutlined />}
                          onClick={() => handleTranslateChunk(chunkIndex)}
                          title="翻译此块"
                          disabled={disableTranslateButton}
                        />
                      )}
                    </Space>
                  )
                }
              </div>
            </div>
          )}
        </Col>

        {/* 译文列 */}
        <Col span={translatedSpan} style={{ display: 'flex' }}>
          {isMergedTranslatedEditing && chunkIndex === 0 ? (
            /* 译文合并编辑模式 - 只在第一个chunk显示textarea */
            <MergedEditDisplay
              ref={fullPageEditorRef}
              type="translated"
              mergedText={props.mergedTranslatedText}
              setMergedText={props.setMergedTranslatedText}
              onSave={props.handleSaveMergedTranslatedEdit}
              onCancel={props.handleCancelMergedTranslatedEdit}
              toolbarHeight={toolbarHeight}
            />
          ) : (
            /* 正常译文显示模式 */
            <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
              {/* Think 过程显示 */}
              {(currentThinkContent || currentThinkState.isThinking) && (
                <ThinkDisplay
                  thinkContent={currentThinkContent}
                  isThinking={currentThinkState.isThinking}
                  isCompleted={currentThinkState.isCompleted}
                  style={{ marginBottom: '12px' }}
                />
              )}

              <div
                className={`translated-content-container ${isEditingTranslated ? 'editing-mode' : ''}`}
                style={{ flexGrow: 1, minHeight: isEditingTranslated ? '300px' : '200px', display: 'flex', flexDirection: 'column' }}
              >
                {/* renderTranslatedContentInternal is called here */}
                {renderTranslatedContentInternal(items, translatedItemsForChunk, chunkIndex)}
              </div>
              <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
                {isEditingTranslated
                  ? renderEditButtonsDisplay('translated')
                  : (
                    <Space align="baseline" size={0}>
                      <Popconfirm
                        title="确认清空此区块译文?"
                        description="此操作将移除当前译文内容，但原文保留。"
                        onConfirm={() => handleClearTranslatedChunk(chunkIndex)}
                        okText="确认清空"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                        disabled={!translatedItemsForChunk || translatedItemsForChunk.length === 0}
                      >
                        <Button
                          type="text"
                          danger
                          icon={<ClearOutlined />}
                          size="small"
                          title="清空译文"
                          disabled={!translatedItemsForChunk || translatedItemsForChunk.length === 0}
                        />
                      </Popconfirm>
                      {renderEditButtonsDisplay('translated')}
                    </Space>
                  )
                }
              </div>
            </div>
          )}
        </Col>
        <Col span={24}><Divider style={{ marginTop: '10px', marginBottom: '10px'}} /></Col>
      </Row>
    );
  }
}, (prevProps, nextProps) => {
  return (
    prevProps.chunkIndex === nextProps.chunkIndex &&
    isEqual(prevProps.items, nextProps.items) &&
    isEqual(prevProps.translatedItemsForChunk, nextProps.translatedItemsForChunk) &&
    prevProps.editingState.type === nextProps.editingState.type &&
    prevProps.editingState.index === nextProps.editingState.index &&
    (prevProps.editingState.index !== prevProps.chunkIndex || prevProps.editText === nextProps.editText) &&
    prevProps.isActivelyTranslating === nextProps.isActivelyTranslating &&
    prevProps.disableTranslateButton === nextProps.disableTranslateButton &&
    isEqual(prevProps.stoppingChunks, nextProps.stoppingChunks) &&
    prevProps.showTranslationOnly === nextProps.showTranslationOnly &&
    prevProps.isMergedOriginalEditing === nextProps.isMergedOriginalEditing &&
    prevProps.isMergedTranslatedEditing === nextProps.isMergedTranslatedEditing &&
    // 当处于合并编辑模式时，必须比较文本内容是否发生变化
    (!nextProps.isMergedOriginalEditing || prevProps.mergedOriginalText === nextProps.mergedOriginalText) &&
    (!nextProps.isMergedTranslatedEditing || prevProps.mergedTranslatedText === nextProps.mergedTranslatedText) &&
    // prevProps.onEditStart === nextProps.onEditStart && // handleEditStart is stable
    // prevProps.handleEditSave === nextProps.handleEditSave && // handleEditSave is stable
    // prevProps.handleEditCancel === nextProps.handleEditCancel && // handleEditCancel is stable
    // prevProps.handleTranslateChunk === nextProps.handleTranslateChunk && // stable
    // prevProps.handleStopChunkTranslation === nextProps.handleStopChunkTranslation && // stable
    // prevProps.handleClearTranslatedChunk === nextProps.handleClearTranslatedChunk && // stable
    // prevProps.handleDeleteChunk === nextProps.handleDeleteChunk && // stable
    prevProps.renderTranslatedContentInternal === nextProps.renderTranslatedContentInternal
    // isEqual(prevProps.formatConversionNeeded, nextProps.formatConversionNeeded) && // REMOVED
    // prevProps.handleAttemptAIFormatConversion === nextProps.handleAttemptAIFormatConversion && // REMOVED
    // prevProps.clearFormatConversionNeeded === nextProps.clearFormatConversionNeeded // REMOVED
  );
});

export default ChunkRenderer;