import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Table, Button, Typography, Tag, Select, Popconfirm } from 'antd';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { DragOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';

const { Paragraph } = Typography;

const DragHandle = ({ index, moveRow }) => {
  const ref = useRef(null);
  
  const [{ isDragging }, drag] = useDrag({
    type: 'table-row',
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: 'table-row',
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? 'drop-over-downward' : 'drop-over-upward',
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });

  drop(drag(ref));

  return (
    <Button
      ref={ref}
      type="text"
      icon={<DragOutlined />}
      style={{ cursor: 'move', opacity: isDragging ? 0.5 : 1 }}
      className={`drag-handle ${isOver ? dropClassName : ''}`}
    />
  );
};

const EditableCell = ({ value, dataIndex, record, onChange, editorType }) => {
  if (editorType === 'text') {
    return (
      <Paragraph
        editable={{
          onChange: (newValue) => {
            onChange(newValue, record.key, dataIndex);
          },
        }}
        style={{ marginBottom: 0 }}
      >
        {value}
      </Paragraph>
    );
  } else if (editorType === 'tags') {
    const tagValues = value || [];
    
    return (
      <Select
        mode="tags"
        style={{ width: '100%' }}
        value={tagValues}
        onChange={(newTags) => {
          onChange(newTags, record.key, dataIndex);
        }}
        tokenSeparators={[',']}
        open={false}
        suffixIcon={null}
      >
        {tagValues.map(tag => (
          <Select.Option key={tag} value={tag}>
            {tag}
          </Select.Option>
        ))}
      </Select>
    );
  }
  
  return value;
};

const EnhanceTable = (props) => {
  const {
    dataSource: initialDataSource,
    columns: initialColumns,
    rowKey = 'key',
    editableCells = {},  // { dataIndex: 'text' | 'tags' }
    onRowsChange,
    onDeleteRow, // 新增删除行的回调函数
    onMoveRow, // 新增移动行的回调函数
    onCellChange, // 新增单元格内容变化的回调函数
    ...restProps
  } = props;

  const [dataSource, setDataSource] = useState(initialDataSource || []);

  useEffect(() => {
    setDataSource(initialDataSource || []);
  }, [initialDataSource]);

  // 拖拽移动行
  const moveRow = useCallback(
    async (dragIndex, hoverIndex) => {
      try {
        console.log(`开始移动行: ${dragIndex} -> ${hoverIndex}`);
        const dragRow = dataSource[dragIndex];
        const newData = [...dataSource];
        newData.splice(dragIndex, 1);
        newData.splice(hoverIndex, 0, dragRow);
        
        // 先临时更新UI以提供即时反馈
        setDataSource(newData);
        
        // 如果提供了移动行的回调函数，调用它
        if (onMoveRow) {
          console.log('调用onMoveRow回调');
          const success = await onMoveRow(dragRow[rowKey], dragIndex, hoverIndex, newData);
          console.log('onMoveRow回调返回:', success);
          
          if (success === false) {
            // 如果后端更新失败，恢复到原始顺序
            console.log('后端更新失败，恢复原始状态');
            const originalData = [...dataSource];
            setDataSource(originalData);
            return;
          }
        }
        
        // 最终确认更新状态
        console.log('最终更新状态');
        if (onRowsChange) {
          onRowsChange(newData);
        }
      } catch (error) {
        console.error('移动行失败:', error);
        // 错误处理也不重置状态，避免闪烁
      }
    },
    [dataSource, onRowsChange, onMoveRow, rowKey],
  );

  // 处理单元格内容变化
  const handleCellChange = useCallback(
    (newValue, key, dataIndex) => {
      const newData = [...dataSource];
      const targetIndex = newData.findIndex((item) => item[rowKey] === key);
      
      if (targetIndex > -1) {
        const oldValue = newData[targetIndex][dataIndex];
        newData[targetIndex][dataIndex] = newValue;
        setDataSource(newData);
        
        if (onRowsChange) {
          onRowsChange(newData);
        }
        
        // 如果提供了单元格变化的回调函数，调用它
        if (onCellChange) {
          onCellChange(key, dataIndex, oldValue, newValue, newData[targetIndex]);
        }
      }
    },
    [dataSource, rowKey, onRowsChange, onCellChange],
  );

  // 添加行
  const handleAddRow = useCallback(() => {
    // 检查是否已经存在一个空的新行
    const hasEmptyRow = dataSource.some(row => {
      return Object.entries(row).every(([key, value]) => {
        if (key === rowKey) return true; // 跳过key字段的检查
        if (Array.isArray(value)) return value.length === 0;
        return !value;
      });
    });

    if (hasEmptyRow) {
      return; // 如果已经存在空行，则不添加新行
    }

    const newKey = `new-${Date.now()}`;
    const newRow = { [rowKey]: newKey };
    
    // 初始化新行的所有列
    initialColumns.forEach((column) => {
      if (column.dataIndex && column.dataIndex !== rowKey) {
        // 确保tags类型的字段始终初始化为空数组，即使editableCells中没有定义
        if (column.dataIndex === 'models' || editableCells[column.dataIndex] === 'tags') {
          newRow[column.dataIndex] = [];
        } else {
          newRow[column.dataIndex] = '';
        }
      }
    });
    
    const newData = [...dataSource, newRow];
    setDataSource(newData);
    
    if (onRowsChange) {
      onRowsChange(newData);
    }
  }, [dataSource, initialColumns, editableCells, rowKey, onRowsChange]);

  // 删除行
  const handleDeleteRow = useCallback(
    (key) => {
      // 找到要删除的行数据
      const rowToDelete = dataSource.find((item) => item[rowKey] === key);
      
      // 如果提供了删除行的回调函数，调用它
      if (onDeleteRow) {
        // 在这里我们首先调用删除回调，然后根据回调结果决定是否继续
        onDeleteRow(key).then(success => {
          // 只有当删除操作成功时，才更新本地数据
          if (success) {
            const newData = dataSource.filter((item) => item[rowKey] !== key);
            setDataSource(newData);
            
            if (onRowsChange) {
              onRowsChange(newData);
            }
          }
        }).catch(error => {
          console.error('删除行操作失败:', error);
          // 删除失败时不更新本地数据
        });
      } else {
        // 如果没有提供删除回调，则直接更新本地数据
        const newData = dataSource.filter((item) => item[rowKey] !== key);
        setDataSource(newData);
        
        if (onRowsChange) {
          onRowsChange(newData);
        }
      }
    },
    [dataSource, rowKey, onRowsChange, onDeleteRow],
  );

  // 处理列配置
  const columns = [
    {
      title: '',
      key: 'drag',
      width: 40,
      className: 'drag-visible',
      render: (_, __, index) => <DragHandle index={index} moveRow={moveRow} />,
    },
    ...initialColumns.map((column) => ({
      ...column,
      render: (text, record) => {
        const editorType = editableCells[column.dataIndex];
        
        if (editorType) {
          return (
            <EditableCell
              value={text}
              dataIndex={column.dataIndex}
              record={record}
              onChange={handleCellChange}
              editorType={editorType}
            />
          );
        }
        
        return column.render ? column.render(text, record) : text;
      },
    })),
    {
      title: '',
      key: 'action',
      width: 50,
      render: (_, record) => (
        <Popconfirm
          title="确认删除？"
          onConfirm={() => handleDeleteRow(record[rowKey])}
          okText="确认"
          cancelText="取消"
        >
          <Button type="text" icon={<DeleteOutlined />} danger />
        </Popconfirm>
      ),
    },
  ];

  return (
    <DndProvider backend={HTML5Backend}>
      <div style={{ position: 'relative' }}>

        <Table
          {...restProps}
          columns={columns}
          dataSource={dataSource}
          rowKey={rowKey}
          pagination={props.pagination}
        />
        <Button
          color="primary"
          variant="outlined"
          size="small"
          icon={<PlusOutlined />}
          onClick={handleAddRow}
          style={{ marginTop: 10}}
        />
      </div>
    </DndProvider>
  );
};

export default EnhanceTable;
