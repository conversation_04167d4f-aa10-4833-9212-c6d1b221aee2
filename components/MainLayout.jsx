import React from 'react';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';

const MainLayout = () => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sidebar />
      <Layout className="site-layout" style={{ marginLeft: 250, background: '#fff', overflow: 'visible' }}>
        {/* marginLeft should match Sidebar width */}
        <Outlet /> {/* Child routes will render here */}
      </Layout>
    </Layout>
  );
};

export default MainLayout;