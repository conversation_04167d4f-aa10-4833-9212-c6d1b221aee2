import React from 'react';
import { Button } from 'antd';
import { AUTH0_REDIRECT_URI, AUTH0_DOMAIN, AUTH0_CLIENT_ID } from '../utils/constant.js';

// 加载中组件
export const LoadingComponent = () => {
  return (
    <div style={{ position: 'absolute', top: '10px', left: '10px', fontSize: '16px' }}>
      加载中...
    </div>
  );
};

// 定义不同的社交登录提供商
const socialProviders = [
  { name: 'Google', connection: 'google-oauth2', icon: 'google' },
  { name: 'GitHub', connection: 'github', icon: 'github' },
  { name: 'Microsoft', connection: 'windowslive', icon: 'windows' }
];

// 使用特定提供商登录的函数
const loginWithProvider = (connection) => {
  window.location.href=`https://${AUTH0_DOMAIN}/authorize?response_type=code&client_id=${AUTH0_CLIENT_ID}&connection=${connection}&redirect_uri=${encodeURIComponent(AUTH0_REDIRECT_URI)}&scope=openid%20profile%20email`
};

// 登录界面组件
export const LoginPage = () => {
  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
      <div style={{ textAlign: 'center' }}>
        <h1>欢迎使用译必</h1>

        <div className="login-buttons-container" style={{ display: 'flex', flexDirection: 'column', gap: '10px', maxWidth: '300px', margin: '0 auto' }}>
          {socialProviders.map((provider) => (
            <Button
              key={provider.connection}
              type="primary"
              icon={<i className={`icon-${provider.icon}`} />}
              onClick={() => loginWithProvider(provider.connection)}
              style={{ marginBottom: '10px' }}
            >
              通过 {provider.name} 登录
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};
