import { useState, useEffect, useRef, memo } from 'react';
import { toPng } from 'html-to-image';
import { Alert } from 'antd';

const HtmlToImage = ({ htmlString, onLoadingChange, contentType = 'table' }) => {
  const [imageDataUrl, setImageDataUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [imageDimensions, setImageDimensions] = useState({ width: 'auto', height: 'auto' });
  const nodeRef = useRef(null);

  // 从datauri计算图片原始尺寸并返回1/2大小
  const calculateHalfSizeFromDataUri = (dataUri) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const halfWidth = Math.round(img.naturalWidth * 0.8);
        const halfHeight = Math.round(img.naturalHeight * 0.8);
        resolve({ width: halfWidth, height: halfHeight });
      };
      img.onerror = () => {
        // 如果无法加载图片，返回auto
        resolve({ width: 'auto', height: 'auto' });
      };
      img.src = dataUri;
    });
  };

  // 通知父组件加载状态变化
  useEffect(() => {
    if (onLoadingChange) {
      onLoadingChange(loading);
    }
  }, [loading]); // 移除onLoadingChange依赖，避免无限更新

  useEffect(() => {
    const convertHtmlToImage = async () => {
      if (!nodeRef.current || !htmlString) {
        setError('无法渲染节点或缺少 HTML 内容');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);
      setImageDataUrl(null); // Reset image data on new conversion
      setImageDimensions({ width: 'auto', height: 'auto' }); // Reset image dimensions

      // Temporarily render the HTML to the hidden section
      nodeRef.current.innerHTML = htmlString;

      // Ensure styles are applied and layout is calculated
      // Adding a small delay might help in some complex cases
      await new Promise(resolve => setTimeout(resolve, 200)); // Increased delay to 200ms

      try {
        // 根据内容类型选择不同的容器选择器
        const containerSelector = contentType === 'refs' ? '.refs-container' : '.table-container';
        const containerElement = nodeRef.current.querySelector(containerSelector);
        if (!containerElement) {
          const contentTypeName = contentType === 'refs' ? '参考文献容器' : '表格容器';
          throw new Error(`在提供的 HTML 中未找到${contentTypeName}元素。`);
        }

        // Get the actual rendered dimensions of the container (including padding)
        const containerWidth = containerElement.offsetWidth;
        const containerHeight = containerElement.offsetHeight;

        // Use container dimensions to ensure padding is included

        const dataUrl = await toPng(containerElement, {
          width: containerWidth,
          height: containerHeight,
          // Use higher pixelRatio for crisp text
          pixelRatio: 1.5, // 增加到3倍分辨率获得更清晰的文字
          style: {
            margin: '0',
            padding: '0',
            border: 'none' // 移除可能的边框
          },
          backgroundColor: '#ffffff',
          quality: 1.0,
          // 确保精确捕获尺寸
          canvasWidth: containerWidth,
          canvasHeight: containerHeight,
          // 优化字体加载，减少重复请求
          cacheBust: false, // 禁用缓存破坏，允许使用缓存的资源
          skipFonts: false, // 不跳过字体，但使用缓存
          preferredFontFormat: 'woff2', // 优先使用更高效的字体格式
          // 添加字体加载超时
          fontEmbedTimeout: 5000, // 5秒字体加载超时
          // 跳过自动字体嵌入，使用已加载的字体
          skipAutoScale: true,
        });
        setImageDataUrl(dataUrl);

        // 计算图片的1/2尺寸
        const halfDimensions = await calculateHalfSizeFromDataUri(dataUrl);
        setImageDimensions(halfDimensions);
      } catch (err) {
        console.error('HTML 到图片转换失败:', err);
        const contentTypeName = contentType === 'refs' ? '参考文献' : '表格';
        setError(`无法将${contentTypeName}转换为图片。`);
      } finally {
        // Clean up the temporary HTML
        if (nodeRef.current) {
          nodeRef.current.innerHTML = '';
        }
        setLoading(false);
      }
    };

    convertHtmlToImage();

    // Cleanup function
    return () => {
      if (nodeRef.current) {
        nodeRef.current.innerHTML = '';
      }
    };
  }, [htmlString]); // Re-run effect if htmlString changes

  // Style for the hidden section used for rendering
  const hiddenStyle = {
    position: 'absolute',
    top: '-9999px',
    left: '-9999px',
    // Remove fixed width to allow natural table rendering width
    // width: '800px', // REMOVED
    padding: '1px', // Prevent margin collapse issues
    backgroundColor: 'white', // Ensure background for rendering
    border: '0', // Optional: helps visualize if moved on-screen
    height:'1px',
    // Add overflow: hidden? Might not be necessary
  };

  return (
    <>
      {/* Hidden section for rendering HTML */}
      <section ref={nodeRef} style={hiddenStyle}></section>

      {/* Display loading, error, or the resulting image */}
      {loading && <section style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>加载中...</section>}
      {error && <Alert message="转换错误" description={error} type="error" showIcon />}
      {/* Support wider images with horizontal scroll */}
      {imageDataUrl && !loading && (
        <img
          src={imageDataUrl}
          style={{
            display: 'block',
            minWidth: contentType === 'refs' ? '100%' : '250%',
            width: contentType === 'refs' ? '100%' : (imageDimensions.width === 'auto' ? 'auto' : `${imageDimensions.width}px`),
            height: 'auto',//imageDimensions.height === 'auto' ? 'auto' : `${imageDimensions.height}px`,
            margin: '0',
            borderRadius: '0',
            imageRendering: 'crisp-edges'
          }}
        />
      )}
      {!loading && !error && !imageDataUrl && <section>无法生成图片。</section>}
    </>
  );
};

// Add custom comparison to prevent unnecessary re-renders
const areEqual = (prevProps, nextProps) => {
  // Only re-render when htmlString, onLoadingChange, or contentType actually changes
  return prevProps.htmlString === nextProps.htmlString &&
         prevProps.onLoadingChange === nextProps.onLoadingChange &&
         prevProps.contentType === nextProps.contentType;
};

export default memo(HtmlToImage, areEqual);
