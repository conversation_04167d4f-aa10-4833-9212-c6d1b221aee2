import { useState, useEffect } from 'react';
import { Modal, Input, Button, Typography, Checkbox, Switch, Progress, Spin } from 'antd';
import { EditOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';

const { Text } = Typography;

// 术语摘要组件 - 显示特定原文术语+译文术语组合的情况
const TermSummary = ({ term, onStandardTranslationChange, isEnabled, onToggleEnabled, onApplyToAll }) => {
  // 获取当前译文术语（现在每个item只有一种译文）
  // 优先显示用户定义的标准翻译，如果为空，则显示第一个变体的翻译
  const displayTranslationInHeader = term.initialDisplayTranslation || ''; // Use the stored initial display translation
  const totalOccurrences = term.variantsInChunks?.length || 0;

  return (
    <div style={{
      background: isEnabled ? '#ffffff' : '#fafafa',
      border: `1px solid ${isEnabled ? '#d1d5db' : '#e5e7eb'}`,
      borderRadius: '8px',
      padding: '12px 16px',
      opacity: isEnabled ? 1 : 0.6,
      transition: 'all 0.2s ease'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Text style={{ fontSize: '12px', color: '#6b7280', minWidth: '50px' }}>
            原翻译：
          </Text>
          <Text strong style={{ fontSize: '14px', color: '#374151' }}>
            {term.originalTerm}
          </Text>
          <Text style={{ fontSize: '12px', color: '#6b7280' }}>
            →
          </Text>
          <Text style={{ fontSize: '13px', color: '#059669', fontWeight: 500 }}>
            {displayTranslationInHeader}
          </Text>
          <Switch
            size="small"
            checked={isEnabled}
            onChange={onToggleEnabled}
          />
        </div>
        <div style={{ fontSize: '12px', color: '#6b7280' }}>
          共 {totalOccurrences} 处
        </div>
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Text style={{ fontSize: '12px', color: '#6b7280', minWidth: '50px' }}>
          修改为：
        </Text>
        <Input
          size="small"
          value={term.userDefinedStandardTranslation}
          onChange={(e) => onStandardTranslationChange(e.target.value)}
          placeholder="输入标准译法"
          disabled={!isEnabled}
          style={{
            fontSize: '12px',
            flex: 1,
            maxWidth: '160px'
          }}
        />
        <Button
          size="small"
          type="primary"
          ghost
          onClick={() => onApplyToAll && onApplyToAll()}
          disabled={!isEnabled || !term.userDefinedStandardTranslation}
          style={{
            fontSize: '11px',
            height: '24px',
            padding: '0 8px'
          }}
        >
          全部应用
        </Button>
      </div>
    </div>
  );
};

// 翻译实例详情组件 - 显示具体的翻译实例和上下文
const TranslationInstance = ({ variant, originalTerm, standardTranslation, onEdit }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(variant.translationVariant);

  // 在调用 highlightTerm 之前记录原始数据
  // console.log('[HL_DBG_PRE_FORMAT] Term to highlight (originalTerm):', JSON.stringify(originalTerm), 'Length:', originalTerm ? originalTerm.length : 'N/A');
  // if (originalTerm) {
  //   for (let i = 0; i < originalTerm.length; i++) {
  //     console.log(`[HL_DBG_PRE_FORMAT] originalTerm char[${i}]: '${originalTerm[i]}' (code: ${originalTerm.charCodeAt(i)})`);
  //   }
  // }
  // console.log('[HL_DBG_PRE_FORMAT] Original Chunk Content (variant.originalChunkContent) snippet:', variant.originalChunkContent ? JSON.stringify(variant.originalChunkContent.substring(0, 100) + '...') : 'N/A', 'Length:', variant.originalChunkContent ? variant.originalChunkContent.length : 'N/A');
  // if (variant.originalChunkContent) {
  //   for (let i = 0; i < Math.min(variant.originalChunkContent.length, 20); i++) { // Log first 20 chars
  //     console.log(`[HL_DBG_PRE_FORMAT] variant.originalChunkContent char[${i}]: '${variant.originalChunkContent[i]}' (code: ${variant.originalChunkContent.charCodeAt(i)})`);
  //   }
  // }
  // console.log('[HL_DBG_PRE_FORMAT] Translation Variant (variant.translationVariant):', JSON.stringify(variant.translationVariant));
  // console.log('[HL_DBG_PRE_FORMAT] Translated Chunk Content (variant.translatedChunkContent) snippet:', variant.translatedChunkContent ? JSON.stringify(variant.translatedChunkContent.substring(0, 100) + '...') : 'N/A');


  const handleSave = () => {
    onEdit(editValue);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(variant.translationVariant);
    setIsEditing(false);
  };

  // 高亮显示术语在上下文中的位置
  const highlightTerm = (text, term, isOriginal = true) => {
    // console.log('[HL_DBG_ENTRY] Term (param):', JSON.stringify(term), 'Length:', term ? term.length : 'N/A');
    // if (term && typeof term === 'string') {
    //   for (let i = 0; i < term.length; i++) {
    //     console.log(`[HL_DBG_ENTRY] Term (param) char[${i}]: '${term[i]}' (code: ${term.charCodeAt(i)})`);
    //   }
    // }
    // console.log('[HL_DBG_ENTRY] Text (param) snippet:', text ? JSON.stringify(text.substring(0, 100) + '...') : 'N/A', 'Length:', text ? text.length : 'N/A');
    // if (text && typeof text === 'string') {
    //     for (let i = 0; i < Math.min(text.length, 20); i++) { // Log first 20 chars of text
    //         console.log(`[HL_DBG_ENTRY] Text (param) char[${i}]: '${text[i]}' (code: ${text.charCodeAt(i)})`);
    //     }
    // }

    // console.log('[HL_DBG] Highlight attempt. Term:', term, 'Original:', isOriginal, 'Text snippet:', text ? text.substring(0, 50) + '...' : 'N/A');
    if (!text || !term) {
      // console.log('[HL_DBG] Highlight skipped: Text or term is empty.');
      return text;
    }

    // 转义特殊正则字符
    const escapeRegExp = (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const escapedTerm = escapeRegExp(term);
    // console.log('[HL_DBG] Escaped term:', escapedTerm);

    try {
      // 对于中文术语，不使用单词边界，直接匹配
      const isChinese = /[\u4e00-\u9fff]/.test(term);
      let regex;
      let matchFoundInRegexTest = false;

      if (isChinese) {
        // 中文术语直接匹配
        regex = new RegExp(escapedTerm, 'gi');
        // console.log('[HL_DBG] Chinese term regex created:', regex.toString());
      } else {
        // 英文术语尝试单词边界匹配
        regex = new RegExp(`\\b${escapedTerm}\\b`, 'gi');
        // console.log('[HL_DBG] English term regex (word boundary) created:', regex.toString());
        // 在使用regex.test()之前，确保它不是null或者undefined
        if (regex && !regex.test(text)) {
          // console.log('[HL_DBG] Word boundary match failed for English term. Trying direct match.');
          regex = new RegExp(escapedTerm, 'gi');
          // console.log('[HL_DBG] English term regex (direct) created:', regex.toString());
        }
      }

      // 再次检查 regex 是否有效
      if (!regex) {
        // console.error('[HL_DBG] Regex is undefined. This should not happen.');
        return text;
      }
      
      regex.lastIndex = 0; // 重置 regex 状态进行测试
      matchFoundInRegexTest = regex.test(text);
      // console.log('[HL_DBG] Result of regex.test():', matchFoundInRegexTest);

      if (!matchFoundInRegexTest) {
        // console.log('[HL_DBG] No match found by regex.test(). Returning original text.');
        return text;
      }

      regex.lastIndex = 0; // 重置 regex 状态进行替换

      let replacementsMade = 0;
      const highlightedText = text.replace(regex, (match) => {
        replacementsMade++;
        // console.log('[HL_DBG] Match found and replaced:', match);
        return `<HIGHLIGHT>${match}</HIGHLIGHT>`;
      });

      if (replacementsMade === 0) {
        // console.log('[HL_DBG] text.replace() made no actual replacements, though regex.test() was true. Text snippet:', text.substring(0, 50) + '...');
      }
      
      // 分割并渲染
      const parts = highlightedText.split(/(<HIGHLIGHT>.*?<\/HIGHLIGHT>)/);

      return parts.map((part, index) => {
        if (part.startsWith('<HIGHLIGHT>') && part.endsWith('</HIGHLIGHT>')) {
          const content = part.slice(11, -12); // 移除<HIGHLIGHT>标签
          return (
            <span
              key={index}
              style={{
                background: isOriginal ? '#fef3c7' : '#dbeafe',
                padding: '1px 3px',
                borderRadius: '3px',
                fontWeight: 600,
                color: isOriginal ? '#92400e' : '#1e40af'
              }}
            >
              {content}
            </span>
          );
        }
        return part;
      });

    } catch (e) {
      console.warn('高亮术语时发生错误:', e);
      return text;
    }
  };

  // 处理上下文文本，优先围绕术语截取，限制在maxLength左右
  const formatContextText = (text, termStr) => {
    if (!text) return '';
    const maxLength = 200; // Desired max length of the context snippet

    // If no termStr is provided, or if it's an empty string, use original truncation logic
    if (!termStr || String(termStr).trim() === '') {
      // console.log(`[FMT_CTX] No termStr provided or empty. Using basic truncation for text snippet: ${String(text).substring(0,50)}...`);
      if (String(text).length <= maxLength) return text;
      const truncated = String(text).substring(0, maxLength);
      const lastPunctuation = Math.max(
        truncated.lastIndexOf('。'), truncated.lastIndexOf('，'),
        truncated.lastIndexOf('.'), truncated.lastIndexOf(','),
        truncated.lastIndexOf(' ')
      );
      // Prefer to break at punctuation if it's reasonably far and not the last char (to allow adding "...")
      if (lastPunctuation > maxLength * 0.75 && lastPunctuation < truncated.length -1 ) {
        return truncated.substring(0, lastPunctuation + 1) + "...";
      } else if (lastPunctuation > maxLength * 0.75) { // If it is the last char or close, just return up to punctuation
        return truncated.substring(0, lastPunctuation + 1);
      }
      return truncated + '...'; // Default truncation with "..."
    }

    const sText = String(text);
    const sTermStr = String(termStr);

    // Normalize text and term for robust searching
    const normalizedText = sText.normalize('NFKC').replace(/[\u2010-\u2015\u2212\uFE58\uFE63\uFF0D]/g, '-');
    const normalizedTermStr = sTermStr.normalize('NFKC').replace(/[\u2010-\u2015\u2212\uFE58\uFE63\uFF0D]/g, '-');
    
    let termIndexInOriginal = -1;
    try {
      // Try finding the non-normalized term in non-normalized text first, case insensitively
      termIndexInOriginal = sText.toLowerCase().indexOf(sTermStr.toLowerCase());
      if (termIndexInOriginal === -1) {
        // If not found, try finding the normalized term in normalized text
        const termIndexInNormalized = normalizedText.toLowerCase().indexOf(normalizedTermStr.toLowerCase());
        if (termIndexInNormalized !== -1) {
            // This is an approximation, as character counts might differ.
            // We'll use this index on the original string.
            termIndexInOriginal = termIndexInNormalized;
            // console.log(`[FMT_CTX] Term "${sTermStr}" not in original, but normalized "${normalizedTermStr}" found at ~${termIndexInOriginal}.`);
        }
      }
    } catch (e) {
      console.error(`[FMT_CTX] Error during indexOf: ${e}. Text (snippet): ${sText.substring(0,50)}, Term: ${sTermStr}`);
      // Fallback to original truncation if indexOf fails (same as !termStr logic)
      if (sText.length <= maxLength) return sText;
      const truncated = sText.substring(0, maxLength);
      const lastPunctuation = Math.max(
        truncated.lastIndexOf('。'), truncated.lastIndexOf('，'),
        truncated.lastIndexOf('.'), truncated.lastIndexOf(','),
        truncated.lastIndexOf(' ')
      );
      if (lastPunctuation > maxLength * 0.75 && lastPunctuation < truncated.length -1) {
        return truncated.substring(0, lastPunctuation + 1) + "...";
      } else if (lastPunctuation > maxLength * 0.75) {
        return truncated.substring(0, lastPunctuation + 1);
      }
      return truncated + '...';
    }

    let snippet;

    if (termIndexInOriginal !== -1) {
      const termActualLength = sTermStr.length;
      // Ensure contextRadius is not negative if term is very long
      const contextRadius = Math.max(0, Math.floor((maxLength - termActualLength) / 2));
      
      let snippetStartIndex = Math.max(0, termIndexInOriginal - contextRadius);
      let snippetEndIndex = Math.min(sText.length, termIndexInOriginal + termActualLength + contextRadius);

      // Attempt to expand snippet to maxLength if it's shorter and text is available
      if (snippetEndIndex - snippetStartIndex < maxLength && sText.length > maxLength) {
          const needed = maxLength - (snippetEndIndex - snippetStartIndex);
          const extendBefore = Math.floor(needed / 2);
          const extendAfter = needed - extendBefore;

          const oldSnippetStartIndex = snippetStartIndex;
          const oldSnippetEndIndex = snippetEndIndex;

          snippetStartIndex = Math.max(0, snippetStartIndex - extendBefore);
          snippetEndIndex = Math.min(sText.length, snippetEndIndex + extendAfter);
          
          // If one side couldn't extend fully, try to give more to the other side
          const actualExtendedBefore = oldSnippetStartIndex - snippetStartIndex;
          const actualExtendedAfter = snippetEndIndex - oldSnippetEndIndex;

          if (actualExtendedBefore < extendBefore && snippetEndIndex < sText.length) { // only extend end if not already at end
              snippetEndIndex = Math.min(sText.length, snippetEndIndex + (extendBefore - actualExtendedBefore));
          }
          if (actualExtendedAfter < extendAfter && snippetStartIndex > 0) { // only extend start if not already at start
              snippetStartIndex = Math.max(0, snippetStartIndex - (extendAfter - actualExtendedAfter));
          }
      }
      
      snippet = sText.substring(snippetStartIndex, snippetEndIndex);

      let prefix = "";
      let suffix = "";

      if (snippetStartIndex > 0) {
        prefix = "...";
      }
      if (snippetEndIndex < sText.length) {
        suffix = "...";
      }
      
      if (snippet.trim() === '') {
          snippet = (prefix && suffix) ? "..." : prefix + snippet + suffix;
      } else{
          snippet = prefix + snippet + suffix;
      }

    } else {
      // console.warn(`[FMT_CTX] Term "${sTermStr}" not found. Falling back to initial truncation.`);
      // Fallback logic (same as !termStr case)
      if (sText.length <= maxLength) return sText;
      const truncated = sText.substring(0, maxLength);
      const lastPunctuation = Math.max(
        truncated.lastIndexOf('。'), truncated.lastIndexOf('，'),
        truncated.lastIndexOf('.'), truncated.lastIndexOf(','),
        truncated.lastIndexOf(' ')
      );
      if (lastPunctuation > maxLength * 0.75 && lastPunctuation < truncated.length -1) {
        return truncated.substring(0, lastPunctuation + 1) + "...";
      } else if (lastPunctuation > maxLength * 0.75) {
        return truncated.substring(0, lastPunctuation + 1);
      }
      return truncated + '...';
    }
    // console.log(`[FMT_CTX] Term: "${sTermStr}", Snippet: "${snippet}"`);
    return snippet;
  };

  return (
    <div style={{
      background: '#ffffff', // Removed isSelected logic
      border: `1px solid '#e5e7eb'`, // Removed isSelected logic
      borderRadius: '6px',
      padding: '10px 12px',
      fontSize: '12px',
      opacity: 1, // Removed isSelected logic
      transition: 'all 0.2s ease'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '6px' }}>
        <Text style={{ fontSize: '12px', color: '#6b7280' }}>
          Chunk {variant.chunkIndex + 1}
        </Text>
        <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: '6px' }}>
          <Text style={{ fontSize: '12px', fontWeight: 500 }}>
            {originalTerm} →
          </Text>
          {isEditing ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <Input
                size="small"
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                style={{ width: '120px', fontSize: '11px' }}
                onPressEnter={handleSave}
              />
              <Button
                type="text"
                size="small"
                icon={<CheckOutlined />}
                onClick={handleSave}
                style={{ padding: '2px 4px', fontSize: '10px' }}
              />
              <Button
                type="text"
                size="small"
                icon={<CloseOutlined />}
                onClick={handleCancel}
                style={{ padding: '2px 4px', fontSize: '10px' }}
              />
            </div>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <Text
                style={{
                  fontSize: '12px',
                  fontWeight: 500,
                  color: variant.translationVariant === standardTranslation ? '#059669' : '#dc2626'
                }}
              >
                {variant.translationVariant}
              </Text>
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => setIsEditing(true)}
                style={{ padding: '2px 4px', fontSize: '10px' }}
              />
            </div>
          )}
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', fontSize: '11px' }}>
        <div>
          <Text type="secondary" style={{ fontSize: '10px' }}>原文上下文：</Text>
          <div style={{
            background: '#f9fafb',
            padding: '6px 8px',
            borderRadius: '4px',
            marginTop: '2px',
            lineHeight: '1.5',
            fontSize: '11px',
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            minHeight: '20px'
          }}>
            {highlightTerm(formatContextText(variant.originalChunkContent, originalTerm), originalTerm, true)}
          </div>
        </div>
        <div>
          <Text type="secondary" style={{ fontSize: '10px' }}>译文上下文：</Text>
          <div style={{
            background: '#f9fafb',
            padding: '6px 8px',
            borderRadius: '4px',
            marginTop: '2px',
            lineHeight: '1.5',
            fontSize: '11px',
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            minHeight: '20px'
          }}>
            {highlightTerm(formatContextText(variant.translatedChunkContent, variant.translationVariant), variant.translationVariant, false)}
          </div>
        </div>
      </div>
    </div>
  );
};

const TerminologySyncModal = ({ open, terms, onConfirm, onCancel, progress }) => {
  const [editableTerms, setEditableTerms] = useState([]);
  const [expandedTerms, setExpandedTerms] = useState(new Set());

  useEffect(() => {
    if (open && terms) {
      setEditableTerms(
        terms.map(term => ({
          ...term,
          userDefinedStandardTranslation: term.userDefinedStandardTranslation || (term.variantsInChunks && term.variantsInChunks.length > 0 ? term.variantsInChunks[0].translationVariant : ''),
          initialDisplayTranslation: term.variantsInChunks?.[0]?.translationVariant || '', // Store for static header display
          isEnabled: true,
          variantsInChunks: term.variantsInChunks ? term.variantsInChunks.map(variant => ({
            ...variant,
            initialTranslationVariant: variant.translationVariant // Store initial value
          })) : []
        }))
      );
      // 默认展开所有术语
      setExpandedTerms(new Set(terms.map((_, index) => index)));
    }
  }, [open, terms]);

  // 按原文术语分组
  const groupTermsByOriginal = () => {
    const groups = new Map();

    editableTerms.forEach((term, termIndex) => {
      const originalTerm = term.originalTerm;
      if (!groups.has(originalTerm)) {
        groups.set(originalTerm, []);
      }
      groups.get(originalTerm).push({ ...term, termIndex });
    });

    return Array.from(groups.entries()).map(([originalTerm, terms]) => ({
      originalTerm,
      terms
    }));
  };

  const handleTermChange = (value, index) => {
    const newTerms = [...editableTerms];
    newTerms[index].userDefinedStandardTranslation = value;
    setEditableTerms(newTerms);
  };

  const handleTermEnabledChange = (checked, termIndex) => {
    const newTerms = [...editableTerms];
    newTerms[termIndex].isEnabled = checked;
    setEditableTerms(newTerms);
  };

  const handleVariantEdit = (newValue, termIndex, variantIndex) => {
    const newTerms = [...editableTerms];
    const term = newTerms[termIndex];
    const variant = term.variantsInChunks[variantIndex];
    const oldTranslation = variant.translationVariant;


    // 更新用于显示的翻译
    variant.translationVariant = newValue;

    // TODO: 更新译文上下文内容 - 这部分是缺失的关键逻辑，暂时只加日志
    // 尝试在这里模拟更新，或者至少记录如果更新会发生什么
    if (variant.translatedChunkContent && oldTranslation && oldTranslation !== newValue) {
      const escapeRegExp = (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      variant.translatedChunkContent = variant.translatedChunkContent.replace(new RegExp(escapeRegExp(oldTranslation), 'g'), newValue);
      // const escapeRegExp = (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      // try {
      //   const regex = new RegExp(escapeRegExp(oldTranslation), 'gi');
      //   variant.translatedChunkContent = variant.translatedChunkContent.replace(regex, newValue);
      //   console.log('[DEBUG]   Mock translatedChunkContent updated snippet:', variant.translatedChunkContent ? variant.translatedChunkContent.substring(0, 50) + '...' : 'N/A');
      // } catch (e) {
      //   console.warn(`[DEBUG] Error in mock replacing term for handleVariantEdit:`, e);
      // }
    } else {
    }
    
    setEditableTerms(newTerms);
  };

  const toggleTermExpansion = (termIndex) => {
    const newExpanded = new Set(expandedTerms);
    if (newExpanded.has(termIndex)) {
      newExpanded.delete(termIndex);
    } else {
      newExpanded.add(termIndex);
    }
    setExpandedTerms(newExpanded);
  };

  // 全部应用功能 - 将统一翻译应用到该词的所有地方
  const handleApplyToAll = (termIndex) => {
    const newTerms = [...editableTerms];
    const term = newTerms[termIndex];
    const standardTranslation = term.userDefinedStandardTranslation;

    if (standardTranslation && term.variantsInChunks) {
      const escapeRegExp = (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      
      term.variantsInChunks.forEach(variant => {
        const oldVariantTranslation = variant.translationVariant; // 保存旧的翻译
        variant.translationVariant = standardTranslation; // 更新用于显示的翻译

        // 更新译文上下文内容
        if (variant.translatedChunkContent && oldVariantTranslation && oldVariantTranslation !== standardTranslation) {
          try {
            const regex = new RegExp(escapeRegExp(oldVariantTranslation), 'gi');
            variant.translatedChunkContent = variant.translatedChunkContent.replace(regex, standardTranslation);
          } catch (e) {
            console.warn(`Error replacing term in translatedChunkContent for termIndex ${termIndex}:`, e);
          }
        }
      });
      setEditableTerms(newTerms);
    }
  };





  // 获取全局统计信息
  const getGlobalStats = () => {
    const enabledTerms = editableTerms.filter(term => term.isEnabled);
    const totalInstances = editableTerms.reduce((sum, term) => {
      return sum + (term.variantsInChunks?.length || 0);
    }, 0);

    // 计算实际被修改过且启用的翻译实例数量
    const modifiedAndEnabledVariants = editableTerms.reduce((sum, term) => {
      if (term.isEnabled && term.variantsInChunks) {
        return sum + term.variantsInChunks.filter(variant => variant.translationVariant !== variant.initialTranslationVariant).length;
      }
      return sum;
    }, 0);

    return {
      enabledTerms: enabledTerms.length,
      totalTerms: editableTerms.length,
      totalVariants: totalInstances,
      modifiedAndEnabledVariants: modifiedAndEnabledVariants
    };
  };

  const handleConfirm = () => {
    // 只传递启用的术语，并且只包含实际被修改过的翻译实例
    const filteredTerms = editableTerms
      .filter(term => term.isEnabled)
      .map(term => ({
        ...term,
        variantsInChunks: term.variantsInChunks ? term.variantsInChunks.filter(variant => variant.translationVariant !== variant.initialTranslationVariant) : []
      }))
      .filter(term => term.variantsInChunks && term.variantsInChunks.length > 0); // 移除没有修改过的翻译实例的术语

    onConfirm(filteredTerms);
  };



  const globalStats = getGlobalStats();

  return (
    <Modal
      title={
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '0 4px'
        }}>
          <span style={{ fontSize: '16px', fontWeight: 600 }}>统一翻译</span>
          <div style={{
            background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
            border: '1px solid #bae6fd',
            borderRadius: '12px',
            padding: '4px 8px',
            fontSize: '11px',
            color: '#0369a1'
          }}>
            要处理 <strong>{globalStats.enabledTerms}/{globalStats.totalTerms}</strong> 个词，
            要修改 <strong>{globalStats.modifiedAndEnabledVariants}/{globalStats.totalVariants}</strong> 处
          </div>
        </div>
      }
      open={open}
      onOk={handleConfirm}
      onCancel={onCancel}
      width={1100}
      okText={progress?.isProcessing ? '处理中...' : `确认修改 (${globalStats.modifiedAndEnabledVariants}/${globalStats.totalVariants} 处)`}
      cancelText="取消"
      maskClosable={false}
      confirmLoading={progress?.isProcessing}
      okButtonProps={{
        disabled: progress?.isProcessing || editableTerms.length === 0
      }}
      styles={{
        body: {
          maxHeight: '65vh',
          overflowY: 'auto',
          padding: '16px 24px'
        }
      }}
    >
      {/* 进度显示区域 */}
      {progress && progress.isProcessing && (
        <div style={{
          marginBottom: '20px',
          padding: '16px',
          background: '#f8fafc',
          border: '1px solid #e2e8f0',
          borderRadius: '8px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
            <Spin size="small" />
            <Text strong style={{ fontSize: '14px', color: '#374151' }}>
              {progress.currentStep}
            </Text>
          </div>

          {progress.totalChunks > 0 && (
            <div style={{ marginBottom: '8px' }}>
              <Progress
                percent={Math.round((progress.processedChunks / progress.totalChunks) * 100)}
                size="small"
                status={progress.errors.length > 0 ? "exception" : "active"}
                format={(percent) => `${progress.processedChunks}/${progress.totalChunks} (${percent}%)`}
              />
            </div>
          )}

          {progress.errors.length > 0 && (
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                错误信息：
              </Text>
              <div style={{
                maxHeight: '60px',
                overflowY: 'auto',
                marginTop: '4px',
                fontSize: '11px',
                color: '#dc2626'
              }}>
                {progress.errors.map((error, index) => (
                  <div key={index}>{error}</div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 术语列表区域 */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        {editableTerms.length === 0 && !progress?.isProcessing && (
          <div style={{
            textAlign: 'center',
            padding: '40px 20px',
            color: '#6b7280'
          }}>
            <Text type="secondary">
              {progress?.currentStep || '暂无需要统一的词汇'}
            </Text>
          </div>
        )}

        {groupTermsByOriginal().map((group, groupIndex) => (
          <div key={group.originalTerm + groupIndex} style={{ marginBottom: '16px' }}>
            {/* 原文术语总标题 */}
            <div style={{
              fontSize: '14px',
              fontWeight: 600,
              color: '#1f2937',
              marginBottom: '8px',
              padding: '8px 12px',
              background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
              border: '1px solid #e2e8f0',
              borderRadius: '6px'
            }}>
              {group.originalTerm}
              <span style={{
                fontSize: '12px',
                fontWeight: 400,
                color: '#6b7280',
                marginLeft: '8px'
              }}>
                共 {group.terms.length} 种翻译
              </span>
            </div>

            {/* 该原文术语下的所有翻译变体 */}
            {group.terms.map((term) => (
              <div key={term.originalTerm + term.termIndex} style={{
                marginLeft: '32px', // 增加缩进
                marginBottom: '12px'
              }}>
                {/* 术语摘要 */}
                <TermSummary
                  term={term}
                  onStandardTranslationChange={(value) => handleTermChange(value, term.termIndex)}
                  isEnabled={term.isEnabled}
                  onToggleEnabled={(checked) => handleTermEnabledChange(checked, term.termIndex)}
                  onApplyToAll={() => handleApplyToAll(term.termIndex)}
                />

                {/* 展开/收起按钮 */}
                <div style={{ marginTop: '4px', marginBottom: '8px' }}>
                  <Button
                    type="text"
                    size="small"
                    onClick={() => toggleTermExpansion(term.termIndex)}
                    style={{
                      fontSize: '12px',
                      color: '#6b7280',
                      padding: '2px 8px',
                      height: 'auto'
                    }}
                  >
                    {expandedTerms.has(term.termIndex) ? '收起详情' : '展开详情'}
                  </Button>
                </div>

                {/* 翻译实例详情 */}
                {expandedTerms.has(term.termIndex) && term.variantsInChunks && (
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '6px',
                    marginBottom: '8px',
                    paddingLeft: '12px',
                    borderLeft: '2px solid #e5e7eb'
                  }}>
                    {term.variantsInChunks.map((variant, variantIndex) => (
                      <TranslationInstance
                        key={variantIndex}
                        variant={variant}
                        originalTerm={term.originalTerm}
                        standardTranslation={term.userDefinedStandardTranslation}
                        onEdit={(newValue) => handleVariantEdit(newValue, term.termIndex, variantIndex)}
                      />
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        ))}
      </div>
    </Modal>
  );
};

export default TerminologySyncModal;