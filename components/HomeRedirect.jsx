import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useStore } from '../stores';
import { shallow } from 'zustand/shallow';

const HomeRedirect = () => {
  const user = useStore(state => state.user, shallow);
  const userArticles = useStore(state => state.userArticles, shallow);
  const isLoadingUserArticles = useStore(state => state.isLoadingUserArticles);
  const fetchUserArticles = useStore(state => state.fetchUserArticles);

  useEffect(() => {
    console.log('[HomeRedirect] useEffect triggered', {
      user: user?.uuid,
      userArticles: userArticles?.length,
      isLoadingUserArticles
    });

    // 注意：用户文章列表现在通过 App.jsx 中的 fetchBootstrapData 获取
    // 这里不再需要单独请求，避免重复请求
  }, [user, userArticles, isLoadingUserArticles]);

  console.log('[HomeRedirect] Render state:', {
    user: user?.uuid,
    userArticles: userArticles?.length,
    isLoadingUserArticles
  });

  // 如果用户还没有登录，等待
  if (!user) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '16px'
      }}>
        正在验证用户身份...
      </div>
    );
  }

  // 如果还在加载用户文章，显示加载状态
  if (isLoadingUserArticles) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '16px'
      }}>
        正在加载文章列表...
      </div>
    );
  }

  // 如果有文章，跳转到最新的文章（第一个，因为已按updatedAt降序排列）
  if (userArticles && userArticles.length > 0) {
    const latestArticle = userArticles[0];
    console.log('[HomeRedirect] Redirecting to latest article:', latestArticle.uuid);
    return <Navigate to={`/article/${latestArticle.uuid}/edit`} replace />;
  }

  // 如果没有文章，跳转到空白文章页面
  console.log('[HomeRedirect] No articles found, redirecting to /article');
  return <Navigate to="/article" replace />;
};

export default HomeRedirect;
