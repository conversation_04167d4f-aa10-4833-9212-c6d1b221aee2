import React, { useState } from 'react';
import EnhanceTextArea from './EnhanceTextArea';

const SearchTestPage = () => {
  const [value, setValue] = useState(`这是一个测试文档，用于验证搜索功能。

在这个文档中，我们有很多不同的内容：
- 第一段包含一些基本信息
- 第二段包含更多详细信息
- 第三段包含总结信息

搜索功能应该能够：
1. 通过 Ctrl+F (或 Command+F) 打开搜索面板
2. 在右上角显示悬浮的搜索框
3. 支持大小写敏感搜索
4. 支持正则表达式搜索
5. 显示匹配数量和当前位置
6. 支持上一个/下一个导航

测试关键词：
- 搜索
- 功能
- 测试
- 信息
- 内容

这个文档足够长，可以测试滚动时搜索面板的位置是否正确。
当用户滚动页面时，搜索面板应该始终相对于 textarea 保持在右上角位置。

让我们添加更多内容来测试：
Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.

中文测试内容：
这里有一些中文内容用于测试中文搜索功能。
搜索功能应该能够正确处理中文字符。
包括标点符号、数字123和英文mixed content。

最后一段：
这是文档的最后一段，用于测试搜索到文档末尾的情况。
搜索功能应该能够循环搜索，从末尾回到开头。`);

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '800px', 
      margin: '0 auto',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <h1>EnhancedTextArea 搜索功能测试</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>使用说明：</h2>
        <ul>
          <li>按 <kbd>Ctrl+F</kbd> (Windows/Linux) 或 <kbd>Command+F</kbd> (Mac) 打开搜索面板</li>
          <li>搜索面板会出现在文本框的右上角</li>
          <li>输入搜索词后会自动高亮匹配项</li>
          <li>使用 ↑ 和 ↓ 按钮或 Enter/Shift+Enter 导航</li>
          <li>按 <kbd>Esc</kbd> 关闭搜索面板</li>
          <li>支持区分大小写和正则表达式搜索</li>
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>测试文本区域：</h2>
        <EnhanceTextArea
          value={value}
          onChange={(e) => setValue(e.target.value)}
          autoSize={{ minRows: 15 }}
          showLineNumbers={true}
          style={{
            fontSize: '13px',
            lineHeight: '1.6'
          }}
          placeholder="在这里输入文本进行测试..."
        />
      </div>

      <div style={{ marginTop: '20px' }}>
        <h2>测试建议：</h2>
        <ol>
          <li>尝试搜索 "搜索" - 应该找到多个匹配项</li>
          <li>尝试搜索 "功能" - 测试导航功能</li>
          <li>尝试搜索 "Lorem" - 测试英文搜索</li>
          <li>启用区分大小写，搜索 "lorem" - 应该无匹配</li>
          <li>启用正则表达式，搜索 "\\d+" - 应该匹配数字</li>
          <li>滚动页面测试搜索面板位置是否正确</li>
        </ol>
      </div>
    </div>
  );
};

export default SearchTestPage;
