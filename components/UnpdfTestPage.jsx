import React, { useState, useEffect } from 'react';
import { Button, Upload, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
// import { getDocumentProxy, extractText, extractImages, /* renderPageAsImage */ } from 'unpdf'; // Removed: Using pdfParser.js
// import * as pdfjsLib from 'pdfjs-dist'; // Removed: pdfParser.js handles worker initialization
import { getPdfText, extractPdfImages } from '../utils/pdfParser.js'; // Import new parser functions

// Helper function imageToDataURI is no longer needed as extractPdfImages from pdfParser returns data URIs.

const UnpdfTestPage = () => {
  const [loading, setLoading] = useState(false);
  // const [pdfJsInitialized, setPdfJsInitialized] = useState(false); // Removed: pdfParser.js handles initialization
  // const [renderedPageImages, setRenderedPageImages] = useState([]); // State for rendered images

  // useEffect for pdf.js initialization is removed as pdfParser.js handles it.

  const handleFileUpload = async (file) => {
    // if (!pdfJsInitialized) { // Removed check
    //   message.error('PDF 解析器尚未初始化成功，请稍后再试或检查控制台错误。');
    //   return false;
    // }
    setLoading(true);
    // setRenderedPageImages([]); // Clear previous rendered images
    console.log(`Processing file: ${file.name}`);
    try {
      const arrayBuffer = await file.arrayBuffer();
      // pdfParser functions accept ArrayBuffer directly.

      console.log('Extracting text using pdfParser.getPdfText...');
      const extractedText = await getPdfText(arrayBuffer.slice(0)); // Use new function, with a copy of arrayBuffer
      console.log('Extracted Text:', extractedText);
      message.success(`${file.name} 文本提取成功。`);

      console.log('Extracting images using pdfParser.extractPdfImages...');
      const extractedImageObjects = await extractPdfImages(arrayBuffer.slice(0)); // Use new function, with a copy of arrayBuffer
      
      // The new extractPdfImages returns an array of objects like:
      // { src: string, alt: string, width?: number, height?: number }
      // We can directly use this or adapt if a different structure is needed for display.
      // For now, let's log them and display a count.
      console.log('Extracted Images (from pdfParser):', extractedImageObjects);
      const allImages = extractedImageObjects.map((img, index) => ({
        page: 'N/A', // Page number isn't directly available from the new extractPdfImages in this context
        name: img.alt || `image_${index + 1}`,
        dataUri: img.src,
        width: img.width,
        height: img.height
      }));

      if (allImages.length > 0) {
        message.success(`${allImages.length} 张图片提取并转换为 DataURI 成功。`);
      } else {
        message.info('`extractImages` 未找到独立图片。'); // Removed: 尝试将页面渲染为图片...
        // const renderedPages = [];
        // for (let i = 1; i <= totalPages; i++) {
        //   try {
        //     console.log(`Rendering page ${i} as image...`);
        //     // Use a fresh copy of the original arrayBuffer for renderPageAsImage
        //     const uint8ArrayForRender = new Uint8Array(arrayBuffer.slice(0));
        //     // Removed scale option to use default (1.0) for now
        //     const pageImageBuffer = await renderPageAsImage(uint8ArrayForRender, i);
        //     const blob = new Blob([pageImageBuffer], { type: 'image/png' });
        //     const dataUri = await new Promise((resolve, reject) => {
        //       const reader = new FileReader();
        //       reader.onloadend = () => resolve(reader.result);
        //       reader.onerror = reject;
        //       reader.readAsDataURL(blob);
        //     });
        //     renderedPages.push({ page: i, dataUri });
        //     console.log(`Page ${i} rendered as image (Data URI length):`, dataUri.length);
        //     // Update: No longer logging snippet to console, will display on page
        //   } catch (renderError) {
        //     console.error(`Error rendering page ${i} as image:`, renderError);
        //     message.error(`页面 ${i} 渲染为图片失败: ${renderError.message}`);
        //   }
        // }
        // setRenderedPageImages(renderedPages); // Store rendered images in state
        // if (renderedPages.length > 0) {
        //   console.log('Rendered Pages (as Data URIs) stored in state.');
        //   message.success(`成功将 ${renderedPages.length} 个页面渲染为图片。请在页面下方查看。`);
        // } else {
        //   message.warning('页面渲染为图片也失败了。');
        // }
      }

    } catch (error) {
      console.error('Error processing PDF:', error);
      message.error(`${file.name} 解析失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
    return false; // Prevent antd upload default behavior
  };

  const props = {
    name: 'file',
    accept: '.pdf',
    beforeUpload: handleFileUpload,
    showUploadList: false,
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>unPDF Test Page</h1>
      <p>上传一个 PDF 文件来测试 unPDF 的文本和图片提取功能。提取的内容将显示在浏览器的控制台中。</p>
      <Upload {...props}>
        <Button icon={<UploadOutlined />} loading={loading} disabled={loading}>
          {loading ? '正在解析...' : '选择 PDF 文件'}
        </Button>
      </Upload>

      {/* {renderedPageImages.length > 0 && (
        <div style={{ marginTop: '20px' }}>
          <h2>渲染的页面图片：</h2>
          {renderedPageImages.map((img, index) => (
            <div key={index} style={{ marginBottom: '10px', border: '1px solid #eee', padding: '10px' }}>
              <p>页面 {img.page}:</p>
              <img src={img.dataUri} alt={`Rendered Page ${img.page}`} style={{ maxWidth: '100%', border: '1px solid #ccc' }} />
            </div>
          ))}
        </div>
      )} */}
    </div>
  );
};

export default UnpdfTestPage;