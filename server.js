import express from 'express';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises'; // Use promise-based fs for async operations
import session from 'express-session';
import cors from 'cors';
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';
import { syncDatabase, getDatabaseStatus, isDatabaseConnected, setupDatabaseReconnection, User, Article, Api, File } from './model.js'; // Added File
import {baseUrl, reactPort, AUTH0_REDIRECT_URI,SESSION_SECRET,AUTH0_CLIENT_SECRET,AUTH0_DOMAIN,AUTH0_CLIENT_ID,JWT_SECRET,JWT_EXPIRES_IN, defaultApiUrl, defaultApiKey, defaultApiModel} from './utils/constant.server.js';
import jwt from 'jsonwebtoken';
import sharp from 'sharp';
import cookieParser from 'cookie-parser';

// 用于存储后台导入任务的状态
const importTasks = {}; // { taskId: { status: 'pending' | 'processing' | 'completed' | 'failed', progressMessage: '', data: {}, error: null, result: null, userId: '' } }

// Constants for Data URI chunking
const MAX_DATA_URI_CHUNK_SIZE = 4 * 1024 * 1024; // 4MB limit for each chunk's Data URI string in the DB

// 图片压缩辅助函数
async function compressImageIfNeeded(fileBuffer, mimeType, maxWidth = 800) {
  // 检查是否为图片类型
  if (!mimeType.startsWith('image/')) {
    return fileBuffer; // 非图片文件直接返回原始数据
  }

  // 检查是否为支持的图片格式
  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/tiff', 'image/gif'];
  if (!supportedFormats.includes(mimeType.toLowerCase())) {
    return fileBuffer; // 不支持的格式直接返回原始数据
  }

  try {
    const image = sharp(fileBuffer);
    const metadata = await image.metadata();

    // 如果图片宽度小于等于最大宽度，直接返回原始数据
    if (!metadata.width || metadata.width <= maxWidth) {
      return fileBuffer;
    }

    // 压缩图片，保持宽高比
    const compressedBuffer = await image
      .resize(maxWidth, null, {
        withoutEnlargement: true, // 不放大图片
        fit: 'inside' // 保持宽高比
      })
      .jpeg({ quality: 85 }) // 转换为JPEG格式，质量85%
      .toBuffer();

    console.log(`[Image Compression] 原始尺寸: ${metadata.width}x${metadata.height}, 压缩后大小: ${fileBuffer.length} -> ${compressedBuffer.length} bytes`);

    return compressedBuffer;
  } catch (error) {
    console.error('[Image Compression] 图片压缩失败:', error);
    return fileBuffer; // 压缩失败时返回原始数据
  }
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
// 修改 CORS 配置
const corsOptions = {
  origin: (origin, callback) => {
    const feBaseUrl = baseUrl.replace(/\/$/,""); // 前端期望的 baseUrl
    const allowedOrigins = [feBaseUrl];

    // 在非生产环境，确保 localhost:3000 (Vite dev server) 被允许
    // origin 参数是请求头中的 Origin 字段
    if (process.env.NODE_ENV !== 'production') {
      const devOrigin = `http://localhost:${reactPort}`;
      if (!allowedOrigins.includes(devOrigin)) {
        allowedOrigins.push(devOrigin);
      }
    }

    // 如果请求没有 Origin (例如服务器间请求或某些工具)，或者 Origin 在允许列表中
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error(`Origin ${origin} not allowed by CORS`));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'], // 包含常用方法
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token', 'Accept', 'Origin', 'x-stainless-timeout', 'x-stainless-arch', 'x-stainless-os', 'x-stainless-runtime', 'x-stainless-runtime-version', 'x-stainless-sdk-version', 'x-stainless-package-version', 'x-stainless-retry-count'], // 包含常用请求头及 OpenAI SDK 可能发送的头部
  exposedHeaders: ['Content-Range', 'X-Content-Range', 'Set-Cookie'] // 允许前端读取的响应头
};
// Custom OPTIONS handler for /api/openai/* to dynamically allow x-stainless-* headers
app.options('/api/openai/*', (req, res) => {
  const requestOrigin = req.headers.origin;

  // Determine allowed origins (replicating logic from global corsOptions)
  // baseUrl and reactPort are available from imports at the top of the file
  const feBaseUrl = baseUrl.replace(/\/$/,"");
  let determinedAllowedOrigins = [feBaseUrl];
  if (process.env.NODE_ENV !== 'production') {
    const devOrigin = `http://localhost:${reactPort}`;
    if (!determinedAllowedOrigins.includes(devOrigin)) {
      determinedAllowedOrigins.push(devOrigin);
    }
  }

  // Check if origin is allowed
  if (!requestOrigin || determinedAllowedOrigins.includes(requestOrigin)) {
    res.header('Access-Control-Allow-Origin', requestOrigin); // Reflect the allowed origin
  } else {
    return res.status(403).json({ error: `Origin ${requestOrigin} not allowed by CORS for this path.` });
  }

  res.header('Access-Control-Allow-Credentials', 'true');
  // Define methods allowed for the actual request (e.g., POST for chat completions, GET, etc.)
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');

  let headersToAllow = ['Content-Type', 'Authorization', 'Accept', 'Origin']; // Base headers always allowed
  const requestedHeaders = req.headers['access-control-request-headers'];

  if (requestedHeaders) {
    // Headers are comma-separated. Trim whitespace and convert to lowercase for consistent matching.
    const individualRequestedHeaders = requestedHeaders.split(',').map(h => h.trim().toLowerCase());
    individualRequestedHeaders.forEach(header => {
      // Add any requested header that starts with 'x-stainless-'
      if (header.startsWith('x-stainless-')) {
        if (!headersToAllow.includes(header)) { // Avoid duplicates
          headersToAllow.push(header);
        }
      }
    });
  }
  res.header('Access-Control-Allow-Headers', headersToAllow.join(', '));

  // Optional: Max age for preflight request caching by the browser
  // res.header('Access-Control-Max-Age', '86400'); // 24 hours in seconds

  res.sendStatus(204); // Standard response for successful OPTIONS preflight
});
app.use(cors(corsOptions));

// 确保 OPTIONS 请求能被正确处理 (cors 中间件通常会处理，但显式添加无害)
// 这对于带凭据的复杂请求（如POST with Content-Type: application/json）的预检尤其重要
app.options('*', cors(corsOptions));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(cookieParser());

// 会话配置
app.use(session({
  secret: SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 24 * 60 * 60 * 1000 // 60天
  }
}));



// JWT认证中间件
const authenticateJWT = async (req, res, next) => {
  const token = req.cookies.token || req.headers.authorization?.split(' ')[1];

  if (!token) {
    // 尝试从会话中获取用户ID
    if (req.session.userId) {
      try {
        const user = await User.findOne({ where: { uuid: req.session.userId } });
        if (user) {
          req.user = user;
          // 创建JWT令牌并设置Cookie
          const token = jwt.sign({ userId: user.uuid }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
          res.cookie('token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
            sameSite: 'lax' // 允许跨站点请求
          });
        }
      } catch (error) {
        // console.error('会话验证错误:', error); // 保留此错误日志或按需移除
      }
    }
    return next();
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const user = await User.findOne({ where: { uuid: decoded.userId } });

    if (!user) {
      return next();
    }

    req.user = user;
    // 更新会话，确保会话和JWT保持同步
    req.session.userId = user.uuid;
    next();
  } catch (error) {
    // console.error('[AuthJWT] JWT verification error:', error.message); // 保留此错误日志或按需移除
    // 清除无效的令牌
    res.clearCookie('token');
    next();
  }
};

// 应用JWT认证中间件
app.use(authenticateJWT);

// 数据库连接等待中间件
const waitForDatabaseConnection = async (req, res, next) => {
  // 跳过不需要数据库的路由（注意这里的路径是相对于 /api 的）
  const skipPaths = [
    '/debug/db-status',
    '/health'
  ];

  if (skipPaths.some(skipPath => req.path.startsWith(skipPath))) {
    return next();
  }

  // 如果数据库已连接，直接继续
  if (isDatabaseConnected()) {
    return next();
  }

  // 数据库未连接，等待连接成功
  console.log(`[DB Wait] 数据库未连接，等待连接成功: ${req.method} ${req.originalUrl}`);

  const maxWaitTime = 30000; // 最大等待30秒
  const checkInterval = 1000; // 每1秒检查一次
  const startTime = Date.now();

  // 设置请求超时
  const timeoutId = setTimeout(() => {
    if (!res.headersSent) {
      console.error(`[DB Wait] 等待数据库连接超时: ${req.method} ${req.originalUrl}`);
      const dbStatus = getDatabaseStatus();
      res.status(503).json({
        error: '数据库连接超时',
        message: '数据库连接等待超时，请稍后重试',
        dbStatus: {
          isConnected: dbStatus.isConnected,
          isConnecting: dbStatus.isConnecting,
          lastError: dbStatus.lastError
        }
      });
    }
  }, maxWaitTime);

  // 定期检查数据库连接状态
  const checkConnection = () => {
    if (isDatabaseConnected()) {
      clearTimeout(timeoutId);
      if (!res.headersSent) {
        console.log(`[DB Wait] 数据库连接成功，继续处理请求: ${req.method} ${req.originalUrl}`);
        next();
      }
    } else if (Date.now() - startTime < maxWaitTime) {
      setTimeout(checkConnection, checkInterval);
    }
    // 如果超时，由上面的timeout处理
  };

  // 开始检查
  checkConnection();
};

// 应用数据库连接等待中间件到所有API路由
app.use('/api', waitForDatabaseConnection);

// 添加数据库状态检查路由（不需要数据库连接）
app.get('/api/debug/db-status', (_, res) => {
  const dbStatus = getDatabaseStatus();
  res.json({
    status: dbStatus.isConnected ? 'connected' : 'disconnected',
    ...dbStatus
  });
});

// 添加全局错误处理中间件
app.use((err, _, res, __) => {
  console.error('全局错误:', err);
  console.error('错误堆栈:', err.stack);
  res.status(500).json({ error: '服务器错误', message: err.message });
});

// Auth0 登录回调路由
app.get('/api/auth/callback', async (req, res) => {
  const { code } = req.query;

  // 如果用户已登录，直接重定向到主页
  if (req.user) {
    return res.redirect(baseUrl);
  }

  if (!code) {
    return res.status(400).json({ error: '缺少授权码' });
  }

  try {
    // 获取访问令牌
    const tokenResponse = await fetch(`https://${AUTH0_DOMAIN}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        client_id: AUTH0_CLIENT_ID,
        client_secret: AUTH0_CLIENT_SECRET,
        code,
        redirect_uri: AUTH0_REDIRECT_URI,
      })
    });

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    if (!accessToken) {
      console.error('获取访问令牌失败:', tokenData);
      return res.status(400).json({ error: '获取访问令牌失败' });
    }

    // 获取用户信息
    const userResponse = await fetch(`https://${AUTH0_DOMAIN}/userinfo`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    const userData = await userResponse.json();

    // 在数据库中查找或创建用户
    try {
      let user = await User.findOne({ where: { auth0Id: userData.sub } });
      const { sub, email, name, nickname, picture, ...otherAuth0Fields } = userData;

      const userConfig = {
        email: email,
        username: name,
        nickname: nickname || null,
        avatar: picture || null,
        translationPrompt: '', // 新用户默认值
        titleGenerationPrompt: '', // 新用户默认值 (假设也在此处初始化)
        summaryGenerationPrompt: '', // 新用户默认值
        concurrencyLevel: 3, // 新用户默认值
        chunkSize: 5, // 新用户默认值
        defaultApiModel: ['', ''], // 新用户默认值
        ...otherAuth0Fields // 来自Auth0的其他字段也放入config
      };

      if (!user) {
        user = await User.create({
          uuid: uuidv4(),
          auth0Id: sub,
          config: userConfig
        });
      } else {
        // 更新用户信息，合并现有的 config 和新的信息
        const existingConfig = user.config || {};
        const { userPrompt, systemPrompt, ...restOfExistingConfig } = existingConfig; // 移除旧的提示词字段
        const updatedConfig = {
          ...restOfExistingConfig, // 保留用户已有的其他设置
          email: email, // 更新来自 Auth0 的信息
          username: name,
          nickname: nickname || null,
          avatar: picture || null,
          translationPrompt: existingConfig.translationPrompt || existingConfig.systemPrompt || '', // 迁移旧的 systemPrompt
          titleGenerationPrompt: existingConfig.titleGenerationPrompt || '',
          summaryGenerationPrompt: existingConfig.summaryGenerationPrompt || '', // 保留现有的AI总结提示词
          ...otherAuth0Fields // 合并其他 Auth0 字段
        };
        await user.update({ config: updatedConfig });
      }

      // 设置会话
      req.session.userId = user.uuid;

      // 创建JWT令牌
      const token = jwt.sign({ userId: user.uuid }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

      // 设置Cookie
      res.cookie('token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 30 * 24 * 60 * 60 * 1000 // 30天
      });

      // 重定向到主页
      res.redirect(baseUrl);
    } catch (dbError) {
      console.error('数据库操作错误:', dbError);
      res.status(500).json({ error: '数据库操作失败', message: dbError.message });
    }
  } catch (error) {
    console.error('Auth0登录错误:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ error: '登录处理失败', message: error.message });
  }
});

// 获取当前登录用户信息
app.get('/api/auth/me', async (req, res) => {
  console.log('[Auth Me] Received request for /api/auth/me');

  if (req.user && req.user.uuid) {
    // 用户已通过JWT认证
    console.log('[Auth Me] User authenticated via JWT. User ID:', req.user.uuid);
    //console.log('[Auth Me] req.user.config from JWT path (before toJSON):', JSON.stringify(req.user.config));
    const userFromJwt = req.user.toJSON();
    // 确保默认值存在
    userFromJwt.concurrencyLevel = userFromJwt.concurrencyLevel ?? 3;
    userFromJwt.chunkSize = userFromJwt.chunkSize ?? 5;
    userFromJwt.defaultApiModel = userFromJwt.defaultApiModel || ['', ''];
    userFromJwt.translationPrompt = userFromJwt.translationPrompt || userFromJwt.systemPrompt || '';
    userFromJwt.titleGenerationPrompt = userFromJwt.titleGenerationPrompt || '';
    userFromJwt.summaryGenerationPrompt = userFromJwt.summaryGenerationPrompt || '';
    userFromJwt.referenceParsePrompt = userFromJwt.referenceParsePrompt || '';
    userFromJwt.glossary = userFromJwt.glossary || '';
    //console.log('[Auth Me] Final userFromJwt being sent (JWT path):', JSON.stringify(userFromJwt));
    delete userFromJwt.systemPrompt;
    delete userFromJwt.userPrompt;
    return res.json(userFromJwt);
  }

  if (req.session.userId) {
    // 用户通过会话认证
    console.log('[Auth Me] User authenticated via session. Session User ID:', req.session.userId);
    try {
      const user = await User.findOne({ where: { uuid: req.session.userId } });
      if (!user) {
        req.session.destroy();
        return res.status(401).json({ error: '用户不存在或会话无效' });
      }
      console.log('[Auth Me] User.findOne result config (session path):', JSON.stringify(user.config));

      // 创建JWT令牌并设置Cookie
      const token = jwt.sign({ userId: user.uuid }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
      res.cookie('token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
        sameSite: 'lax'
      });

      const userJson = user.toJSON();
      // 确保默认值存在
      userJson.concurrencyLevel = userJson.concurrencyLevel ?? 3;
      userJson.chunkSize = userJson.chunkSize ?? 5;
      userJson.defaultApiModel = userJson.defaultApiModel || ['', ''];
      userJson.translationPrompt = userJson.translationPrompt || userJson.systemPrompt || '';
      userJson.titleGenerationPrompt = userJson.titleGenerationPrompt || '';
      userJson.summaryGenerationPrompt = userJson.summaryGenerationPrompt || '';
      userJson.referenceParsePrompt = userJson.referenceParsePrompt || '';
      userJson.glossary = userJson.glossary || '';
      //console.log('[Auth Me] Final userJson being sent (session path):', JSON.stringify(userJson));
      delete userJson.systemPrompt;
      delete userJson.userPrompt;
      return res.json(userJson);
    } catch (error) {
      console.error('通过会话获取用户信息错误:', error);
      return res.status(500).json({ error: '获取用户信息失败' });
    }
  }

  console.log('[Auth Me] No user authenticated, returning 401.');
  return res.status(401).json({ error: '未登录' });
});

// 退出登录
app.get('/api/auth/logout', (req, res) => {
  req.session.destroy();

  // 清除JWT Cookie
  res.clearCookie('token');

  const logoutUrl = `https://${AUTH0_DOMAIN}/v2/logout?client_id=${AUTH0_CLIENT_ID}&returnTo=${encodeURIComponent(process.env.NODE_ENV === 'production' ? 'https://tsgv3.vercel.app' : 'http://localhost:3000')}`;
  res.redirect(logoutUrl);
}); // <-- 添加缺失的结束括号

// 合并的启动数据API - 一次请求获取用户信息、文章列表、API配置和可选的文章内容
app.get('/api/bootstrap', async (req, res) => {
  if (!req.user || !req.user.uuid) {
    return res.status(401).json({ error: '未授权或无法识别用户' });
  }

  const { articleUuid } = req.query; // 可选的文章UUID参数

  try {
    // 并行获取所有数据
    const [user, articles, apis, articleData] = await Promise.all([
      // 1. 获取用户信息 (类似 /api/auth/me)
      User.findOne({ where: { uuid: req.user.uuid } }),

      // 2. 获取用户文章列表 (类似 /api/articles/my)
      Article.findAll({
        where: { userId: req.user.uuid },
        attributes: ['uuid', 'title', 'createdAt'],
        order: [['createdAt', 'DESC']]
      }),

      // 3. 获取用户API配置 (类似 /api/apis)
      Api.findAll({
        where: { userId: req.user.uuid },
        attributes: ['uuid', 'provider', 'apiUrl', 'apiKey', 'models', 'order'],
        order: [['order', 'ASC']]
      }),

      // 4. 可选：获取特定文章内容 (类似 /api/article/:uuid/edit)
      articleUuid ? Article.findOne({
        where: {
          uuid: articleUuid,
          userId: req.user.uuid
        },
        attributes: ['uuid', 'content', 'translated', 'title', 'summary', 'refs', 'citation']
      }) : null
    ]);

    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 格式化用户数据
    const userJson = user.toJSON();
    userJson.concurrencyLevel = userJson.concurrencyLevel ?? 3;
    userJson.chunkSize = userJson.chunkSize ?? 5;
    userJson.defaultApiModel = userJson.defaultApiModel || ['', ''];
    userJson.translationPrompt = userJson.translationPrompt || userJson.systemPrompt || '';
    userJson.titleGenerationPrompt = userJson.titleGenerationPrompt || '';
    userJson.summaryGenerationPrompt = userJson.summaryGenerationPrompt || '';
    userJson.referenceParsePrompt = userJson.referenceParsePrompt || '';
    userJson.glossary = userJson.glossary || '';
    delete userJson.systemPrompt;
    delete userJson.userPrompt;

    // 构建响应数据
    const responseData = {
      user: userJson,
      articles: articles || [],
      apis: apis || []
    };

    // 如果请求了特定文章，添加文章数据
    if (articleUuid && articleData) {
      responseData.article = {
        uuid: articleData.uuid,
        title: articleData.title || '',
        summary: articleData.summary || null,
        refs: articleData.refs || null,
        citation: articleData.citation || null,
        content: Array.isArray(articleData.content) ? articleData.content : [],
        translated: articleData.translated || {}
      };
    } else if (articleUuid && !articleData) {
      // 请求了文章但未找到
      responseData.article = null;
    }

    res.json(responseData);
  } catch (error) {
    console.error('获取启动数据失败:', error);
    res.status(500).json({ error: '获取启动数据失败', message: error.message });
  }
});

// 获取当前登录用户的所有文章 (保留原有端点以兼容)
app.get('/api/articles/my', async (req, res) => {
  if (!req.user || !req.user.uuid) {
    return res.status(401).json({ error: '未授权或无法识别用户' });
  }

  try {
    const articles = await Article.findAll({
      where: { userId: req.user.uuid },
      attributes: ['uuid', 'title', 'createdAt'], // 获取 uuid, title 和 createdAt 用于排序或显示
      order: [['createdAt', 'DESC']], // 按创建时间降序排列
    });

    res.json({ articles: articles || [] });
  } catch (error) {
    console.error('获取用户文章列表失败:', error);
    res.status(500).json({ error: '获取文章列表失败', message: error.message });
  }
});

// 新建空白文章
app.post('/api/articles/new', async (req, res) => {
  if (!req.user || !req.user.uuid) {
    return res.status(401).json({ error: '未授权或无法识别用户' });
  }
  const userId = req.user.uuid;

  try {
    // 创建 Article 记录
    const newArticle = await Article.create({
      uuid: uuidv4(),
      userId: userId,
      content: [], // 原文内容直接存储在 Article 中
      translated: [], // 空的译文内容
      title: `无标题文章 - ${Date.now()}`, // 默认标题，带时间戳以区分
      // doi 字段默认为 null 或根据需要设置
    });

    res.json({
      success: true,
      newArticleUuid: newArticle.uuid,
      title: newArticle.title
    });

  } catch (error) {
    console.error(`用户 ${userId} 新建空白文章失败:`, error);
    res.status(500).json({ error: '新建空白文章失败', message: error.message });
  }
});

// 删除用户自己的文章
app.delete('/api/articles/:articleId', async (req, res) => {
  if (!req.user || !req.user.uuid) {
    return res.status(401).json({ error: '未授权或无法识别用户' });
  }

  const { articleId } = req.params;
  const userId = req.user.uuid;

  try {
    const article = await Article.findOne({
      where: {
        uuid: articleId,
        userId: userId,
      },
    });

    if (!article) {
      return res.status(404).json({ error: '文章未找到或无权删除' });
    }

    await article.destroy();
    // 注意：此处不删除关联的 Content 记录，因为 Content 可能被其他用户或用途共享。
    // 如果业务逻辑确定 Content 应该在 Article 删除时一起删除（例如，Content 是 Article 的私有部分），
    // 则需要在这里添加删除 Content 的逻辑，并考虑 Content 是否还被其他 Article 引用。

    res.status(200).json({ success: true, message: '文章删除成功' });
  } catch (error) {
    console.error(`删除文章 ${articleId} (用户 ${userId}) 失败:`, error);
    res.status(500).json({ error: '删除文章失败', message: error.message });
  }
});

// 调试路由 - 检查数据库连接和用户表
app.get('/api/debug/users', async (_, res) => {
  try {
    const users = await User.findAll();
    res.json({ count: users.length, users });
  } catch (error) {
    console.error('调试路由错误:', error);
    res.status(500).json({ error: error.message });
  }
});



// 文章API
// 此路由的功能现在更通用，不仅仅是提交DOI，而是创建或更新文章内容
// 考虑重命名为例如 /api/articles/submit 或 /api/articles/upsertContent
app.post('/api/articles/submitContent', async (req, res) => { // 重命名路由，移除Doi
  const { content, title } = req.body; // 现在只接收 content 和可选的 title

  if (!req.user || !req.user.uuid) { // 使用 req.user
    return res.status(401).json({ error: '未登录或用户无法识别' });
  }
  const userId = req.user.uuid;

  try {
    // 由于没有了DOI作为唯一标识符，这里的逻辑需要调整。
    // 如果是创建新文章：
    const article = await Article.create({
      uuid: uuidv4(),
      content,
      userId: userId,
      translated: [],
      title: title || `新文章 - ${Date.now()}` // 如果没有提供标题，则生成一个默认标题
    });

    res.json({ success: true, articleId: article.uuid, title: article.title });
  } catch (error) {
    console.error('提交内容错误:', error);
    res.status(500).json({ error: '提交内容失败' });
  }
});

// 设置用户默认API模型
app.post('/api/user/defaultApiModel', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });

  try {
    const { provider, model } = req.body;

    // 检查 provider 和 model 是否都为 null，用于清除设置
    if (provider === null && model === null) {
      const user = await User.findOne({ where: { uuid: req.user.uuid } });
      if (!user) {
        return res.status(404).json({ error: '用户不存在' });
      }
      const currentConfig = user.config || {};
      const updatedConfig = {
        ...currentConfig,
        defaultApiModel: [null, null] // 或者 []，取决于您希望如何在数据库中表示“未设置”
      };
      await User.update(
        { config: updatedConfig },
        { where: { uuid: user.uuid } }
      );
      return res.json({ success: true, message: '用户默认API模型已清除', defaultApiModel: [null, null] });
    }

    // 如果不是清除操作，则 provider 和 model 都必须存在
    if (!provider || !model) {
      return res.status(400).json({ error: '缺少provider或model参数 (或仅一个为null)' });
    }

    const user = await User.findOne({ where: { uuid: req.user.uuid } });
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    const currentConfig = user.config || {};
    const updatedConfig = {
      ...currentConfig,
      defaultApiModel: [provider, model]
    };

    await User.update(
      { config: updatedConfig },
      { where: { uuid: user.uuid } }
    );

    res.json({ success: true, defaultApiModel: [provider, model] });
  } catch (error) {
    console.error('保存默认API模型设置失败:', error);
    res.status(500).json({ error: '保存默认API模型设置失败', message: error.message });
  }
});

// 设置用户默认分块大小
app.post('/api/user/chunkSize', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });

  try {
    const { chunkSize } = req.body;
    if (chunkSize === undefined || typeof chunkSize !== 'number' || !Number.isInteger(chunkSize) || chunkSize < 1) {
      return res.status(400).json({ error: '无效的 chunkSize 参数，必须是大于等于1的整数' });
    }

    const user = await User.findOne({ where: { uuid: req.user.uuid } });
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    const currentConfig = user.config || {};
    const updatedConfig = {
      ...currentConfig,
      chunkSize: chunkSize
    };

    await User.update(
      { config: updatedConfig },
      { where: { uuid: user.uuid } }
    );

    res.json({ success: true, chunkSize: chunkSize });
  } catch (error) {
    console.error('保存分块大小设置失败:', error);
    res.status(500).json({ error: '保存分块大小设置失败', message: error.message });
  }
});


// 设置用户默认并发级别
app.post('/api/user/concurrencyLevel', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });

  try {
    const { concurrencyLevel } = req.body;
    if (concurrencyLevel === undefined || typeof concurrencyLevel !== 'number' || !Number.isInteger(concurrencyLevel) || concurrencyLevel < 1) {
      return res.status(400).json({ error: '无效的 concurrencyLevel 参数，必须是大于等于1的整数' });
    }

    const user = await User.findOne({ where: { uuid: req.user.uuid } });
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    const currentConfig = user.config || {};
    const updatedConfig = {
      ...currentConfig,
      concurrencyLevel: concurrencyLevel
    };

    await User.update(
      { config: updatedConfig },
      { where: { uuid: user.uuid } }
    );

    res.json({ success: true, concurrencyLevel: concurrencyLevel });
  } catch (error) {
    console.error('保存并发级别设置失败:', error);
    res.status(500).json({ error: '保存并发级别设置失败', message: error.message });
  }
});

// 保存用户提示词设置
app.post('/api/user/prompts', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });

  try {
    const { translationPrompt, titleGenerationPrompt, summaryGenerationPrompt, parseTextPrompt, referenceParsePrompt, glossary } = req.body;
    // 确保至少 translationPrompt 存在，其他提示词可以是可选的空字符串
    if (translationPrompt === undefined) {
      return res.status(400).json({ error: '缺少 translationPrompt 参数' });
    }

    const user = await User.findOne({ where: { uuid: req.user.uuid } });
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    const currentConfig = user.config || {};
    const { systemPrompt, userPrompt, ...restOfConfig } = currentConfig; // 移除旧字段
    const updatedConfig = {
      ...restOfConfig,
      translationPrompt: translationPrompt,
      titleGenerationPrompt: titleGenerationPrompt === undefined ? (currentConfig.titleGenerationPrompt || '') : titleGenerationPrompt, // 保留或更新
      summaryGenerationPrompt: summaryGenerationPrompt === undefined ? (currentConfig.summaryGenerationPrompt || '') : summaryGenerationPrompt, // 保留或更新
      parseTextPrompt: parseTextPrompt === undefined ? (currentConfig.parseTextPrompt || '') : parseTextPrompt,
      referenceParsePrompt: referenceParsePrompt === undefined ? (currentConfig.referenceParsePrompt || '') : referenceParsePrompt, // 保留或更新参考文献解析提示词
      glossary: glossary === undefined ? (currentConfig.glossary || '') : glossary // 保留或更新术语库
    };
    console.log(`[User Prompts] Attempting to update user ${user.uuid} with config:`, JSON.stringify(updatedConfig));

    const updateResultArray = await User.update( // 移除 returning: true，并获取完整的返回数组
      { config: updatedConfig },
      { where: { uuid: user.uuid } }
    );
    // Sequelize update for MySQL/MariaDB returns an array like [affectedRowsCount]
    // For other dialects, it might be [affectedRowsCount, affectedRows] if returning is supported and used.
    // Since we removed returning: true, we expect [affectedRowsCount].
    console.log('[User Prompts] User.update result (expected [affectedRowsCount]):', JSON.stringify(updateResultArray));

    const affectedRows = Array.isArray(updateResultArray) ? updateResultArray[0] : undefined;

    // 检查更新是否真的影响了行
    if (affectedRows === 0) {
        console.warn(`[User Prompts] User.update for user ${user.uuid} affected 0 rows. Config was:`, JSON.stringify(updatedConfig));
    } else if (affectedRows === undefined) {
        console.error(`[User Prompts] User.update for user ${user.uuid} returned an unexpected result:`, JSON.stringify(updateResultArray), ". Config was:", JSON.stringify(updatedConfig));
        // 如果更新操作本身返回了意外结果，应该向上抛出错误
        return res.status(500).json({ error: '保存用户提示词设置时发生意外错误', message: 'Update operation returned unexpected result.' });
    }

    // 更新成功后，重新从数据库读取最新的用户信息以确保数据一致性
    const updatedUser = await User.findOne({ where: { uuid: user.uuid } });
    if (!updatedUser || !updatedUser.config) {
        console.error(`[User Prompts] Failed to re-fetch user or user config after update for user ${user.uuid}`);
        return res.status(500).json({ error: '保存成功但无法获取最新用户配置' });
    }

    console.log('[User Prompts] Successfully updated and re-fetched user config:', JSON.stringify(updatedUser.config));

    res.json({
      success: true,
      translationPrompt: updatedUser.config.translationPrompt,
      titleGenerationPrompt: updatedUser.config.titleGenerationPrompt,
      summaryGenerationPrompt: updatedUser.config.summaryGenerationPrompt, // 返回 summaryGenerationPrompt
      parseTextPrompt: updatedUser.config.parseTextPrompt, // 返回 parseTextPrompt
      referenceParsePrompt: updatedUser.config.referenceParsePrompt, // 返回参考文献解析提示词
      glossary: updatedUser.config.glossary // 返回术语库
    });
  } catch (error) {
    console.error(`[User Prompts] 保存用户提示词设置失败 for user ${req.user?.uuid}:`, error);
    res.status(500).json({ error: '保存用户提示词设置失败', message: error.message });
  }
});

// OpenAI 兼容接口
app.post('/api/v1/chat/completions', async (req, res) => {
  console.log('[OpenAI API] 收到请求，Origin:', req.headers.origin);
  console.log('[OpenAI API] 用户认证状态:', !!req.user);

  // 恢复对用户登录状态的检查
  if (!req.user) {
    console.log('[OpenAI API] 用户未登录，返回401');
    return res.status(401).json({ error: '未授权，用户未登录' });
  }

  try {
    const { messages, stream, ...rest } = req.body;
    console.log('[OpenAI API] 请求体参数:', {
      messagesCount: messages?.length,
      stream,
      model: rest.model,
      temperature: rest.temperature
    });

    const payload = {
      ...rest,
      messages,
      model: defaultApiModel, // 使用默认模型
      stream: stream || false, // 确保stream有默认值
    };

    console.log('[OpenAI API] 向上游API发送请求:', {
      url: defaultApiUrl,
      model: payload.model,
      stream: payload.stream
    });

    const response = await fetch(defaultApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${defaultApiKey}`,
      },
      body: JSON.stringify(payload),
    });

    // 首先，检查响应状态码是否表示成功 (2xx)
    if (!response.ok) {
      let upstreamErrorBody = await response.text(); // 获取原始响应体文本
      let errorDetails = `Upstream API returned status ${response.status}.`;
      try {
        // 尝试解析为JSON，如果上游返回的是JSON错误
        const errorJson = JSON.parse(upstreamErrorBody);
        errorDetails += ` Body: ${JSON.stringify(errorJson)}`;
        console.error('OpenAI 兼容接口错误 (上游返回JSON):', errorJson);
      } catch (e) {
        // 如果解析失败，说明上游返回的不是JSON
        errorDetails += ` Raw Body: ${upstreamErrorBody.substring(0, 500)}${upstreamErrorBody.length > 500 ? '...' : ''}`; // 限制长度以防日志过长
        console.error('OpenAI 兼容接口错误 (上游返回非JSON):', upstreamErrorBody);
      }
      return res.status(502).json({ // 使用 502 Bad Gateway
        success: false,
        error: {
          type: "UPSTREAM_API_ERROR",
          message: "The upstream chat API returned an error or an invalid response.",
          details: errorDetails
        }
      });
    }

    // 如果响应状态码是成功的，再尝试解析JSON
    if (stream) {
      res.setHeader('Content-Type', 'text/event-stream');
      response.body.pipe(res);
    } else {
      try {
        const data = await response.json();
        res.json(data);
      } catch (syntaxError) {
        // 捕获 response.json() 可能抛出的 SyntaxError
        const rawBody = await response.text(); // 尝试获取原始文本，但注意response可能已被消费
        console.error('OpenAI 兼容接口 - JSON解析错误:', syntaxError.message);
        console.error('OpenAI 兼容接口 - 原始响应体 (可能不完整或已消费):', rawBody.substring(0, 500)); // 记录原始响应体（部分）
        return res.status(502).json({ // 使用 502 Bad Gateway
          success: false,
          error: {
            type: "UPSTREAM_API_ERROR",
            message: "The upstream chat API returned an invalid JSON response.",
            details: `Failed to parse JSON. Error: ${syntaxError.message}. Raw response (partial): ${rawBody.substring(0, 200)}${rawBody.length > 200 ? '...' : ''}`
          }
        });
      }
    }

  } catch (error) {
    // 捕获 fetch 本身的错误，例如网络问题
    console.error('OpenAI 兼容接口请求失败 (网络或其他fetch错误):', error);
    res.status(500).json({
      success: false,
      error: {
        type: "INTERNAL_SERVER_ERROR",
        message: 'Failed to make a request to the upstream API.',
        details: error.message
      }
    });
  }
});



// OpenAI API 代理路由
app.all('/api/openai/*', async (req, res) => {
  const targetUrl = req.params[0];

  if (!targetUrl) {
    return res.status(400).json({ error: '目标 URL 未提供' });
  }

  // 基本的 URL 验证，防止 SSRF (虽然任务说超出核心要求，但这是一个好的实践)
  try {
    const url = new URL(targetUrl);
    if (!['http:', 'https:'].includes(url.protocol)) {
      return res.status(400).json({ error: '无效的目标 URL 协议' });
    }
    // 可选：添加更严格的域名白名单或黑名单检查
  } catch (e) {
    return res.status(400).json({ error: '无效的目标 URL 格式' });
  }

  try {
    const { method, headers: clientHeaders, body: clientBody } = req;

    // 准备转发的头部，移除 host 和与代理相关的头部
    const headersToForward = { ...clientHeaders };
    delete headersToForward.host;
    delete headersToForward['x-forwarded-for'];
    delete headersToForward['x-forwarded-proto'];
    delete headersToForward['x-forwarded-host'];
    // Content-Length 会由 node-fetch 自动处理，如果提供了 body
    // 如果是 GET 或 HEAD 请求，显式删除 Content-Length 和 Content-Type
    if (method === 'GET' || method === 'HEAD') {
        delete headersToForward['content-length'];
        delete headersToForward['content-type'];
    }

    const options = {
      method,
      headers: headersToForward,
      body: (method !== 'GET' && method !== 'HEAD' && clientBody && (typeof clientBody === 'string' || (typeof clientBody === 'object' && Object.keys(clientBody).length > 0)))
            ? (typeof clientBody === 'string' ? clientBody : JSON.stringify(clientBody))
            : undefined,
      redirect: 'follow', // 跟随重定向
    };

    const proxyResponse = await fetch(targetUrl, options);

    // 将目标 API 的头部转发回客户端
    const responseHeadersForClient = {};
    proxyResponse.headers.forEach((value, name) => {
      const lowerName = name.toLowerCase();
      if (lowerName !== 'transfer-encoding' && lowerName !== 'connection') {
        responseHeadersForClient[name] = value;
      }
    });

    // Override Access-Control-Allow-Origin
    const clientOrigin = req.headers.origin;
    // baseUrl and reactPort are available from imports at the top of the file
    const feBaseUrl = baseUrl.replace(/\/$/,"");
    let determinedAllowedOrigins = [feBaseUrl];
    if (process.env.NODE_ENV !== 'production') {
      const devOrigin = `http://localhost:${reactPort}`;
      if (!determinedAllowedOrigins.includes(devOrigin)) {
        determinedAllowedOrigins.push(devOrigin);
      }
    }

    if (clientOrigin && determinedAllowedOrigins.includes(clientOrigin)) {
      res.setHeader('Access-Control-Allow-Origin', clientOrigin);
      // Ensure credentials are also allowed if origin is specific and was requested
      if (req.headers['access-control-allow-credentials'] === 'true' || (responseHeadersForClient['access-control-allow-credentials'] && responseHeadersForClient['access-control-allow-credentials'].toLowerCase() === 'true')) {
        res.setHeader('Access-Control-Allow-Credentials', 'true');
      }
      // Remove ACAO from headers copied from target, as we are setting it explicitly.
      delete responseHeadersForClient['access-control-allow-origin']; // lowercase
      delete responseHeadersForClient['Access-Control-Allow-Origin']; // camelcase
      // Also remove credentials if we are setting it, to avoid duplicates from target
      delete responseHeadersForClient['access-control-allow-credentials'];
      delete responseHeadersForClient['Access-Control-Allow-Credentials'];

    } else if (responseHeadersForClient['access-control-allow-origin'] === '*') {
        // If clientOrigin is not in allowed list OR not provided (e.g. server-to-server),
        // AND target returned '*', this is problematic for credentialed requests.
        // Our custom OPTIONS handler should have already dealt with the preflight for browsers.
        // If a browser somehow skipped preflight or this is a non-browser client sending credentials
        // with an unallowed origin, we should not reflect '*'.
        delete responseHeadersForClient['access-control-allow-origin'];
        delete responseHeadersForClient['Access-Control-Allow-Origin'];
    }
    // If ACAO was something specific from target but not '*', and clientOrigin didn't match,
    // we let it pass through, but this scenario should be rare for credentialed browser requests
    // due to preflight.


    Object.entries(responseHeadersForClient).forEach(([name, value]) => {
        // Only set headers that haven't been explicitly set by our ACAO override logic above
        // This check is a bit redundant if deletions were thorough, but acts as a safeguard.
        const lowerName = name.toLowerCase();
        if (lowerName !== 'access-control-allow-origin' && lowerName !== 'access-control-allow-credentials') {
             res.setHeader(name, value);
        } else if (lowerName === 'access-control-allow-origin' && !res.getHeader('Access-Control-Allow-Origin')) {
            // If we decided to pass through target's specific ACAO (not '*') and didn't set our own
            res.setHeader(name, value);
        } else if (lowerName === 'access-control-allow-credentials' && !res.getHeader('Access-Control-Allow-Credentials')) {
            // If we decided to pass through target's specific ACAC (not 'true') and didn't set our own
             res.setHeader(name, value);
        }
    });
    res.status(proxyResponse.status);

    // 流式传输响应体
    // 检查是否是流式响应，如果是，则直接pipe
    const contentType = proxyResponse.headers.get('content-type');
    if (contentType && contentType.includes('text/event-stream')) {
        proxyResponse.body.pipe(res);
    } else {
        // 对于非流式响应，先读取完整内容再发送，以便记录
        const responseBodyBuffer = await proxyResponse.buffer(); // node-fetch specific
        res.send(responseBodyBuffer);
    }

  } catch (error) {
    console.error(`Error during proxy request to ${targetUrl}:`, error.message);
    console.error(`Proxy error stack:`, error.stack);
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return res.status(502).json({ error: '无法连接到目标 API', details: error.message });
    }
    if (error.name === 'FetchError' && error.message.includes('timeout')) {
        return res.status(504).json({ error: '目标 API 请求超时', details: error.message });
    }
    // 尝试返回更详细的错误信息，如果可用
    const errorResponse = {
        error: '代理请求时发生内部服务器错误',
        details: error.message,
        type: error.type, // e.g., 'system'
        code: error.code, // e.g., 'ECONNRESET'
        errno: error.errno
    };
    console.error(`Sending proxy error response to client:`, JSON.stringify(errorResponse, null, 2));
    res.status(500).json(errorResponse);
  }
});

// API管理路由
app.get('/api/apis', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });

  try {
    const apis = await Api.findAll({
      where: { userId: req.user.uuid },
      attributes: ['uuid', 'provider', 'apiUrl', 'apiKey', 'models', 'order'],
      order: [['order', 'ASC']]
    });
    res.json(apis);
  } catch (error) {
    res.status(500).json({ error: '获取API失败' });
  }
});

app.post('/api/apis', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });

  try {
    // 查找当前用户的API数量，用于设置新API的顺序
    const count = await Api.count({
      where: { userId: req.user.uuid }
    });

    const api = await Api.create({
      provider: req.body.provider,
      apiUrl: req.body.apiUrl,
      apiKey: req.body.apiKey,
      models: req.body.models,
      userId: req.user.uuid,
      order: count // 设置为当前数量，这样新API会排在最后
    });
    res.json(api);
  } catch (error) {
    console.error('创建API失败:', error);
    res.status(500).json({ error: '创建API失败', message: error.message });
  }
});

// 重新排序API列表
app.put('/api/apis/reorder', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });

  try {
    const orderedApis = req.body;

    if (!Array.isArray(orderedApis)) {
      return res.status(400).json({ error: '请求体必须是数组格式' });
    }

    // 直接进行批量更新

    const updatePromises = orderedApis.map(item =>
      Api.update(
        { order: item.order },
        {
          where: {
            uuid: item.uuid,
            userId: req.user.uuid
          }
        }
      )
    );

    await Promise.all(updatePromises);

    res.json({ success: true });
  } catch (error) {
    console.error('更新API顺序错误:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ error: '更新API顺序失败', details: error.message });
  }
});

// 更新API
app.put('/api/apis/:uuid', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });
  const { uuid } = req.params;

  try {
    // 查找当前用户的指定API
    const api = await Api.findOne({
      where: {
        uuid: uuid,
        userId: req.user.uuid
      }
    });

    if (!api) {
      return res.status(404).json({ error: 'API不存在或无权访问' });
    }

    // 更新API，保留order字段
    await api.update({
      provider: req.body.provider,
      apiUrl: req.body.apiUrl,
      apiKey: req.body.apiKey,
      models: req.body.models,
      // 如果请求中包含order字段则更新，否则保持不变
      order: req.body.order !== undefined ? req.body.order : api.order
    });

    res.json(api);
  } catch (error) {
    console.error('更新API错误:', error);
    res.status(500).json({ error: '更新API失败' });
  }
});

// 新增：保存翻译内容路由
app.post('/api/content/saveTranslation', async (req, res) => {
  // 1. 检查认证
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  try {
    // 2. 提取和验证输入
    const { uuid, translated } = req.body; // 移除了 doi
    const userId = req.user.uuid;

    // 验证 translated 是否为数组
    if (!Array.isArray(translated)) {
      return res.status(400).json({ error: '请求参数无效 (translated 必须是数组)' });
    }
    // 验证 uuid 是否存在
    if (!uuid) {
      return res.status(400).json({ error: '请求参数无效 (需要 uuid)' });
    }

    // 3. 查找 Article 记录
    const articleRecord = await Article.findOne({ where: { uuid, userId } });

    if (!articleRecord) {
      return res.status(404).json({ error: '未找到指定的文章记录' });
    }

    // 4. 如果文章记录已存在，更新其 translated 字段
    await articleRecord.update({ translated: translated });
    return res.json({ success: true, message: '翻译已成功保存' });

  } catch (error) {
    console.error('保存翻译失败:', error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});

// 新增：保存原文内容路由
app.post('/api/content/saveOriginal', async (req, res) => {
  // 1. 检查认证
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  try {
    // 2. 提取和验证输入
    const { uuid, content: newContentArray } = req.body; // Renamed 'content' to 'newContentArray' for clarity, removed doi
    const userId = req.user.uuid;

    // 验证 newContentArray 是否为数组
    if (!Array.isArray(newContentArray)) {
      return res.status(400).json({ error: '请求参数无效 (content 必须是数组)' });
    }
    // 验证 uuid 是否存在
    if (!uuid) {
      return res.status(400).json({ error: '请求参数无效 (需要 uuid)' });
    }

    // 3. 查找 Article 记录
    const articleRecord = await Article.findOne({ where: { uuid, userId } });

    if (!articleRecord) {
      return res.status(404).json({ error: '未找到指定的文章记录' });
    }

    // 4. 更新 Article 记录的 content 字段
    await articleRecord.update({ content: newContentArray });

    // 5. 返回成功响应
    return res.json({ success: true, message: '原文已成功保存' });

  } catch (error) {
    console.error('保存原文失败:', error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});

// 新增：根据文章 UUID 获取编辑所需的原文和译文 (需要认证)
app.get('/api/article/:uuid/edit', async (req, res) => {
  // 1. 检查认证
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  const { uuid: articleUuid } = req.params;
  const userId = req.user.uuid;

  try {
    // 2. 根据文章 UUID 和用户 ID 查找 Article 记录
    const article = await Article.findOne({
      where: {
        uuid: articleUuid,
        userId: userId // 确保只有所有者能访问
      },
      attributes: ['uuid', 'content', 'translated', 'title', 'summary', 'refs', 'citation'] // 添加了 'refs' 和 'citation'
    });

    if (!article) {
      return res.status(404).json({ error: '未找到指定的文章记录或无权访问' });
    }

    // 3. 准备并返回数据 (原文、译文、UUIDs)
    const rawOriginalContentFromDB = Array.isArray(article.content) ? article.content : [];
    const dbTranslatedArray = Array.isArray(article.translated) ? article.translated : [];

    // Ensure original content items sent to client have an 'id' (using index).
    // This 'id' will be used by the client to key translations.
    let processedOriginalContent = rawOriginalContentFromDB.map((item, index) => {
        if (item === null || typeof item !== 'object') {
            console.warn(`[GET /api/article/${articleUuid}/edit] Original content item at index ${index} is null or not an object.`);
            return { id: index, tag: 'p', children: '', type: 'text' }; // Default placeholder
        }
        return { ...item, id: index };
    });

    // DEBUG Roo: Clean children from img tags in processedOriginalContent before sending to client
    processedOriginalContent = processedOriginalContent.map(item => {
      if (item && item.tag === 'img' && item.hasOwnProperty('children')) {
        console.log(`[DEBUG Roo - Server /edit] Removing "children" property from img item: ${JSON.stringify(item)}`);
        const newItem = { ...item };
        delete newItem.children;
        return newItem;
      }
      return item;
    });

    const finalResponseTranslated = [];
    processedOriginalContent.forEach((origItemWithId, index) => {
        const translatedPayload = dbTranslatedArray[index];

        if (translatedPayload !== null && translatedPayload !== undefined) {
            const structuredPayload = (typeof translatedPayload === 'object' && translatedPayload !== null)
                                      ? translatedPayload
                                      : { type: 'text', children: String(translatedPayload) };

            finalResponseTranslated.push({
                id: origItemWithId.id,
                ...structuredPayload
            });
        }
    });

    res.json({
      content: processedOriginalContent,
      translated: finalResponseTranslated,
      // doi: article.doi, // 移除了 doi
      articleUuid: article.uuid,      // 文章 UUID
      title: article.title,
      summary: article.summary,
      refs: article.refs,             // 添加refs字段
      citation: article.citation      // 添加citation字段
    });

  } catch (error) {
    console.error(`[GET /api/article/${articleUuid}/edit] 处理用户 ${userId} 的请求时出错:`, error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});

// 新增：更新文章（标题和/或翻译内容）
app.put('/api/articles/:articleUuid', async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  const { articleUuid } = req.params;
  const { title, summary, translated, content, refs, citation } = req.body; // 支持所有可更新字段
  const userId = req.user.uuid;

  // 检查是否至少提供了一个可更新的字段
  if (title === undefined && summary === undefined && translated === undefined &&
      content === undefined && refs === undefined && citation === undefined) {
    return res.status(400).json({ error: '请求体中缺少可更新的字段 (title、summary、translated、content、refs 或 citation)' });
  }

  try {
    const article = await Article.findOne({
      where: {
        uuid: articleUuid,
        userId: userId
      }
    });

    if (!article) {
      return res.status(404).json({ error: '未找到指定的文章记录或无权访问' });
    }

    const updates = {};
    if (title !== undefined) {
      updates.title = title;
    }
    if (summary !== undefined) {
      updates.summary = summary;
    }
    if (translated !== undefined) {
      if (!Array.isArray(translated)) {
        return res.status(400).json({ error: '如果提供 translated，则必须是数组' });
      }
      updates.translated = translated;
    }
    if (content !== undefined) {
      if (!Array.isArray(content)) {
        return res.status(400).json({ error: '如果提供 content，则必须是数组' });
      }
      updates.content = content;
    }
    if (refs !== undefined) {
      updates.refs = refs;
    }
    if (citation !== undefined) {
      updates.citation = citation;
    }

    await article.update(updates);

    // Return the updated article, including all its fields
    const updatedArticle = await Article.findOne({
        where: { uuid: articleUuid, userId: userId }
    });

    res.json(updatedArticle);

  } catch (error) {
    console.error(`[PUT /api/articles/${articleUuid}] 处理用户 ${userId} 的请求时出错:`, error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});

// 新增：保存文章参考和来源
app.put('/api/article/:uuid/references', async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  const { uuid: articleUuid } = req.params;
  const { references, sources } = req.body;
  const userId = req.user.uuid;

  try {
    const article = await Article.findOne({
      where: {
        uuid: articleUuid,
        userId: userId
      }
    });

    if (!article) {
      return res.status(404).json({ error: '未找到指定的文章记录或无权访问' });
    }

    const updates = {};
    if (references !== undefined) {
      updates.references = references;
    }
    if (sources !== undefined) {
      updates.sources = sources;
    }

    await article.update(updates);

    res.json({
      success: true,
      references: article.references,
      sources: article.sources
    });
  } catch (error) {
    console.error(`[PUT /api/article/${articleUuid}/references] 处理用户 ${userId} 的请求时出错:`, error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});

// 新增：保存文章参考文献
app.put('/api/article/:uuid/refs', async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  const { uuid: articleUuid } = req.params;
  const { refs } = req.body;
  const userId = req.user.uuid;

  try {
    const article = await Article.findOne({
      where: {
        uuid: articleUuid,
        userId: userId
      }
    });

    if (!article) {
      return res.status(404).json({ error: '未找到指定的文章记录或无权访问' });
    }

    await article.update({ refs: refs || null });

    res.json({
      success: true,
      refs: article.refs
    });
  } catch (error) {
    console.error(`[PUT /api/article/${articleUuid}/refs] 处理用户 ${userId} 的请求时出错:`, error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});

// 新增：保存文章来源
app.put('/api/article/:uuid/citation', async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  const { uuid: articleUuid } = req.params;
  const { citation } = req.body;
  const userId = req.user.uuid;

  try {
    const article = await Article.findOne({
      where: {
        uuid: articleUuid,
        userId: userId
      }
    });

    if (!article) {
      return res.status(404).json({ error: '未找到指定的文章记录或无权访问' });
    }

    await article.update({ citation: citation || null });

    res.json({
      success: true,
      citation: article.citation
    });
  } catch (error) {
    console.error(`[PUT /api/article/${articleUuid}/citation] 处理用户 ${userId} 的请求时出错:`, error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});

// 新增：更新文章标题路由
app.put('/api/article/:uuid/title', async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  const { uuid: articleUuid } = req.params;
  const { title } = req.body;
  const userId = req.user.uuid;

  if (title === undefined) {
    return res.status(400).json({ error: '请求体中缺少 title 字段' });
  }

  try {
    const article = await Article.findOne({
      where: {
        uuid: articleUuid,
        userId: userId
      }
    });

    if (!article) {
      return res.status(404).json({ error: '未找到指定的文章记录或无权访问' });
    }

    // 如果传入的 title 为空字符串，并且数据库中的 title 也为空或 null，则尝试从译文获取
    let titleToSave = title;
    if (title === '' && (!article.title || article.title === '')) {
      const translated = article.translated || [];
      if (translated.length > 0 && translated[0] && typeof translated[0] === 'string') {
        titleToSave = translated[0].replace(/<[^>]+>/g, '').trim(); // 移除 HTML 标签并取首段
      } else if (translated.length > 0 && translated[0] && typeof translated[0] === 'object' && translated[0].type === 'text' && translated[0].children) {
        titleToSave = translated[0].children.trim();
      }
      // 如果从译文获取后仍然为空，则保持为空字符串
      if (!titleToSave) {
        titleToSave = '';
      }
    }

    await article.update({ title: titleToSave });
    res.json({ success: true, title: titleToSave });

  } catch (error) {
    console.error(`[PUT /api/article/${articleUuid}/title] 处理用户 ${userId} 的请求时出错:`, error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});


// 新增：根据文章 UUID 获取公开的翻译预览
// 注意：此路由不进行用户认证，仅返回公开信息
app.get('/api/article/:uuid', async (req, res) => {
  const { uuid: articleUuid } = req.params; // 从 URL 参数获取文章 UUID

  // 验证 UUID 格式 (可选但推荐) - 保持验证
  // const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  // if (!uuidRegex.test(articleUuid)) {
  //   return res.status(400).json({ error: '无效的文章 UUID 格式' });
  // }

  try {
    // 1. 根据文章 UUID 查找 Article 记录
    // 只获取公开需要的字段：translated、title、createdAt、summary、refs、citation
    const article = await Article.findOne({
      where: { uuid: articleUuid },
      attributes: ['uuid', 'translated', 'title', 'createdAt', 'summary', 'refs', 'citation'] // 添加了 summary、refs、citation
    });

    if (!article) {
      console.log(`[GET /api/article/${articleUuid}] 未找到文章记录`);
      return res.status(404).json({ error: '未找到指定的文章记录' });
    }

    console.log(`[GET /api/article/${articleUuid}] 找到文章记录`); // 移除了 DOI log

    // 2. 准备并返回数据 (翻译、标题、总结、参考、来源)
    const translatedData = Array.isArray(article.translated) ? article.translated : [];

    res.json({
      translated: translatedData,
      // doi: article.doi, // 移除了 doi
      articleUuid: article.uuid,
      title: article.title,
      createdAt: article.createdAt,
      summary: article.summary,
      refs: article.refs,
      citation: article.citation
    });

  } catch (error) {
    console.error(`[GET /api/article/${articleUuid}] 处理请求时出错:`, error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});


// 新增：删除区块路由
app.post('/api/content/deleteChunk', async (req, res) => {
  // 1. 检查认证
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  try {
    // 2. 提取和验证输入
    const { uuid, itemIds } = req.body; // 移除了 doi
    const userId = req.user.uuid;

    if (!Array.isArray(itemIds) || itemIds.length === 0) {
      return res.status(400).json({ error: '请求参数无效 (itemIds 必须是非空数组)' });
    }
    if (!uuid) { // 移除了 doi 的检查
      return res.status(400).json({ error: '请求参数无效 (需要 uuid)' });
    }

    // 3. 查找 Article 记录
    const articleRecord = await Article.findOne({ where: { uuid, userId } });

    if (!articleRecord) {
      return res.status(404).json({ error: '未找到指定的文章记录' });
    }

    // 4. 更新 Article 记录的 content 和 translated 字段 - 标记为 null
    let contentUpdated = false;
    let translationUpdated = false;

    // 更新 Article.content
    const currentContent = articleRecord.content || [];
    const newContent = [...currentContent]; // 创建副本
    itemIds.forEach(id => {
      if (id >= 0 && id < newContent.length && newContent[id] !== null) {
        newContent[id] = null; // 标记为 null
        contentUpdated = true;
      }
    });

    // 更新 Article.translated
    const currentTranslated = articleRecord.translated || [];
    const newTranslated = [...currentTranslated]; // 创建副本
    itemIds.forEach(id => {
      if (id >= 0 && id < newTranslated.length && newTranslated[id] !== null) {
        newTranslated[id] = null; // 标记为 null
        translationUpdated = true;
      }
    });

    // 5. 保存更改到数据库
    const updatesToApply = {};
    if (contentUpdated) {
      updatesToApply.content = newContent;
    }
    if (translationUpdated) {
      updatesToApply.translated = newTranslated;
    }

    if (Object.keys(updatesToApply).length > 0) {
      await articleRecord.update(updatesToApply);
    }

    // 6. 返回成功响应
    return res.json({ success: true, message: '区块已成功标记为删除' });

  } catch (error) {
    console.error('[deleteChunk] 删除区块失败:', error);
    res.status(500).json({ error: '服务器错误', message: error.message });
  }
});


// 删除API
app.delete('/api/apis/:uuid', async (req, res) => {
  if (!req.user) return res.status(401).json({ error: '未授权' });
  const { uuid } = req.params;

  try {

    // 查找当前用户的指定API
    const api = await Api.findOne({
      where: {
        uuid: uuid,
        userId: req.user.uuid
      }
    });

    if (!api) {
      return res.status(404).json({ error: 'API不存在或无权访问' });
    }

    // 删除API
    await api.destroy();

    res.json({ success: true });
  } catch (error) {
    console.error('删除API错误:', error);
    res.status(500).json({ error: '删除API失败' });
  }
});

// GET /api/file/:fileId - Serves a file from the Files table
app.get('/api/file/:fileId', async (req, res) => {
  try {
    const { fileId } = req.params;
    if (!fileId) {
      return res.status(400).json({ success: false, error: 'File ID is required.' });
    }

    // It's good practice to validate fileId format if it's expected to be a UUID, for example.
    // For now, we assume it's a valid primary key.

    let currentFileRecord = await File.findByPk(fileId);

    if (!currentFileRecord) {
      return res.status(404).json({ success: false, error: 'File record not found.' });
    }

    // const firstMimeType = currentFileRecord.mimeType || 'application/octet-stream'; // mimeType removed from model
    let assembledBase64Payload = "";
    let dataHeader = "";

    // Loop to get all chunks
    while (currentFileRecord) {
      const currentDataUri = currentFileRecord.dataUri;
      const currentMimeTypeMatch = currentDataUri.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);

      if (!currentMimeTypeMatch || currentMimeTypeMatch.length < 1) {
        console.error(`[API File] Invalid Data URI format in chunk ${currentFileRecord.id} for original fileId ${fileId}`);
        return res.status(500).json({ success: false, error: 'Invalid chunk data format.' });
      }

      if (!dataHeader) { // Capture header from the first valid chunk
          dataHeader = currentMimeTypeMatch[0];
      }

      assembledBase64Payload += currentDataUri.substring(currentMimeTypeMatch[0].length);

      if (currentFileRecord.nextChunkId) {
        currentFileRecord = await File.findByPk(currentFileRecord.nextChunkId);
        if (!currentFileRecord) {
          console.error(`[API File] Broken chain: Chunk ${currentFileRecord?.id} pointed to non-existent nextChunkId ${currentFileRecord?.nextChunkId} for original fileId ${fileId}`);
          return res.status(500).json({ success: false, error: 'File chunk chain is broken.' });
        }
      } else {
        currentFileRecord = null; // End of chain
      }
    }

    if (!dataHeader) { // Should have been set if at least one chunk was processed
        console.error(`[API File] Could not determine Data URI header for fileId ${fileId}`);
        return res.status(500).json({ success: false, error: 'Could not reconstruct file header.' });
    }

    const finalDataUri = dataHeader + assembledBase64Payload;

    // Parse MIME type from the fully assembled Data URI
    const finalMimeTypeMatch = finalDataUri.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);
    const actualMimeType = (finalMimeTypeMatch && finalMimeTypeMatch[1]) ? finalMimeTypeMatch[1] : 'application/octet-stream';

    const originalFileBuffer = Buffer.from(assembledBase64Payload, 'base64'); // Decode only the assembled base64 payload

    // 对图片进行压缩处理（默认最大宽度800px）
    const fileBuffer = await compressImageIfNeeded(originalFileBuffer, actualMimeType, 800);

    // 如果是图片且进行了压缩，更新Content-Type为JPEG
    const finalMimeType = (actualMimeType.startsWith('image/') && fileBuffer !== originalFileBuffer)
      ? 'image/jpeg'
      : actualMimeType;

    res.writeHead(200, {
      'Content-Type': finalMimeType,
      'Content-Length': fileBuffer.length
    });
    res.end(fileBuffer);

  } catch (error) {
    console.error(`[API File] Error serving file ${req.params.fileId}:`, error);
    // Avoid sending detailed error messages to client in production for security
    res.status(500).json({ success: false, error: 'An error occurred while serving the file.' });
  }
});

// GET /api/file/:fileId/large - Serves the original file without compression
app.get('/api/file/:fileId/large', async (req, res) => {
  try {
    const { fileId } = req.params;
    if (!fileId) {
      return res.status(400).json({ success: false, error: 'File ID is required.' });
    }

    let currentFileRecord = await File.findByPk(fileId);

    if (!currentFileRecord) {
      return res.status(404).json({ success: false, error: 'File record not found.' });
    }

    let assembledBase64Payload = "";
    let dataHeader = "";

    // Loop to get all chunks
    while (currentFileRecord) {
      const currentDataUri = currentFileRecord.dataUri;
      const currentMimeTypeMatch = currentDataUri.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);

      if (!currentMimeTypeMatch || currentMimeTypeMatch.length < 1) {
        console.error(`[API File Large] Invalid Data URI format in chunk ${currentFileRecord.id} for original fileId ${fileId}`);
        return res.status(500).json({ success: false, error: 'Invalid chunk data format.' });
      }

      if (!dataHeader) { // Capture header from the first valid chunk
          dataHeader = currentMimeTypeMatch[0];
      }

      assembledBase64Payload += currentDataUri.substring(currentMimeTypeMatch[0].length);

      if (currentFileRecord.nextChunkId) {
        currentFileRecord = await File.findByPk(currentFileRecord.nextChunkId);
        if (!currentFileRecord) {
          console.error(`[API File Large] Broken chain: Chunk ${currentFileRecord?.id} pointed to non-existent nextChunkId ${currentFileRecord?.nextChunkId} for original fileId ${fileId}`);
          return res.status(500).json({ success: false, error: 'File chunk chain is broken.' });
        }
      } else {
        currentFileRecord = null; // End of chain
      }
    }

    if (!dataHeader) { // Should have been set if at least one chunk was processed
        console.error(`[API File Large] Could not determine Data URI header for fileId ${fileId}`);
        return res.status(500).json({ success: false, error: 'Could not reconstruct file header.' });
    }

    const finalDataUri = dataHeader + assembledBase64Payload;

    // Parse MIME type from the fully assembled Data URI
    const finalMimeTypeMatch = finalDataUri.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);
    const actualMimeType = (finalMimeTypeMatch && finalMimeTypeMatch[1]) ? finalMimeTypeMatch[1] : 'application/octet-stream';

    const fileBuffer = Buffer.from(assembledBase64Payload, 'base64'); // Decode only the assembled base64 payload - NO COMPRESSION

    res.writeHead(200, {
      'Content-Type': actualMimeType, // Use original MIME type
      'Content-Length': fileBuffer.length
    });
    res.end(fileBuffer);

  } catch (error) {
    console.error(`[API File Large] Error serving large file ${req.params.fileId}:`, error);
    // Avoid sending detailed error messages to client in production for security
    res.status(500).json({ success: false, error: 'An error occurred while serving the file.' });
  }
});

// --- BEGIN: Server-side rendering/data injection for preview page ---
app.get('/article/:uuid', async (req, res, next) => {
  const { uuid: articleUuid } = req.params;

  try {
    // 1. Fetch article data
    const article = await Article.findOne({
      where: { uuid: articleUuid },
      attributes: ['uuid', 'translated', 'title', 'createdAt', 'summary', 'refs', 'citation'] // 添加了 summary、refs、citation
    });

    let preloadedData = null;
    if (article) {
      const translatedData = Array.isArray(article.translated) ? article.translated : [];
      preloadedData = {
        translated: translatedData,
        // doi: article.doi, // 移除了 doi
        articleUuid: article.uuid,
        title: article.title,
        createdAt: article.createdAt,
        summary: article.summary,
        refs: article.refs,
        citation: article.citation
      };
    }

    // 2. Read the main HTML file
    // Determine path based on environment
    const indexPath = process.env.NODE_ENV === 'production'
      ? join(__dirname, 'dist', 'index.html')
      : join(__dirname, 'index.html'); // Assuming index.html is in root for dev

    fs.readFile(indexPath, 'utf8', (err, htmlData) => {
      if (err) {
        console.error('Error reading index.html:', err);
        return res.status(500).send('Error loading page');
      }

      // 3. Inject data into the HTML
      // Use JSON.stringify for safe embedding. Handle potential XSS if data contains user input.
      const injectedHtml = htmlData.replace(
        '</body>',
        `<script>window.__PRELOADED_STATE__ = ${JSON.stringify(preloadedData)}</script></body>`
      );

      // 4. Send the modified HTML
      res.setHeader('Content-Type', 'text/html');
      res.send(injectedHtml);
    });

  } catch (error) {
    console.error(`[SSR /article/${articleUuid}] Error fetching data:`, error);
    // Fallback to standard client-side rendering or show an error page
    // For simplicity, we'll call next() to let other handlers (like static serving) try
    next(error); // Pass error to global error handler or let static serve index.html
  }
});
// --- END: Server-side rendering/data injection ---


// 在生产环境中提供静态文件
// This needs to come AFTER the specific /article/:uuid handler
if (process.env.NODE_ENV === 'production') {
  const distPath = join(__dirname, 'dist');

  // 检查 dist 目录是否存在
  if (fs.existsSync(distPath)) {
    app.use(express.static(distPath));

    // 所有未匹配的路由都返回 index.html
    app.get('*', (_, res) => {
      res.sendFile(join(distPath, 'index.html'));
    });
  }
}

// 临时存储分块数据
const chunkStorage = {};

// 1. 端点：/api/import-pdf/chunked (HTTP POST)
app.post('/api/import-pdf/chunked', authenticateJWT, async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ success: false, error: '未授权' });
  }

  const { uploadSessionId, chunkIndex, totalChunks, fileName, data } = req.body;

  // 验证必要字段
  if (!uploadSessionId || typeof chunkIndex !== 'number' || typeof totalChunks !== 'number' || !fileName || data === undefined) {
    return res.status(400).json({ success: false, error: '请求参数无效，缺少必要的字段或字段类型不正确。' });
  }

  if (chunkIndex < 0 || totalChunks <= 0 || chunkIndex >= totalChunks) {
    return res.status(400).json({ success: false, error: 'chunkIndex 或 totalChunks 参数值无效。' });
  }

  try {
    if (!chunkStorage[uploadSessionId]) {
      chunkStorage[uploadSessionId] = {
        chunks: {},
        expectedChunks: totalChunks,
        fileName: fileName,
        receivedChunksCount: 0,
        userId: req.user.uuid // 存储userId以便在finalize时验证
      };
    }

    const session = chunkStorage[uploadSessionId];

    // 验证后续块的 totalChunks 和 fileName 是否与第一个块一致
    if (session.expectedChunks !== totalChunks || session.fileName !== fileName) {
      return res.status(400).json({ success: false, error: 'totalChunks 或 fileName 与会话初始值不匹配。' });
    }

    // 检查用户是否匹配
    if (session.userId !== req.user.uuid) {
        return res.status(403).json({ success: false, error: '无权访问此上传会话。' });
    }

    if (!session.chunks[chunkIndex]) {
      session.chunks[chunkIndex] = data;
      session.receivedChunksCount++;
    } else {
      // 如果块已存在，可以选择覆盖或返回错误，这里简单返回消息
      return res.status(200).json({
        success: true,
        message: `Chunk ${chunkIndex + 1}/${totalChunks} for session ${uploadSessionId} already received. No update performed.`
      });
    }

    res.status(200).json({
      success: true,
      message: `Chunk ${chunkIndex + 1}/${totalChunks} received for session ${uploadSessionId}.`
    });

  } catch (error) {
    console.error(`[Chunk Upload] Error processing chunk for session ${uploadSessionId}:`, error);
    res.status(500).json({ success: false, error: '处理数据块失败。', details: error.message });
  }
});

// 后台处理PDF导入的函数
async function processPdfImportInBackground(taskId, uploadSessionId, originalFileName, totalExpectedChunks, user, referencesText) { // Added referencesText
  const task = importTasks[taskId];
  if (!task) {
    console.error(`[Background Import] Task ${taskId} not found.`);
    return;
  }

  try {
    task.status = 'processing';
    task.progressMessage = '正在检索上传会话...';

    const session = chunkStorage[uploadSessionId];
    if (!session) {
      throw new Error(`上传会话 ${uploadSessionId} 未找到或已过期。`);
    }
    if (session.userId !== user.uuid) {
      throw new Error('无权操作此上传会话。');
    }
    if (session.fileName !== originalFileName) {
      throw new Error('提供的 fileName 与会话中存储的不匹配。');
    }
    if (session.expectedChunks !== totalExpectedChunks || session.receivedChunksCount !== totalExpectedChunks) {
      throw new Error(`数据块不完整或数量不匹配。Expected: ${session.expectedChunks}, Received: ${session.receivedChunksCount}, Claimed by client: ${totalExpectedChunks}`);
    }

    task.progressMessage = '正在合并数据块...';
    const allEnhancedContentJson = [];
    for (let i = 0; i < session.expectedChunks; i++) {
      if (session.chunks[i] === undefined) {
        throw new Error(`合并数据块失败：缺少块 ${i}。`);
      }
      if (Array.isArray(session.chunks[i])) {
        allEnhancedContentJson.push(...session.chunks[i]);
      } else {
        allEnhancedContentJson.push(session.chunks[i]);
      }
    }

    // DEBUG Roo: Log structure of image items in allEnhancedContentJson (merged content)
    if (allEnhancedContentJson && Array.isArray(allEnhancedContentJson)) {
      const imagesInMergedContent = allEnhancedContentJson.filter(item => item.tag === 'img');
      console.log(`[DEBUG Roo - Server] Images in merged allEnhancedContentJson (task ${taskId}):`, JSON.stringify(imagesInMergedContent.map(img => ({ srcLength: img.src?.length, alt: img.alt, children: img.children, width: img.width, height: img.height }))));
    }

    task.progressMessage = '正在创建文章记录...';
    const articleUuid = uuidv4();
    const originalFilenameToUse = session.fileName || `pdf-import-${Date.now()}.pdf`;
    let initialTitle = `导入自 ${originalFilenameToUse}`;

    if (allEnhancedContentJson && allEnhancedContentJson.length > 0) {
        const firstH1 = allEnhancedContentJson.find(item => item && item.tag === 'h1');
        if (firstH1 && firstH1.children) {
            initialTitle = String(firstH1.children).substring(0, 250).trim();
        } else if (allEnhancedContentJson[0] && allEnhancedContentJson[0].children) {
            const firstChildContent = allEnhancedContentJson[0].children;
            if (firstChildContent !== null && firstChildContent !== undefined) {
                 initialTitle = String(firstChildContent).substring(0, 100).trim();
            }
        }
    }

    // Process images: store Data URIs in Files table and replace src with file IDs
    task.progressMessage = '正在处理内容中的图片引用...';
    const processedContentJson = []; // Create a new array for processed content
    for (const [index, item] of allEnhancedContentJson.entries()) {
      let processedItem = { ...item }; // Clone item to avoid modifying the original array directly during iteration (optional, but safer)

      if (item.tag === 'img' && item.src && typeof item.src === 'string' && item.src.startsWith('data:')) {
        try {
          const fullDataUri = item.src;
          const base64Data = fullDataUri.substring(fullDataUri.indexOf(',') + 1);
          const md5Hash = crypto.createHash('md5').update(base64Data).digest('hex');

          const existingFile = await File.findOne({ where: { md5: md5Hash } });

          if (existingFile) {
            processedItem.src = `/api/file/${existingFile.id}`;
          } else {
            // Continue with existing file creation logic, but add md5
            const fullDataUriLength = Buffer.byteLength(fullDataUri, 'utf8'); // Get byte length

            const mimeTypeMatch = fullDataUri.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);
            const mimeType = mimeTypeMatch ? mimeTypeMatch[1] : 'application/octet-stream';
            const dataHeader = mimeTypeMatch ? mimeTypeMatch[0] : `data:${mimeType};base64,`; // e.g., "data:image/png;base64,"
            const base64Payload = fullDataUri.substring(dataHeader.length);

            let firstChunkId;

            if (fullDataUriLength <= MAX_DATA_URI_CHUNK_SIZE) {
              // Store as a single record
              console.log(`[Background Import] Attempting to store single file. Data URI length: ${fullDataUri.length}, MD5: ${md5Hash}`);
              const fileRecord = await File.create({
                id: uuidv4(), // Explicitly generate ID
                dataUri: fullDataUri,
                md5: md5Hash, // Add MD5 hash
                nextChunkId: null,
              });
              firstChunkId = fileRecord.id;
              console.log(`[Background Import] Stored single file. Record ID: ${firstChunkId}`);
            } else {
              // Chunk the Data URI
              console.log(`[Background Import] Data URI length ${fullDataUri.length} exceeds MAX_DATA_URI_CHUNK_SIZE ${MAX_DATA_URI_CHUNK_SIZE}. Chunking...`);
              const base64HeaderLength = Buffer.byteLength(dataHeader, 'utf8');
              const maxBase64PayloadPerChunk = MAX_DATA_URI_CHUNK_SIZE - base64HeaderLength;

              if (maxBase64PayloadPerChunk <= 0) {
                console.error(`[Background Import] MAX_DATA_URI_CHUNK_SIZE (${MAX_DATA_URI_CHUNK_SIZE}) is too small for Data URI header (${base64HeaderLength}).`);
                throw new Error('MAX_DATA_URI_CHUNK_SIZE is too small to fit even the Data URI header.');
              }

              const base64Segments = [];
              for (let i = 0; i < base64Payload.length; i += maxBase64PayloadPerChunk) {
                base64Segments.push(base64Payload.substring(i, i + maxBase64PayloadPerChunk));
              }
              console.log(`[Background Import] Data URI split into ${base64Segments.length} segments.`);

              const chunkFileRecordsData = [];
              const chunkIds = base64Segments.map(() => uuidv4()); // Pre-generate all chunk IDs
              firstChunkId = chunkIds[0];

              for (let i = 0; i < base64Segments.length; i++) {
                const currentChunkDataUri = dataHeader + base64Segments[i];
                console.log(`[Background Import] Preparing chunk ${i + 1}/${base64Segments.length}. Chunk Data URI length: ${currentChunkDataUri.length}`);
                chunkFileRecordsData.push({
                  id: chunkIds[i],
                  dataUri: currentChunkDataUri,
                  md5: (i === 0) ? md5Hash : null, // Store MD5 only with the first chunk
                  nextChunkId: (i < chunkIds.length - 1) ? chunkIds[i+1] : null,
                });
              }
              console.log(`[Background Import] Attempting File.bulkCreate for ${chunkFileRecordsData.length} chunks.`);
              await File.bulkCreate(chunkFileRecordsData);
              console.log(`[Background Import] File.bulkCreate successful for ${chunkFileRecordsData.length} chunks. First chunk ID: ${firstChunkId}`);
            }

            processedItem.src = `/api/file/${firstChunkId}`; // Point to the first chunk
          }
        } catch (fileProcessingError) {
          const originalSrcSnippet = item.src && typeof item.src === 'string' ? item.src.substring(0, 100) + '...' : 'N/A';
          console.error(`[Background Import] Error processing/chunking image for article ${articleUuid} (item ${index + 1}). Original src (snippet): ${originalSrcSnippet}. Error Details:`, fileProcessingError, fileProcessingError.stack);
        }
      }
      if (processedItem.tag === 'img') {
        delete processedItem.alt; // Remove alt attribute from images
        delete processedItem.width; // Remove width attribute
        delete processedItem.height; // Remove height attribute
      }
      processedContentJson.push(processedItem);
    }

    const finalContentString = JSON.stringify(processedContentJson);
    console.log(`[Background Import] Length of final processedContentJson string for Article.content: ${finalContentString.length}`);
    const imageItemsInProcessed = processedContentJson.filter(pItem => pItem.tag === 'img'); // Log ALL images before saving
    if (imageItemsInProcessed.length > 0) {
      // DEBUG Roo: Log structure of image items in processedContentJson before saving to DB
      console.log(`[DEBUG Roo - Server] Images in processedContentJson before DB save (task ${taskId}):`, JSON.stringify(imageItemsInProcessed.map(img => ({ src: img.src, alt: img.alt, children: img.children, width: img.width, height: img.height }))));
    } else {
      console.log(`[DEBUG Roo - Server] No image items in processedContentJson before DB save (task ${taskId}).`);
    }

    console.log(`[Background Import] Attempting to create Article record for UUID: ${articleUuid} with processed content and references.`);
    let articleRecord = await Article.create({
      uuid: articleUuid,
      userId: user.uuid,
      content: processedContentJson, // Use content with image references
      translated: [],
      title: initialTitle,
      refs: referencesText // Save referencesText to refs field
    });
    console.log(`[Background Import] Article.create successful for UUID: ${articleUuid}. Refs length: ${referencesText ? referencesText.length : 'null'}`);

    if (articleRecord && articleRecord.uuid) {
      const reReadArticle = await Article.findOne({ where: { uuid: articleRecord.uuid } });
      if (reReadArticle && reReadArticle.content) {
        // Verification that article was saved correctly
        reReadArticle.content.filter(item => item.tag === 'img').slice(0, 3);
      }
    }

    task.progressMessage = '正在尝试AI生成标题...';
    let aiTitleGeneratedSuccessfully = false;
    try {
      let textForAiTitle = allEnhancedContentJson
        .filter(item => item && typeof item.children === 'string')
        .map(item => item.children)
        .join(' ')
        .substring(0, 1000);

      if (textForAiTitle.trim()) {
        const titleSystemPrompt = `请根据以下文本内容，为其生成一个简洁、准确、地道的中文标题（不超过25个字）：\n\n${textForAiTitle}`;
        const aiPayload = {
          model: defaultApiModel,
          messages: [
            { role: 'system', content: "你是一个专业的标题生成助手，擅长从文本中提炼核心内容并生成简洁的中文标题。" },
            { role: 'user', content: titleSystemPrompt }
          ],
          temperature: 0.5,
          stream: false
        };

        if (defaultApiUrl && defaultApiKey) {
            const aiResponse = await fetch(defaultApiUrl, { // This fetch might be the one timing out
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${defaultApiKey}`,
              },
              body: JSON.stringify(aiPayload),
            });

            if (aiResponse.ok) {
              const aiData = await aiResponse.json();
              const generatedTitle = aiData.choices?.[0]?.message?.content?.trim();
              if (generatedTitle) {
                await articleRecord.update({ title: generatedTitle });
                initialTitle = generatedTitle; // Update initialTitle to reflect the new AI title
                aiTitleGeneratedSuccessfully = true;
              } else {
              }
            } else {
                const errorText = await aiResponse.text();
                console.warn(`[Background Import AI Title Gen] AI API request failed with status ${aiResponse.status} for article ${articleUuid}. Response: ${errorText.substring(0, 200)}`);
            }
        } else {
            console.warn(`[Background Import AI Title Gen] defaultApiUrl or defaultApiKey is not configured. Skipping AI title generation for article ${articleUuid}.`);
        }
      } else {
      }
    } catch (aiError) {
      console.error(`[Background Import AI Title Gen] Error during AI title generation for article ${articleUuid}:`, aiError.message, aiError.stack);
    }

    if (aiTitleGeneratedSuccessfully) {
        task.progressMessage = '文章内容已保存，AI生成标题成功。';
    } else {
        task.progressMessage = '文章内容已保存，AI生成标题失败或跳过，使用默认标题。';
    }

    delete chunkStorage[uploadSessionId]; // 清理临时块存储

    task.status = 'completed';
    task.progressMessage = '导入成功完成！';
    task.result = {
      newArticleUuid: articleRecord.uuid,
      newArticleTitle: initialTitle
    };
    console.log(`[Background Import] Task ${taskId} completed successfully for article ${articleRecord.uuid}`);

  } catch (error) {
    console.error(`[Background Import] Error processing task ${taskId} for session ${uploadSessionId}:`, error, error.stack); // Added error.stack
    task.status = 'failed';
    task.progressMessage = '导入处理失败。';
    task.error = error.message || '未知错误';
    if (uploadSessionId && chunkStorage[uploadSessionId]) {
        delete chunkStorage[uploadSessionId];
    }
  }
}


// 2. 端点：/api/import-pdf/finalize-upload (HTTP POST)
app.post('/api/import-pdf/finalize-upload', authenticateJWT, (req, res) => { // Removed async
  if (!req.user) {
    return res.status(401).json({ success: false, error: '未授权' });
  }

  const { uploadSessionId, fileName, totalExpectedChunks, referencesText } = req.body; // Added referencesText

  if (!uploadSessionId || !fileName || typeof totalExpectedChunks !== 'number' || totalExpectedChunks <= 0) {
    return res.status(400).json({ success: false, error: '请求参数无效，缺少 uploadSessionId, fileName 或 totalExpectedChunks。' });
  }
// 3. 端点：/api/import-pdf/task-status/:taskId (HTTP GET)
app.get('/api/import-pdf/task-status/:taskId', authenticateJWT, (req, res) => {
  if (!req.user) {
    return res.status(401).json({ success: false, error: '未授权' });
  }

  const { taskId } = req.params;
  const task = importTasks[taskId];

  if (!task) {
    return res.status(404).json({ success: false, error: '任务未找到。' });
  }

  // 确保只有任务的创建者可以查询其状态
  if (task.userId !== req.user.uuid) {
    return res.status(403).json({ success: false, error: '无权访问此任务的状态。' });
  }

  if (task.status === 'completed') {
    // 任务完成后，可以考虑从 importTasks 中移除，或者设置一个过期时间
    // delete importTasks[taskId]; // 如果决定在获取成功状态后立即清除
    res.status(200).json({
      success: true,
      status: task.status,
      message: task.progressMessage,
      result: task.result
    });
  } else if (task.status === 'failed') {
    // delete importTasks[taskId]; // 如果决定在获取失败状态后立即清除
    res.status(200).json({ // 返回200，但success为false，由客户端判断错误
      success: false, // 指示操作本身可能成功（API调用成功），但业务逻辑失败
      status: task.status,
      message: task.progressMessage,
      error: task.error
    });
  } else { // 'pending' or 'processing'
    res.status(200).json({
      success: true,
      status: task.status,
      message: task.progressMessage
    });
  }
});

  const session = chunkStorage[uploadSessionId];

  if (!session) {
    return res.status(404).json({ success: false, error: `上传会话 ${uploadSessionId} 未找到或已过期。` });
  }

  if (session.userId !== req.user.uuid) {
    return res.status(403).json({ success: false, error: '无权操作此上传会话。' });
  }

  if (session.fileName !== fileName) {
    return res.status(400).json({ success: false, error: '提供的 fileName 与会话中存储的不匹配。' });
  }

  if (session.expectedChunks !== totalExpectedChunks || session.receivedChunksCount !== totalExpectedChunks) {
    console.warn(`[Finalize Upload] Chunk mismatch for session ${uploadSessionId}. Expected: ${session.expectedChunks}, Received in session: ${session.receivedChunksCount}, Received in request: ${totalExpectedChunks}`);
    return res.status(400).json({
      success: false,
      error: '数据块不完整或数量不匹配。',
      details: `Expected: ${session.expectedChunks}, Received: ${session.receivedChunksCount}, Claimed by client: ${totalExpectedChunks}`
    });
  }

  const taskId = uuidv4();
  importTasks[taskId] = {
    status: 'pending',
    progressMessage: '任务已创建，等待处理...',
    userId: req.user.uuid,
    data: { uploadSessionId, fileName, totalExpectedChunks, referencesText }, // Added referencesText
    error: null,
    result: null,
    createdAt: Date.now() // 添加创建时间戳，方便后续可能的清理旧任务
  };

  // 启动后台处理，不等待其完成
  processPdfImportInBackground(taskId, uploadSessionId, fileName, totalExpectedChunks, req.user, referencesText); // Pass referencesText

  res.status(202).json({
    success: true,
    taskId: taskId,
    message: 'PDF导入请求已接受，正在后台处理。'
  });
});


// 新增：从PDF导入文章路由 (这是旧的，保留作为参考或未来可能的单文件上传)
// 不再使用 multer 处理此路由的文件上传，因为文件解析和AI调用移至前端
// app.post('/api/import-pdf', authenticateJWT, upload.single('pdfFile'), async (req, res) => {
app.post('/api/import-pdf', authenticateJWT, async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: '未授权' });
  }

  const { fileName, parsedContent } = req.body;

  if (!parsedContent || !Array.isArray(parsedContent) || parsedContent.length === 0) {
    return res.status(400).json({ error: '缺少或无效的 parsedContent 数据' });
  }
  // 可选：更严格地验证 parsedContent 中每个对象的结构
  for (const item of parsedContent) {
    if (!item || typeof item !== 'object' || !item.tag ||
        (item.tag !== 'img' && (typeof item.children !== 'string' && item.children !== null)) ||
        (item.tag === 'img' && typeof item.src !== 'string')) {
      console.warn('接收到的 parsedContent 对象结构不完全符合预期:', item);
      return res.status(400).json({ error: 'parsedContent 中对象结构无效' });
    }
  }


  try {
    const user = req.user;
    const articleUuid = uuidv4();

    const originalFilenameToUse = fileName || `pdf-import-${Date.now()}.pdf`;

    let initialTitle = `导入自 ${originalFilenameToUse}`;
    if (parsedContent && parsedContent.length > 0) {
        const firstH1 = parsedContent.find(item => item.tag === 'h1');
        if (firstH1 && firstH1.children) {
            initialTitle = String(firstH1.children).substring(0, 250).trim();
        } else if (parsedContent[0] && parsedContent[0].children) {
            initialTitle = String(parsedContent[0].children).substring(0, 100).trim();
        }
    }

    let articleRecord = await Article.create({
      uuid: articleUuid,
      userId: user.uuid,
      content: parsedContent, // 原文内容直接存储在 Article 中
      // doi: null, // doi 字段已从 Article 模型中移除
      translated: [],
      title: initialTitle
    });

    // 尝试使用 AI 生成中文标题
    try {
      let textForAiTitle = parsedContent
        .filter(item => item && typeof item.children === 'string')
        .map(item => item.children)
        .join(' ')
        .substring(0, 1000); // 取前1000个字符

      if (textForAiTitle.trim()) {
        const titleSystemPrompt = `请根据以下文本内容，为其生成一个简洁、准确、地道的中文标题（不超过25个字）：\n\n${textForAiTitle}`;
        const aiPayload = {
          model: defaultApiModel,
          messages: [
            { role: 'system', content: "你是一个专业的标题生成助手，擅长从文本中提炼核心内容并生成简洁的中文标题。" },
            { role: 'user', content: titleSystemPrompt }
          ],
          temperature: 0.5,
          stream: false
        };

        const aiResponse = await fetch(defaultApiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${defaultApiKey}`,
          },
          body: JSON.stringify(aiPayload),
        });

        if (aiResponse.ok) {
          const aiData = await aiResponse.json();
          const generatedTitle = aiData.choices?.[0]?.message?.content?.trim();
          if (generatedTitle) {
            // 更新文章标题
            await articleRecord.update({ title: generatedTitle });
            initialTitle = generatedTitle; // 更新 initialTitle 以便返回给前端
          }
        }
      }
    } catch (aiError) {
      // AI生成标题失败不应阻塞主流程，继续使用提取的标题
      console.error(`[AI Title Gen] 调用AI生成标题时发生错误 (文章 ${articleUuid}):`, aiError);
    }

    res.json({
      success: true,
      newArticleUuid: articleRecord.uuid,
      // contentUuid: articleRecord.uuid, // 如果需要，可以返回 articleUuid 作为 contentUuid
      parsedContent: articleRecord.content,
      newArticleTitle: initialTitle
    });

  } catch (error) {
    console.error('处理前端导入的PDF内容失败:', error);
    res.status(500).json({ error: '保存PDF内容失败', message: error.message });
  }
});

// 为 Vercel 导出 API 处理函数
export default app;

// 只保留一个服务器启动逻辑
// Vercel will handle the listening part when deployed as a serverless function.
// Check if this module is the main module run by Node.js
if (process.argv[1] === fileURLToPath(import.meta.url)) {
    console.log('🚀 正在启动服务器...');

    // 先启动HTTP服务器
    app.listen(PORT, () => {
        console.log(`✅ Express服务器运行在 http://localhost:${PORT}`);
        if (process.env.NODE_ENV !== 'production') {
            console.log("🔧 后端在开发模式下运行，已启用热重载。");
        }
        console.log('📡 数据库连接状态可通过 /api/debug/db-status 查看');
        console.log('⚠️ 在数据库连接成功前，API请求将返回503错误');
    });

    // 设置数据库重连监听
    setupDatabaseReconnection();

    // 异步启动数据库连接（持续尝试直到成功）
    syncDatabase()
      .then(() => {
        console.log('🔄 数据库连接进程已启动，将持续尝试直到连接成功');
      })
      .catch(error => {
        console.error('❌ 数据库连接进程启动失败:', error);
        // 不退出服务器，继续运行HTTP服务
      });
}
