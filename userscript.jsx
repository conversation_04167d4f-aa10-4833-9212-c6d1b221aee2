
import {getEntries,getParsers,match2regex,content2Html,Req} from './utils/index.js';

;(await getParsers()).map(parser=>{
    ;(async ()=>{
        let isParse=false;
        console.log('检查解析器匹配:', parser.match || []);
        (parser.match||[]).map(u=>{
            const regex = new RegExp(match2regex(u));
            const isMatch = regex.test(location.href);
            console.log(`URL模式 "${u}" -> 正则 "${regex}" -> 匹配结果: ${isMatch}`);
            if(isMatch) isParse=true;
        })
        console.log('最终匹配结果:', isParse);
        if(!isParse)return;

        console.log('parser init, matches:',parser.match);
        console.log('当前页面URL:', location.href);
        console.log('解析器名称:', parser.default?.name || 'unknown');

        const {getRef,getContent,getCitation,cut}=parser;

        GM_registerMenuCommand('复制参考',async ()=>{
            console.log('开始执行复制参考功能')
            const ref=getRef()
            console.log('提取到的参考文献:', ref)
            GM_setClipboard(typeof ref=='object'?ref.join('\r\n'):ref)
            console.log('参考文献已复制到剪贴板')
        })

        GM_registerMenuCommand('复制全文',async ()=>{
            console.log('开始执行复制全文功能')
            let c=await getContent()
            console.log('获取到的原始内容数量:', c?.length || 0)

            // 应用截断逻辑
            if(cut) {
                console.log('应用截断逻辑:', cut)
                if(Array.isArray(cut) && cut.length > 0) {
                    // 数组方式：查找匹配的标题进行截断
                    const cutI = c.findIndex(item =>
                        (item.tag === 'h1'|| item.tag=='h2') &&
                        cut.some(cutWord => item.children && item.children.includes(cutWord))
                    )
                    if (cutI > 0) {
                        console.log('在位置', cutI, '处截断内容')
                        c = c.slice(0, cutI)
                    }
                } else if(typeof cut === 'function') {
                    // 函数方式：直接调用函数处理内容
                    try {
                        c = await cut(c)
                    } catch(error) {
                        console.error('截断函数执行错误:', error)
                    }
                }
            }

            console.log('最终内容数量:', c?.length || 0)
            console.log(content2Html(c),c)
            GM_setClipboard(content2Html(c))
            console.log('全文已复制到剪贴板')
        })

        if(getCitation){
            GM_registerMenuCommand('复制引用',async ()=>{
                const citation=getCitation()
                if(citation){
                    GM_setClipboard(citation)
                    console.log('Citation copied:', citation)
                } else {
                    console.log('No citation available')
                }
            })
        }

    })();
});

;(await getEntries()).map((entry)=>{
    GM_registerMenuCommand(`\u3000${entry.label} - 主入口`,async ()=>{
        console.log(await entry.login())
    })
    GM_registerMenuCommand(`\u3000${entry.label} - 跳转入口`,async ()=>{

    })
});


;(async ()=>{
    const isPreviewPage = [
        /^http:\/\/localhost:3000\/article\/[^\/]+(?:\/)?$/,
        /^https:\/\/tsgv3\.vercel\.app\/article\/[^\/]+(?:\/)?$/
    ].some(pattern => pattern.test(location.href));

    if (isPreviewPage) {
        console.log('检测到预览页面，提供图片转base64功能');

        // 暴露图片转base64功能给页面
        window.convertImagesToBase64 = async (clonedContent) => {
            const images = [...clonedContent.querySelectorAll('img')];
            const req = new Req({ engine: 'GM_xmlhttpRequest', jar: false });

            await Promise.all(images.map(async (img) => {
                const src = img.src;
                if (!src || src.startsWith('data:')) return;

                const absoluteUrl = src.startsWith('http') ? src : new URL(src, window.location.href).href;

                const response = await req(absoluteUrl, {
                    headers: {
                        'Referer': window.location.href,
                        'User-Agent': navigator.userAgent
                    }
                });

                if (response.data) {
                    img.src = response.data;
                }
            }));

            return clonedContent;
        };

    }
})();


