import sharp from 'sharp';
import fs from 'fs';

// 测试图片压缩功能
async function testImageCompression() {
  console.log('开始测试图片压缩功能...');

  try {
    // 创建一个测试图片 (1200x800 的红色矩形)
    const testImageBuffer = await sharp({
      create: {
        width: 1200,
        height: 800,
        channels: 3,
        background: { r: 255, g: 0, b: 0 }
      }
    })
    .png()
    .toBuffer();

    console.log(`原始图片大小: ${testImageBuffer.length} bytes, 尺寸: 1200x800`);

    // 测试压缩功能
    const compressedBuffer = await sharp(testImageBuffer)
      .resize(800, null, {
        withoutEnlargement: true,
        fit: 'inside'
      })
      .jpeg({ quality: 85 })
      .toBuffer();

    const compressedMetadata = await sharp(compressedBuffer).metadata();
    
    console.log(`压缩后图片大小: ${compressedBuffer.length} bytes, 尺寸: ${compressedMetadata.width}x${compressedMetadata.height}`);
    console.log(`压缩比: ${((1 - compressedBuffer.length / testImageBuffer.length) * 100).toFixed(2)}%`);

    // 测试小图片不压缩的情况
    const smallImageBuffer = await sharp({
      create: {
        width: 600,
        height: 400,
        channels: 3,
        background: { r: 0, g: 255, b: 0 }
      }
    })
    .png()
    .toBuffer();

    const smallMetadata = await sharp(smallImageBuffer).metadata();
    console.log(`小图片尺寸: ${smallMetadata.width}x${smallMetadata.height}, 大小: ${smallImageBuffer.length} bytes`);
    
    // 模拟压缩逻辑
    if (smallMetadata.width <= 800) {
      console.log('小图片宽度 <= 800px，不需要压缩');
    }

    console.log('✅ 图片压缩功能测试完成');

  } catch (error) {
    console.error('❌ 图片压缩测试失败:', error);
  }
}

// 运行测试
testImageCompression();
