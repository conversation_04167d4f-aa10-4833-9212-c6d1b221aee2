import React, { useState } from 'react';
import PDFViewer from '../components/PDFViewer';

const PDFExample = () => {
    const [pdfUrl, setPdfUrl] = useState('');
    const [showViewer, setShowViewer] = useState(false);

    // 示例 PDF URL
    const examplePDFs = [
        {
            name: '示例 PDF 1',
            url: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'
        },
        {
            name: '示例 PDF 2', 
            url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
        }
    ];

    const handleLoadPDF = () => {
        if (pdfUrl.trim()) {
            setShowViewer(true);
        }
    };

    const handleExamplePDF = (url) => {
        setPdfUrl(url);
        setShowViewer(true);
    };

    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (file && file.type === 'application/pdf') {
            const fileUrl = URL.createObjectURL(file);
            setPdfUrl(fileUrl);
            setShowViewer(true);
        } else {
            alert('请选择一个有效的 PDF 文件');
        }
    };

    return (
        <div style={{ padding: '20px' }}>
            <h1>PDF 查看器示例</h1>
            
            <div style={{ marginBottom: '20px' }}>
                <h3>方式 1: 输入 PDF URL</h3>
                <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
                    <input
                        type="text"
                        placeholder="输入 PDF 文件的 URL"
                        value={pdfUrl}
                        onChange={(e) => setPdfUrl(e.target.value)}
                        style={{ flex: 1, padding: '8px' }}
                    />
                    <button onClick={handleLoadPDF}>加载 PDF</button>
                </div>
            </div>

            <div style={{ marginBottom: '20px' }}>
                <h3>方式 2: 选择示例 PDF</h3>
                {examplePDFs.map((pdf, index) => (
                    <button
                        key={index}
                        onClick={() => handleExamplePDF(pdf.url)}
                        style={{ margin: '5px', padding: '8px 16px' }}
                    >
                        {pdf.name}
                    </button>
                ))}
            </div>

            <div style={{ marginBottom: '20px' }}>
                <h3>方式 3: 上传本地 PDF 文件</h3>
                <input
                    type="file"
                    accept=".pdf"
                    onChange={handleFileUpload}
                    style={{ padding: '8px' }}
                />
            </div>

            {showViewer && pdfUrl && (
                <div style={{ marginTop: '20px' }}>
                    <h3>PDF 预览</h3>
                    <button 
                        onClick={() => setShowViewer(false)}
                        style={{ marginBottom: '10px' }}
                    >
                        关闭预览
                    </button>
                    <PDFViewer pdfUrl={pdfUrl} width={800} height={600} />
                </div>
            )}
        </div>
    );
};

export default PDFExample;
