import React from 'react';
import ReactDOM from 'react-dom/client';
import { Auth0Provider } from '@auth0/auth0-react';
import App from './App.jsx';
import { ConfigProvider, App as AntdApp } from 'antd'; // Import AntdApp
import {AUTH0_DOMAIN,AUTH0_CLIENT_ID} from './utils/constant.js';

const redirectUri = window.location.origin;

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Auth0Provider
      domain={AUTH0_DOMAIN}
      clientId={AUTH0_CLIENT_ID}
      authorizationParams={{
        redirect_uri: redirectUri,
        audience: `https://${AUTH0_DOMAIN}/api/v2/`,
        scope: 'openid profile email'
      }}
    >
      <ConfigProvider theme={{ token: { motion: false } }}>
        <AntdApp> {/* Wrap with AntdApp */}
          <App />
          <style jsx="true" global="true">{`
            html, body {
              height: 100%;
              margin: 0;
              padding: 0;
              overflow-x: hidden;
            }

            body {
              overflow-y: auto;
            }
          `}</style>
        </AntdApp>
      </ConfigProvider>
    </Auth0Provider>
  </React.StrictMode>,
);
