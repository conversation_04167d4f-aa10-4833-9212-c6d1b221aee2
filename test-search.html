<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索框定位测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        
        .container {
            position: relative;
            width: 600px;
            height: 800px; /* 增加高度来测试高 textarea */
            border: 2px solid #ccc;
            margin: 50px auto;
            background: #f9f9f9;
        }

        .textarea-mock {
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 8px;
            box-sizing: border-box;
            resize: none;
            font-size: 12px;
            line-height: 1.6;
        }
        
        .search-panel {
            position: absolute;
            top: 8px; /* 这个会被 JavaScript 动态更新 */
            right: 8px;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 280px;
            font-size: 12px;
        }
        
        .search-input {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            box-sizing: border-box;
        }
        
        .test-info {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: #f0f0f0;
            border-radius: 8px;
        }
        
        .scroll-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            margin: 20px 0;
        }
        
        .scroll-content {
            height: 800px;
            padding: 20px;
            background: linear-gradient(to bottom, #fff, #f0f0f0);
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>搜索框定位测试</h1>
        <p>这个页面用于测试搜索框是否正确定位在 textarea 可视区域的右上角。</p>
        <p><strong>期望行为：</strong></p>
        <ul>
            <li>搜索框应该显示在灰色容器的右上角</li>
            <li>距离容器边缘 8px</li>
            <li>当页面滚动时，搜索框应该跟随容器移动</li>
        </ul>
    </div>

    <div class="container">
        <textarea class="textarea-mock" placeholder="这是一个模拟的 textarea，用于测试搜索框定位...">这是一个测试文档，用于验证搜索功能。

在这个文档中，我们有很多不同的内容：
- 第一段包含一些基本信息
- 第二段包含更多详细信息
- 第三段包含总结信息

搜索功能应该能够：
1. 通过 Ctrl+F (或 Command+F) 打开搜索面板
2. 在右上角显示悬浮的搜索框
3. 支持大小写敏感搜索
4. 支持正则表达式搜索
5. 显示匹配数量和当前位置
6. 支持上一个/下一个导航

测试关键词：
- 搜索
- 功能
- 测试
- 信息
- 内容

这个文档足够长，可以测试滚动时搜索面板的位置是否正确。
当用户滚动页面时，搜索面板应该始终相对于 textarea 保持在右上角位置。

让我们添加更多内容来测试高 textarea 的情况：
Lorem ipsum dolor sit amet, consectetur adipiscing elit.
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.

中文测试内容：
这里有一些中文内容用于测试中文搜索功能。
搜索功能应该能够正确处理中文字符。
包括标点符号、数字123和英文mixed content。

更多内容填充：
第一段：这是第一段内容，用于测试搜索功能的准确性。
第二段：这是第二段内容，包含了更多的测试数据。
第三段：这是第三段内容，继续测试搜索的各种情况。
第四段：这是第四段内容，验证搜索框在长文本中的表现。
第五段：这是第五段内容，确保搜索功能在各种情况下都能正常工作。

技术细节测试：
- React 组件渲染
- CodeMirror 编辑器集成
- 搜索高亮功能
- 正则表达式支持
- 大小写敏感搜索
- 键盘快捷键支持

性能测试内容：
当文档变得很长时，搜索功能应该仍然保持良好的性能。
这包括搜索速度、高亮渲染速度、以及搜索框定位的准确性。

最后一段：
这是文档的最后一段，用于测试搜索到文档末尾的情况。
搜索功能应该能够循环搜索，从末尾回到开头。
当 textarea 很高时，搜索框应该始终显示在可视区域的右上角。</textarea>
        
        <div class="search-panel">
            <input type="text" class="search-input" placeholder="搜索..." />
            <div style="margin-top: 4px; font-size: 11px; color: #666;">
                搜索框应该在这个位置 (容器右上角)
            </div>
        </div>
    </div>

    <div class="test-info">
        <h2>滚动测试</h2>
        <p>滚动下面的内容，观察上方搜索框是否保持在正确位置：</p>
        
        <div class="scroll-container">
            <div class="scroll-content">
                <p>这是一个可滚动的区域...</p>
                <p>继续滚动...</p>
                <p>搜索框应该始终保持在容器的右上角...</p>
                <p>不管页面如何滚动...</p>
                <p>搜索框的位置都应该相对于容器保持不变...</p>
                <br><br><br><br><br><br><br><br><br><br>
                <p>继续滚动测试...</p>
                <br><br><br><br><br><br><br><br><br><br>
                <p>滚动到底部了</p>
            </div>
        </div>
    </div>

    <script>
        // 搜索框定位测试脚本
        console.log('搜索框定位测试页面已加载');

        function updateSearchPanelPosition() {
            const container = document.querySelector('.container');
            const textarea = document.querySelector('.textarea-mock');
            const searchPanel = document.querySelector('.search-panel');

            if (!container || !textarea || !searchPanel) return;

            // 获取各种边界矩形
            const containerRect = container.getBoundingClientRect();
            const textareaRect = textarea.getBoundingClientRect();
            const viewportHeight = window.innerHeight;

            // 计算 textarea 在视口中的可见区域
            const visibleTop = Math.max(textareaRect.top, 0);
            const visibleBottom = Math.min(textareaRect.bottom, viewportHeight);

            // 如果 textarea 在视口中不可见，隐藏搜索框
            if (visibleTop >= visibleBottom) {
                searchPanel.style.display = 'none';
                return;
            }

            // 显示搜索框
            searchPanel.style.display = 'block';

            // 计算搜索框应该显示的位置：在可视区域的右上角
            const relativeTop = Math.max(8, visibleTop - containerRect.top + 8);

            // 更新搜索框位置
            searchPanel.style.top = relativeTop + 'px';

            console.log('搜索框位置更新:', {
                visibleTop,
                visibleBottom,
                relativeTop,
                containerTop: containerRect.top,
                textareaTop: textareaRect.top
            });
        }

        // 初始定位
        updateSearchPanelPosition();

        // 监听滚动事件
        window.addEventListener('scroll', () => {
            console.log('页面滚动位置:', window.scrollY);
            updateSearchPanelPosition();
        });

        // 监听窗口大小变化
        window.addEventListener('resize', updateSearchPanelPosition);
    </script>
</body>
</html>
